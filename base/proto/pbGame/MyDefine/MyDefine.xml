<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbGame/MyDefine">
    <enum name="Const" explain="通用定义" allowAlias="false">
        <type name="None" number="0" explain="自动添加"/>
        <type name="CAN_DELETE" number="4" explain="占位"/>
        <type name="BACK_ERROR_DUR" number="-400" explain=""/>
        <type name="BACK_ERROR_NULL_HAND" number="-401" explain=""/>
    </enum>
    <enum name="POWER" explain="属性能力定义" allowAlias="false">
        <type name="POWER_NONE" number="0" explain="未知"/>
        <type name="POWER_STR" number="1" explain="力量"/>
        <type name="POWER_STR_PERCENT" number="2" explain="力量百分比"/>
        <type name="POWER_CON" number="3" explain="体质"/>
        <type name="POWER_CON_PERCENT" number="4" explain="体质百分比"/>
        <type name="POWER_AGI" number="5" explain="敏捷"/>
        <type name="POWER_AGI_PERCENT" number="6" explain="敏捷百分比"/>
        <type name="POWER_ILT" number="7" explain="智力"/>
        <type name="POWER_ILT_PERCENT" number="8" explain="智力百分比"/>
        <type name="POWER_WIS" number="9" explain="感知"/>
        <type name="POWER_WIS_PERCENT" number="10" explain="感知百分比"/>
        <type name="POWER_HPMAX" number="11" explain=""/>
        <type name="POWER_HPMAX_PERCENT" number="12" explain=""/>
        <type name="POWER_MPMAX" number="13" explain=""/>
        <type name="POWER_MPMAX_PERCENT" number="14" explain=""/>
        <type name="POWER_SPEED_PERCENT" number="23" explain=""/>
        <type name="POWER_HITRATE_PERCENT" number="24" explain=""/>
        <type name="POWER_DODGE_PERCENT" number="25" explain=""/>
        <type name="POWER_MAGIC_HITRATE_PERCENT" number="26" explain=""/>
        <type name="POWER_CRITICAL_PERCENT" number="27" explain=""/>
        <type name="POWER_ATK_STR_PERCENT" number="28" explain=""/>
        <type name="POWER_ATK_AGI_PERCENT" number="29" explain=""/>
        <type name="POWER_ATK_MAGIC_PERCENT" number="30" explain=""/>
        <type name="POWER_DEF_STR_PERCENT" number="31" explain=""/>
        <type name="POWER_DEF_AGI_PERCENT" number="32" explain=""/>
        <type name="POWER_DEF_MAGIC_PERCENT" number="33" explain=""/>
        <type name="POWER_WIL_PERCENT" number="34" explain=""/>
        <type name="POWER_TOUGH_PERCENT" number="35" explain=""/>
        <type name="POWER_REFLECTION_PERCENT" number="36" explain=""/>
        <type name="POWER_BLOCK_PERCENT" number="37" explain=""/>
        <type name="POWER_INSIGHT_PERCENT" number="38" explain=""/>
        <type name="POWER_PENETRATION_PERCENT" number="39" explain=""/>
        <type name="POWER_DEF_FIELD_PERCENT" number="40" explain=""/>
        <type name="POWER_BACK_PERCENT" number="41" explain=""/>
        <type name="POWER_MAGIC_BACK_PERCENT" number="42" explain=""/>
        <type name="POWER_LIFE_ABSORPTION_PERCENT" number="43" explain=""/>
        <type name="POWER_MANA_ABSORPTION_PERCENT" number="44" explain=""/>
        <type name="POWER_MAGIC_PENETRATION_PERCENT" number="45" explain=""/>
        <type name="POWER_HIT_FORCE_PERCENT" number="46" explain=""/>
        <type name="POWER_HEAL_RECOVERY_PERCENT" number="47" explain=""/>
        <type name="POWER_MANA_RECOVERY_PERCENT" number="48" explain=""/>
        <type name="POWER_HP" number="49" explain=""/>
        <type name="POWER_HP_PERCENT" number="50" explain=""/>
        <type name="POWER_MP" number="51" explain=""/>
        <type name="POWER_MP_PERCENT" number="52" explain=""/>
        <type name="POWER_SPEED" number="53" explain=""/>
        <type name="POWER_HITRATE" number="54" explain=""/>
        <type name="POWER_DODGE" number="55" explain=""/>
        <type name="POWER_MAGIC_HITRATE" number="56" explain=""/>
        <type name="POWER_CRITICAL" number="57" explain=""/>
        <type name="POWER_ATK_STR" number="58" explain=""/>
        <type name="POWER_ATK_AGI" number="59" explain=""/>
        <type name="POWER_ATK_MAGIC" number="60" explain=""/>
        <type name="POWER_DEF_STR" number="61" explain=""/>
        <type name="POWER_DEF_AGI" number="62" explain=""/>
        <type name="POWER_DEF_MAGIC" number="63" explain=""/>
        <type name="POWER_WIL" number="64" explain=""/>
        <type name="POWER_TOUGH" number="65" explain=""/>
        <type name="POWER_REFLECTION" number="66" explain=""/>
        <type name="POWER_BLOCK" number="67" explain=""/>
        <type name="POWER_INSIGHT" number="68" explain=""/>
        <type name="POWER_PENETRATION" number="69" explain=""/>
        <type name="POWER_DEF_FIELD" number="70" explain=""/>
        <type name="POWER_BACK" number="71" explain=""/>
        <type name="POWER_MAGIC_BACK" number="72" explain=""/>
        <type name="POWER_LIFE_ABSORPTION" number="73" explain=""/>
        <type name="POWER_MANA_ABSORPTION" number="74" explain=""/>
        <type name="POWER_MAGIC_PENETRATION" number="75" explain=""/>
        <type name="POWER_HIT_FORCE" number="76" explain=""/>
        <type name="POWER_HEAL_RECOVERY" number="77" explain=""/>
        <type name="POWER_MANA_RECOVERY" number="78" explain=""/>
        <type name="POWER_REMOVE_STATUS" number="79" explain=""/>
        <type name="POWER_RECOVER" number="80" explain=""/>
        <type name="POWER_SKILL_DAMAGE" number="81" explain=""/>
        <type name="POWER_SKILL_HITRATE" number="82" explain=""/>
        <type name="POWER_SELF_CRITICAL" number="84" explain=""/>
        <type name="POWER_SKILL_HIT_FORCE" number="85" explain=""/>
        <type name="POWER_SKILL_MAGIC_PENETRATION" number="86" explain=""/>
        <type name="POWER_SKILL_BRK_ARMOR" number="87" explain=""/>
        <type name="POWER_SKILL_REMOVE_STATUS" number="88" explain=""/>
        <type name="POWER_PET_DAMAGE" number="89" explain=""/>
        <type name="POWER_PET_HPMAX_PERCENT" number="90" explain=""/>
        <type name="POWER_PET_MPMAX_PERCENT" number="91" explain=""/>
        <type name="POWER_PET_STR_PERCENT" number="92" explain=""/>
        <type name="POWER_PET_CON_PERCENT" number="93" explain=""/>
        <type name="POWER_PET_AGI_PERCENT" number="94" explain=""/>
        <type name="POWER_PET_ILT_PERCENT" number="95" explain=""/>
        <type name="POWER_PET_WIS_PERCENT" number="96" explain=""/>
        <type name="POWER_RECOVER_PERCENT" number="97" explain=""/>
        <type name="POWER_HPMP_RECOVER" number="98" explain=""/>
        <type name="POWER_OPEN_STORE" number="99" explain=""/>
        <type name="POWER_CHEST_LV1" number="100" explain=""/>
        <type name="POWER_CHEST_LV2" number="101" explain=""/>
        <type name="POWER_CHEST_LV3" number="102" explain=""/>
        <type name="POWER_REQ_SLOT" number="103" explain=""/>
        <type name="POWER_TO_WORLDMAP" number="104" explain=""/>
        <type name="POWER_TO_GXGY" number="105" explain=""/>
        <type name="POWER_EXPIRE_TIME" number="106" explain=""/>
        <type name="POWER_RESET_MISSION" number="107" explain=""/>
        <type name="POWER_CHEST_KEY_LEVEL" number="108" explain=""/>
        <type name="POWER_PET_EGG" number="109" explain=""/>
        <type name="POWER_COSTUME" number="110" explain=""/>
        <type name="POWER_TRANSPORT" number="111" explain=""/>
        <type name="POWER_POWER_TITLE" number="112" explain=""/>
        <type name="POWER_GUARD_STR_ATTACK" number="113" explain=""/>
        <type name="POWER_GUARD_AGI_ATTACK" number="114" explain=""/>
        <type name="POWER_GUARD_MAGIC_ATTACK" number="115" explain=""/>
        <type name="POWER_GUARD_CURSE_ATTACK" number="116" explain=""/>
        <type name="POWER_GUARD_ALL_ATTACK" number="117" explain=""/>
        <type name="POWER_PET_ADD_EXP" number="118" explain=""/>
        <type name="POWER_ADD_EXP" number="119" explain=""/>
        <type name="POWER_EXP_BY_TIME" number="120" explain=""/>
        <type name="POWER_IDENTIFY" number="121" explain=""/>
        <type name="POWER_SWORD_ATK_TIME" number="122" explain=""/>
        <type name="POWER_BLADE_ATK_TIME" number="123" explain=""/>
        <type name="POWER_HEAVY_ATK_TIME" number="124" explain=""/>
        <type name="POWER_LANCE_ATK_TIME" number="125" explain=""/>
        <type name="POWER_STAFF_ATK_TIME" number="126" explain=""/>
        <type name="POWER_HAND_ATK_TIME" number="127" explain=""/>
        <type name="POWER_BOW_ATK_TIME" number="128" explain=""/>
        <type name="POWER_HAND_ITEM_ATK_TIME" number="129" explain=""/>
        <type name="POWER_ALL_ATK_TIME" number="130" explain=""/>
        <type name="POWER_COMPOSITE" number="131" explain=""/>
        <type name="POWER_SWORD_PERCENT" number="133" explain=""/>
        <type name="POWER_BLADE_PERCENT" number="134" explain=""/>
        <type name="POWER_HEAVY_PERCENT" number="135" explain=""/>
        <type name="POWER_LANCE_PERCENT" number="136" explain=""/>
        <type name="POWER_STAFF_PERCENT" number="137" explain=""/>
        <type name="POWER_HAND_PERCENT" number="138" explain=""/>
        <type name="POWER_BOW_PERCENT" number="139" explain=""/>
        <type name="POWER_HAND_ITEM_PERCENT" number="140" explain=""/>
        <type name="POWER_ALL_PERCENT" number="141" explain=""/>
        <type name="POWER_EQUIP_ARMOR_DUR_PERCENT" number="142" explain=""/>
        <type name="POWER_SKILL_HP" number="145" explain=""/>
        <type name="POWER_SKILL_HP_PERCENT" number="146" explain=""/>
        <type name="POWER_SKILL_MP" number="147" explain=""/>
        <type name="POWER_SKILL_MP_PERCENT" number="148" explain=""/>
        <type name="POWER_SKILL_LIFE_ABSORPTION" number="149" explain=""/>
        <type name="POWER_SKILL_MANA_ABSORPTION" number="150" explain=""/>
        <type name="POWER_SKILL_TARGET_BACK" number="151" explain=""/>
        <type name="POWER_SKILL_TARGET_MAGIC_BACK" number="152" explain=""/>
        <type name="POWER_SKILL_TARGET_BLOCK" number="153" explain=""/>
        <type name="POWER_SKILL_TARGET_INSIGHT" number="154" explain=""/>
        <type name="POWER_SKILL_TARGET_WIL" number="155" explain=""/>
        <type name="POWER_SKILL_TARGET_TOUCH" number="156" explain=""/>
        <type name="POWER_GRARD_MASTER_STR_ATTACK" number="157" explain=""/>
        <type name="POWER_GRARD_MASTER_AGI_ATTACK" number="158" explain=""/>
        <type name="POWER_GRARD_MASTER_MAGIC_ATTACK" number="159" explain=""/>
        <type name="POWER_GRARD_MASTER_CURSE_ATTACK" number="160" explain=""/>
        <type name="POWER_GRARD_MASTER_ALL_ATTACK" number="161" explain=""/>
        <type name="POWER_PET_GRARD_STR_ATTACK" number="162" explain=""/>
        <type name="POWER_PET_GRARD_AGI_ATTACK" number="163" explain=""/>
        <type name="POWER_PET_GRARD_MAGIC_ATTACK" number="164" explain=""/>
        <type name="POWER_PET_GRARD_CURSE_ATTACK" number="165" explain=""/>
        <type name="POWER_PET_GRARD_ALL_ATTACK" number="166" explain=""/>
        <type name="POWER_EXP_MISSION_BY_TIME" number="167" explain=""/>
        <type name="POWER_IGNORE_BACK" number="168" explain=""/>
        <type name="POWER_IGNORE_MAGIC_BACK" number="169" explain=""/>
        <type name="POWER_IGNORE_BLOCK" number="170" explain=""/>
        <type name="POWER_IGNORE_INSIGHT" number="171" explain=""/>
        <type name="POWER_IGNORE_WIL" number="172" explain=""/>
        <type name="POWER_IGNORE_TOUCH" number="173" explain=""/>
        <type name="POWER_BALL_ATK_TIME" number="174" explain=""/>
        <type name="POWER_BALL_PERCENT" number="175" explain=""/>
        <type name="POWER_SKILL_SCROLL" number="176" explain=""/>
        <type name="POWER_SKILL_SCROLL_PET" number="177" explain=""/>
        <type name="POWER_KEEPOUT_ATK_TIME" number="178" explain=""/>
        <type name="POWER_NEW_GET_PET" number="179" explain=""/>
        <type name="POWER_NEW_GET_ITEM" number="180" explain=""/>
        <type name="POWER_ENCHANT_ITEM" number="181" explain=""/>
        <type name="POWER_FORMATION_BOOK" number="182" explain=""/>
        <type name="POWER_CHEST_LV4" number="183" explain=""/>
        <type name="POWER_COLOR_BOX" number="184" explain=""/>
        <type name="POWER_TURN_MONSTER_CARD" number="185" explain=""/>
        <type name="POWER_SKILL_BOOK_PET" number="186" explain=""/>
        <type name="POWER_GUN_ATK_TIME" number="190" explain=""/>
        <type name="POWER_GUN_PERCENT" number="191" explain=""/>
        <type name="POWER_HAMMER_ATK_TIME" number="192" explain=""/>
        <type name="POWER_HAMMER_PERCENT" number="193" explain=""/>
        <type name="POWER_FAN_ATK_TIME" number="194" explain=""/>
        <type name="POWER_FAN_PERCENT" number="195" explain=""/>
        <type name="POWER_MAGIC_PERCENT" number="196" explain=""/>
        <type name="POWER_PHYSICS_PERCENT" number="197" explain=""/>
        <type name="POWER_HP_MP" number="198" explain=""/>
        <type name="POWER_IGNORE_CRITICAL" number="201" explain=""/>
        <type name="POWER_IGNORE_CRITICAL_PERCENT" number="202" explain=""/>
        <type name="POWER_CRITICAL_DAMAGE" number="203" explain=""/>
        <type name="POWER_CRITICAL_DAMAGE_PERCENT" number="204" explain=""/>
        <type name="POWER_BLADE_L_ATK_TIME" number="210" explain=""/>
        <type name="POWER_BLADE_H_ATK_TIME" number="211" explain=""/>
        <type name="POWER_SWORD_L_ATK_TIME" number="212" explain=""/>
        <type name="POWER_SWORD_H_ATK_TIME" number="213" explain=""/>
        <type name="POWER_CROSSBOW_L_ATK_TIME" number="214" explain=""/>
        <type name="POWER_CROSSBOW_H_ATK_TIME" number="215" explain=""/>
        <type name="POWER_ARROW_ATK_TIME" number="216" explain=""/>
        <type name="POWER_BLADE_L_DAMAGE_PERCENT" number="230" explain=""/>
        <type name="POWER_BLADE_H_DAMAGE_PERCENT" number="231" explain=""/>
        <type name="POWER_SWORD_L_DAMAGE_PERCENT" number="232" explain=""/>
        <type name="POWER_SWORD_H_DAMAGE_PERCENT" number="233" explain=""/>
        <type name="POWER_CROSSBOW_L_DAMAGE_PERCENT" number="234" explain=""/>
        <type name="POWER_CROSSBOW_H_DAMAGE_PERCENT" number="235" explain=""/>
        <type name="POWER_ARROW_DAMAGE_PERCENT" number="236" explain=""/>
        <type name="POWER_DEF_STR_RANGE" number="250" explain=""/>
        <type name="POWER_DEF_STR_RANGE_PERCENT" number="251" explain=""/>
        <type name="POWER_DEF_STR_NEARBY" number="252" explain=""/>
        <type name="POWER_DEF_STR_NEARBY_PERCENT" number="253" explain=""/>
        <type name="POWER_DEF_AGI_RANGE" number="254" explain=""/>
        <type name="POWER_DEF_AGI_RANGE_PERCENT" number="255" explain=""/>
        <type name="POWER_DEF_AGI_NEARBY" number="256" explain=""/>
        <type name="POWER_DEF_AGI_NEARBY_PERCENT" number="257" explain=""/>
        <type name="POWER_ATK_STR_NEARBY" number="258" explain=""/>
        <type name="POWER_ATK_STR_NEARBY_PERCENT" number="259" explain=""/>
        <type name="POWER_ATK_STR_RANGE" number="260" explain=""/>
        <type name="POWER_ATK_STR_RANGE_PERCENT" number="261" explain=""/>
        <type name="POWER_ATK_AGI_NEARBY" number="262" explain=""/>
        <type name="POWER_ATK_AGI_NEARBY_PERCENT" number="263" explain=""/>
        <type name="POWER_ATK_AGI_RANGE" number="264" explain=""/>
        <type name="POWER_ATK_AGI_RANGE_PERCENT" number="265" explain=""/>
    </enum>
</messages>