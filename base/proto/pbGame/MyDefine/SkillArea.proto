syntax = "proto3";

package proto.MyDefine;

option go_package = "world/common/pbGame/MyDefine;MyDefine";

//After are enums.
// 技能范围定义
// @go-enum-no-prefix
enum SKILL_AREA{
  SINGLE = 0; //单体
  FRONT_BACK_TWO = 1; //前后两人
  UP_DOWN_TWO = 2; //上下两人
  UP_DOWN_THREE = 3; //上下三人
  UP_DOWN_FOUR = 4; //上下四人
  UP_DOWN_FIVE = 5; //上下五人
  TEN = 6; //十字
  SQUARE = 7; //正方
  AROUND_SIX = 8; //六人长方
  ALL = 9; //全体
  ENEMY_SINGLE = 10; //敌单体
  ENEMY_FONT_BACK_TWO = 11; //敌前后两人
  ENEMY_UP_DOWN_TWO = 12; //敌上下两人
  ENEMY_UP_DOWN_THREE = 13; //敌上下三人
  ENEMY_UP_DOWN_FOUR = 14; //敌上下四人
  ENEMY_UP_DOWN_FIVE = 15; //敌上下五人
  ENEMY_TEN = 16; //敌十字
  ENEMY_SQUARE = 17; //敌正方
  ENEMY_AROUND_SIX = 18; //敌六人长方
  ENEMY_ALL = 19; //敌全体
  ME_SINGLE = 20; //我单体
  ME_FONT_BACK_TWO = 21; //我前后两人
  ME_UP_DOWN_TWO = 22; //我上下两人
  ME_UP_DOWN_THREE = 23; //我上下三人
  ME_UP_DOWN_FOUR = 24; //我上下四人
  ME_UP_DOWN_FIVE = 25; //我上下五人
  ME_TEN = 26; //我十字
  ME_SQUARE = 27; //我正方
  ME_AROUND_SIX = 28; //我六人长方
  ME_ALL = 29; //我全体
  MY_SELF = 30; //自身
  ENEMY_HP_LEAST = 31; //敌当前生命最少者
  ENEMY_HP_MOST = 32; //敌当前生命最大者
  ME_HP_LEAST = 33; //我当前生命最少者
  ME_HP_MOST = 34; //我当前生命最大者
  MY_OWNER = 35; //自身主人
  ME_ALL_NO_SELF = 36; //我全体(不包括自身)
  ALL_NO_SELF = 37; //全体(不包括自身)
  PLAYER_AND_PET = 38; //宠物和主人
}

// 技能范围定义
// @go-enum-no-prefix
enum SKILL_AREA_SEARCH{
  SEARCH_ALL = 0; //搜索全体目标
  SEARCH_ENEMY = 1; //搜索敌方
  SEARCH_FRIEND = 2; //搜索友方
  SEARCH_MY_SELF = 3; //搜索自身
  SEARCH_MY_OWNER = 4; //搜索自身主人
}

// 技能范围定义
// @go-enum-no-prefix
enum SKILL_AREA_CURSOR{
  CURSOR_NONE = 0; //
  CURSOR_ALL = -1; //
  CURSOR_1 = 1; //
  CURSOR_2 = 2; //
  CURSOR_3 = 3; //
  CURSOR_4 = 4; //
  CURSOR_5 = 5; //
  CURSOR_6 = 6; //
}

