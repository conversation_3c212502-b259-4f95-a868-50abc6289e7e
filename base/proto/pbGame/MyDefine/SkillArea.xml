<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbGame/MyDefine">
    <enum name="SKILL_AREA" explain="技能范围定义" allowAlias="false">
        <type name="SINGLE" number="0" explain="单体"/>
        <type name="FRONT_BACK_TWO" number="1" explain="前后两人"/>
        <type name="UP_DOWN_TWO" number="2" explain="上下两人"/>
        <type name="UP_DOWN_THREE" number="3" explain="上下三人"/>
        <type name="UP_DOWN_FOUR" number="4" explain="上下四人"/>
        <type name="UP_DOWN_FIVE" number="5" explain="上下五人"/>
        <type name="TEN" number="6" explain="十字"/>
        <type name="SQUARE" number="7" explain="正方"/>
        <type name="AROUND_SIX" number="8" explain="六人长方"/>
        <type name="ALL" number="9" explain="全体"/>
        <type name="ENEMY_SINGLE" number="10" explain="敌单体"/>
        <type name="ENEMY_FONT_BACK_TWO" number="11" explain="敌前后两人"/>
        <type name="ENEMY_UP_DOWN_TWO" number="12" explain="敌上下两人"/>
        <type name="ENEMY_UP_DOWN_THREE" number="13" explain="敌上下三人"/>
        <type name="ENEMY_UP_DOWN_FOUR" number="14" explain="敌上下四人"/>
        <type name="ENEMY_UP_DOWN_FIVE" number="15" explain="敌上下五人"/>
        <type name="ENEMY_TEN" number="16" explain="敌十字"/>
        <type name="ENEMY_SQUARE" number="17" explain="敌正方"/>
        <type name="ENEMY_AROUND_SIX" number="18" explain="敌六人长方"/>
        <type name="ENEMY_ALL" number="19" explain="敌全体"/>
        <type name="ME_SINGLE" number="20" explain="我单体"/>
        <type name="ME_FONT_BACK_TWO" number="21" explain="我前后两人"/>
        <type name="ME_UP_DOWN_TWO" number="22" explain="我上下两人"/>
        <type name="ME_UP_DOWN_THREE" number="23" explain="我上下三人"/>
        <type name="ME_UP_DOWN_FOUR" number="24" explain="我上下四人"/>
        <type name="ME_UP_DOWN_FIVE" number="25" explain="我上下五人"/>
        <type name="ME_TEN" number="26" explain="我十字"/>
        <type name="ME_SQUARE" number="27" explain="我正方"/>
        <type name="ME_AROUND_SIX" number="28" explain="我六人长方"/>
        <type name="ME_ALL" number="29" explain="我全体"/>
        <type name="MY_SELF" number="30" explain="自身"/>
        <type name="ENEMY_HP_LEAST" number="31" explain="敌当前生命最少者"/>
        <type name="ENEMY_HP_MOST" number="32" explain="敌当前生命最大者"/>
        <type name="ME_HP_LEAST" number="33" explain="我当前生命最少者"/>
        <type name="ME_HP_MOST" number="34" explain="我当前生命最大者"/>
        <type name="MY_OWNER" number="35" explain="自身主人"/>
        <type name="ME_ALL_NO_SELF" number="36" explain="我全体(不包括自身)"/>
        <type name="ALL_NO_SELF" number="37" explain="全体(不包括自身)"/>
        <type name="PLAYER_AND_PET" number="38" explain="宠物和主人"/>
    </enum>
    <enum name="SKILL_AREA_SEARCH" explain="技能范围定义" allowAlias="false">
        <type name="SEARCH_ALL" number="0" explain="搜索全体目标"/>
        <type name="SEARCH_ENEMY" number="1" explain="搜索敌方"/>
        <type name="SEARCH_FRIEND" number="2" explain="搜索友方"/>
        <type name="SEARCH_MY_SELF" number="3" explain="搜索自身"/>
        <type name="SEARCH_MY_OWNER" number="4" explain="搜索自身主人"/>
    </enum>
    <enum name="SKILL_AREA_CURSOR" explain="技能范围定义" allowAlias="false">
        <type name="CURSOR_NONE" number="0" explain=""/>
        <type name="CURSOR_ALL" number="-1" explain=""/>
        <type name="CURSOR_1" number="1" explain=""/>
        <type name="CURSOR_2" number="2" explain=""/>
        <type name="CURSOR_3" number="3" explain=""/>
        <type name="CURSOR_4" number="4" explain=""/>
        <type name="CURSOR_5" number="5" explain=""/>
        <type name="CURSOR_6" number="6" explain=""/>
    </enum>
</messages>