syntax = "proto3";

package proto.ITEM_ID;

option go_package = "world/common/pbGame/ITEM_ID;ITEM_ID";

//After are enums.
// 物品id定义
// @go-enum-no-prefix
enum Type{
  ID_NONE = 0; //无效物品
  PET = 24; //宠物
  WOOD = 1000; //木头
  STONE = 1001; //石头
  IRON = 1002; //铁
  IDENTIFY_SCROLL = 40000; //鉴定卷轴
  IDENTIFY_SCROLL_BIND = 40001; //鉴定卷轴(绑定)
  COMMAND_BOOK = 40002; //指令书
  PET_RESET = 40004; //宠物洗髓石
  PET_AGE = 40005; //宠物返老还童石
  REPAIR = 40008; //野外修理卷
  PET_RESET_2 = 40015; //宠物重生石
  CHANGE_NAME = 40016; //改名卷
  SPEAK = 40017; //小喇叭
  PET_ADD_SKILL = 40020; //宠物潜能石
  STAR_SCROLL = 40021; //装备强化卷
  CHANGE_SEX = 42000; //变性卷轴
  CP_POINT_ADD = 42001; //属性点道具
  SP_POINT_ADD = 42002; //技能点道具
  PROSPERITY_DEGREE_POINT_ADD = 42003; //繁荣度
  SKILL_PLAYER = 42004; //人物技能槽
  SKILL_PET = 42005; //宠物技能卷槽
  SKILL_PLAYER_2 = 42006; //人物技能卷槽(绑)
  SKILL_PET_2 = 42007; //宠物技能卷槽(绑)
  HIGH_IDENTIFY_SCROLL = 40022; //高级鉴定卷轴
  HIGH_IDENTIFY_SCROLL_BIND = 40023; //高级鉴定卷轴(绑)
  UPGRADE_IDENTIFY_SCROLL = 40024; //进阶鉴定卷轴
  UPGRADE_IDENTIFY_SCROLL_BIND = 40025; //进阶鉴定卷轴(绑)
  UPGRADE_INTENSIFY_SCROLL = 40026; //进阶强化卷轴
  UPGRADE_INTENSIFY_SCROLL_BIND = 40027; //进阶强化卷轴(绑)
  PET_EXPERIENCE_BOOK = 40047; //宠物经验卷
  PET_EXPERIENCE_BOOK2 = 40043; //宠物经验卷
  PET_EXPERIENCE_BOOK3 = 40006; //宠物经验卷
  SPEAK2 = 42016; //跨服小喇叭
  SKILL_PLAYER_3 = 42017; //中级人物技能槽
  SKILL_PET_3 = 42018; //中级宠物技能槽(绑)
  ADD_BAG_SIZE = 288; //10格背包扩展
  HAIR_START = 40100; //
  HAIR_END = 40199; //
  FACE_START = 40200; //
  FACE_END = 40249; //
  RANGER = 41100; //转职书-侠客
  XIUZHEN = 41101; //转职书-修真
  WARRIOR = 41102; //转职书-战士
  WIZARD = 41103; //转职书-法师
  NEW = 41104; //转职书-贤者
  BACKUP2 = 41105; //转职书-武圣
  BACKUP3 = 41106; //转职书-枪王
  BACKUP4 = 41107; //转职书-锤师
  BACKUP5 = 41108; //转职书-岚舞
  BACKUP6 = 41109; //转职书-备用
  BACKUP7 = 41110; //转职书-备用
}

