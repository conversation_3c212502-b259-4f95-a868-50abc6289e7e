<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbGame">
    <message type="S2C" name="EnterMap" explain="进入地图">
        <field class="pbCross/struct.CrossSimplePlayer" name="plr" explain="玩家简要数据"/>
    </message>
    <message type="S2C" name="LeaveMap" explain="离开地图">
        <field class="int32" name="id" explain="玩家id"/>
    </message>
    <message type="S2C" name="OtherMove" explain="其他玩家移动或者模型改变">
        <field class="int32" name="id" explain="玩家id"/>
        <field class="int32" name="x" explain="x"/>
        <field class="int32" name="y" explain="y"/>
        <field class="int32" name="mode" explain="mode"/>
    </message>
    <message type="C2S" name="TeamJoin" module="game" explain="加入队伍">
        <field class="int32" name="otherId" explain="其他玩家id"/>
    </message>
    <message type="S2C" name="TeamJoin" explain="加入队伍">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
    <message type="C2S" name="TeamInvite" module="game" explain="邀请加入队伍">
        <field class="int32" name="otherId" explain="其他玩家id"/>
    </message>
    <message type="S2C" name="TeamInvite" explain="邀请加入队伍">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
    <message type="C2S" name="JumpMap" module="game" explain="跳转地图">
        <field class="int32" name="toMapId" explain="目标地图id"/>
        <field class="int32" name="toMapX" explain="x"/>
        <field class="int32" name="toMapY" explain="y"/>
    </message>
    <message type="S2C" name="JumpMap" explain="跳转地图">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
    <message type="C2S" name="PlayerMove" module="game" explain="玩家移动">
        <field class="pbGame/struct.Point" name="pos" explain="位置信息"/>
    </message>
    <message type="S2C" name="PlayerMove" explain="玩家移动">
        <field class="pbBase/Response/Response.Code" name="code" explain=""/>
    </message>
    <message type="S2C" name="ScenePlayerEvent" explain="场景玩家事件,某个玩家对另一个玩家发起组队邀请等事件">
        <field class="pbCross/struct.PlayerEvent" name="event" explain=""/>
    </message>
    <message type="C2S" name="PlayerEventChoose" module="game" explain="玩家对于场景事件的选择">
        <field class="int32" name="id" explain="事件id"/>
        <field class="bool" name="yes" explain="是/否"/>
    </message>
    <message type="S2C" name="PlayerEventChoose" explain="玩家对于场景事件的选择处理响应">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
    <message type="S2C" name="GetPlayerEventChooseResult" module="game" explain="收到其他玩家选择场景事件的结果">
        <field class="int32" name="eventType" explain="事件类型"/>
        <field class="bool" name="yes" explain="选择结果是/否"/>
    </message>
</messages>