<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbGame/ITEM_TYPE">
    <enum name="Type" explain="物品type定义 类型" allowAlias="false">
        <type name="ARMOR_HEAD" number="0" explain="防具-头盔"/>
        <type name="ARMOR_CLOTHES" number="1" explain="防具-衣服"/>
        <type name="ARMOR_TROUSERS" number="2" explain="防具-裤子"/>
        <type name="ARMOR_SHOULDER" number="3" explain="防具-肩部"/>
        <type name="ARMOR_WAIST" number="4" explain="防具-腰部"/>
        <type name="ARMOR_BACK" number="5" explain="防具-背部"/>
        <type name="ARMOR_SHOES" number="6" explain="防具-鞋子"/>
        <type name="ARMOR_HAND" number="7" explain="防具-手套"/>
        <type name="ARMOR_NECKLACE" number="8" explain="防具-项链"/>
        <type name="ARMOR_RING" number="9" explain="防具-戒指"/>
        <type name="ARMOR_AMULET" number="10" explain="防具-护符"/>
        <type name="ARMOR_TRANSPORT " number="11" explain="防具-坐骑"/>
        <type name="ARMOR_FASHION " number="12" explain="防具-时装"/>
        <type name="WEAPON_ONEHAND_SWORD " number="13" explain="武器-单手剑"/>
        <type name="WEAPON_TWOHAND_SWORD " number="14" explain="武器-重剑"/>
        <type name="WEAPON_ONEHAND_BLADE " number="15" explain="武器-单手刀"/>
        <type name="WEAPON_TWOHAND_BLADE " number="16" explain="武器-重刀"/>
        <type name="WEAPON_ONEHAND_HEAVY " number="17" explain="武器-单手重型"/>
        <type name="WEAPON_TWOHAND_HEAVY " number="18" explain="武器-双持重型"/>
        <type name="WEAPON_TWOHAND_STAFF " number="19" explain="武器-法杖"/>
        <type name="WEAPON_TWOHAND_LANCE " number="20" explain="武器-长柄"/>
        <type name="WEAPON_ONEHAND_CROSSBOW " number="21" explain="武器-单手弩"/>
        <type name="WEAPON_TWOHAND_CROSSBOW " number="22" explain="武器-重弩"/>
        <type name="WEAPON_TWOHAND_BOW " number="23" explain="武器-弓箭"/>
        <type name="WEAPON_ONEHAND_HAND " number="24" explain="武器-副手"/>
        <type name="TASK" number="25" explain="任务物品"/>
        <type name="BATTLE_USE" number="26" explain="战斗中使用药品"/>
        <type name="ANYTIME_USE" number="27" explain="任何时间使用药品"/>
        <type name="NOT_BATTLE_USE " number="28" explain="战斗中不可使用药品"/>
        <type name="BUILD_MATERIAL" number="29" explain="建筑材料"/>
        <type name="GEM" number="30" explain="宝石"/>
        <type name="SKILL_BOOK " number="31" explain="技能书"/>
        <type name="PET" number="32" explain="宠物"/>
        <type name="SPECIAL" number="33" explain="特殊物品"/>
        <type name="WEAPON_BALL" number="34" explain="法器"/>
        <type name="WEAPON_ONEHAND_GUN" number="35" explain="武器-轻枪"/>
        <type name="WEAPON_TWOHAND_GUN" number="36" explain="武器-重枪"/>
        <type name="WEAPON_ONEHAND_HAMMER" number="37" explain="武器-轻锤"/>
        <type name="WEAPON_TWOHAND_HAMMER" number="38" explain="武器-重锤"/>
        <type name="WEAPON_TWOHAND_FAN" number="39" explain="武器-扇"/>
        <type name="BLOOD_BOTTLE" number="40" explain="能量精华"/>
        <type name="PET_EQUIP" number="41" explain="宠物装备"/>
        <type name="PET_EQUIP_EXP_BOOK  " number="42" explain="宠物装备经验"/>
        <type name="SEAL" number="43" explain="赋灵符"/>
        <type name="ENERGY_ESSENCE" number="44" explain="赋灵符"/>
        <type name="BOX_CHOOSE_ONE" number="45" explain="自选盒子"/>
        <type name="ADD_PET_LIFE" number="46" explain="增加宠物寿命"/>
        <type name="LN_STONE" number="47" explain=""/>
    </enum>
</messages>