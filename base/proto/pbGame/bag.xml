<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbGame">
    <message type="C2S" name="BagReset" module="game" explain="整理物品">
        <field class="pbGame/BagReset/BagReset.Type" name="type" explain="整理类型"/>
    </message>
    <message type="S2C" name="BagReset" explain="整理物品">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="map" name="store" explain="新的物品数据">
            <key class="int32" explain="slotPos"/>
            <value class="pbGame/struct.ItemData" explain="物品"/>
        </field>
    </message>
    <message type="C2S" name="BagItemSell" module="game" explain="物品操作">
        <field class="int32" name="type" explain="操作类型,1出售,2丢弃"/>
        <array class="pbGame/struct.ItemData" name="list" explain="操作的物品数据列表"/>
    </message>
    <message type="S2C" name="BagItemSell" explain="物品操作">
        <field class="pbBase/Response/Response.Code" name="code" explain="物品系列操作"/>
    </message>
    <message type="C2S" name="EquipWear" module="game" explain="穿戴装备">
        <field class="int32" name="slotPos" explain="物品位置"/>
        <field class="int32" name="itemId" explain="物品id"/>
    </message>
    <message type="S2C" name="EquipWear" explain="穿戴装备">
        <field class="pbBase/Response/Response.Code" name="code" explain=""/>
    </message>
    <message type="C2S" name="EquipTakeOff" module="game" explain="脱下装备">
        <field class="int32" name="slotPos" explain="物品位置"/>
        <field class="int32" name="itemId" explain="物品id"/>
    </message>
    <message type="S2C" name="EquipTakeOff" explain="脱下装备">
        <field class="pbBase/Response/Response.Code" name="code" explain=""/>
    </message>
    <message type="C2S" name="PlayerBagUse" module="game" explain="使用物品">
        <field class="pbGame/Bag/Bag.ItemUseType" name="useType" explain="操作类型"/>
        <field class="int32" name="itemSlotPos" explain="物品位置"/>
        <field class="int32" name="itemId" explain="物品id"/>
        <field class="int32" name="useNum" explain="使用数量"/>
        <field class="int32" name="extraId" explain="额外参数"/>
    </message>
    <message type="S2C" name="PlayerBagUse" explain="使用物品">
        <field class="pbBase/Response/Response.Code" name="code" explain=""/>
        <field class="string" name="responseString" explain="响应字符串"/>
        <field class="any" name="any" explain="任意扩展数据 用于响应结果"/>
    </message>
    <message type="C2S" name="BagItemBind" module="game" explain="物品绑定">
        <field class="pbGame/struct.ItemData" name="item" explain="物品数据"/>
    </message>
    <message type="S2C" name="BagItemBind" explain="物品绑定">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
    <message type="C2S" name="BagItemStar" module="game" explain="装备物品升星">
        <field class="pbGame/struct.ItemData" name="item" explain="物品数据"/>
        <field class="bool" name="isUpgrade" explain="是否是进阶升星"/>
    </message>
    <message type="S2C" name="BagItemStar" explain="装备物品升星">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="bool" name="result" explain="是否成功"/>
    </message>
    <message type="C2S" name="BagItemIdentify" module="game" explain="装备物品鉴定">
        <field class="pbGame/struct.ItemData" name="item" explain="物品数据"/>
        <field class="bool" name="isUpgrade" explain="是否是进阶鉴定"/>
    </message>
    <message type="S2C" name="BagItemIdentify" explain="装备物品鉴定">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="pbGame/struct.ItemData" name="item" explain="物品数据"/>
    </message>
    <message type="C2S" name="BagItemIdentifyAnswer" module="game" explain="鉴定响应">
        <field class="int32" name="id" explain="物品id"/>
        <field class="int32" name="slotPos" explain="物品位置"/>
    </message>
    <message type="S2C" name="BagItemIdentifyAnswer" explain="鉴定响应">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
    <message type="C2S" name="BagItemEnchase" module="game" explain="宝石镶嵌">
        <field class="int32" name="itemSlotPos" explain="物品位置"/>
        <field class="int32" name="gemSlotPos" explain="宝石位置"/>
    </message>
    <message type="S2C" name="BagItemEnchase" explain="宝石镶嵌">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
        <field class="bool" name="isBroken" explain="是否失败破损"/>
    </message>
    <message type="C2S" name="BagItemGemReplace" module="game" explain="宝石替换">
        <field class="int32" name="itemSlotPos" explain="物品位置"/>
        <field class="int32" name="gemSlotPos" explain="宝石位置"/>
    </message>
    <message type="S2C" name="BagItemGemReplace" explain="宝石替换">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码"/>
    </message>
</messages>