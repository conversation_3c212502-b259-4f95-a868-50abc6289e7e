syntax = "proto3";

package proto.EQP;

option go_package = "world/common/pbGame/EQP;EQP";

//After are enums.
// 物品get时的装备操作类型定义
// @go-enum-no-prefix
enum Type{
  ZERO = 0; //自动添加
  DEF_STR = 1; //劈砍防御
  DEF_AGI = 2; //穿刺防御
  DEF_MAGIC = 3; //魔法防御
  HIT_RATE = 4; //物理命中
  ATK_MIN = 5; //武伤小
  ATK_MAX = 6; //武伤大
  HIT_TIME = 7; //攻击次数
  ATK_TYPE = 8; //攻击类型
  ITEM_TYPE = 9; //物品类型
  ITEM_SET_ID = 10; //物品套装id
}

