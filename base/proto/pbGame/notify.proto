syntax = "proto3";

package proto;

option go_package = "world/common/pbGame;pbGame";

import "pbGame/struct.proto";

import "pbCross/struct.proto";

//After are messages.
message S2C_MapDataMessage {
  int32 mapId = 1; //地图id
  Point pos = 2; //玩家位置
  repeated int32 npcList = 3; //npc数据
  repeated CrossSimplePlayer plrList = 4; //玩家数据
}
message S2C_BagDataMessage {
  BagData bag = 1; //数据列表，不包含仓库
}
message S2C_LogoutMessage {
  int32 code = 1; //0服务器踢除，1账号在别处登录
}
