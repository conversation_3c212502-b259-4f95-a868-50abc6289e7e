syntax = "proto3";

package proto.EQUIP_POS;

option go_package = "world/common/pbGame/EQUIP_POS;EQUIP_POS";

//After are enums.
// 装备位置定义
// @go-enum-no-prefix
enum Type{
  ARMOR_NECKLACE_POS = 0; //链
  PET_POS = 1; //宠物
  ARMOR_RING_LEFT_POS = 2; //戒左
  ARMOR_FASHION_POS = 3; //时装
  ARMOR_BACK_POS = 4; //背
  ARMOR_CLOTHES_POS = 5; //衣
  ARMOR_HAND_POS = 6; //手
  ARMOR_HEAD_POS = 7; //头
  WEAPON_LEFT_POS = 8; //武-左
  ARMOR_SHOES_POS = 9; //鞋
  WEAPON_RIGHT_POS = 10; //武-右
  ARMOR_WAIST_POS = 11; //腰
  ARMOR_TROUSERS_POS = 12; //裤
  ARMOR_SHOULDER_POS = 13; //肩
  ARMOR_AMULET_POS = 14; //腿
  ARMOR_TRANSPORT_POS = 15; //坐骑
  ARMOR_RING_RIGHT_POS = 16; //戒-右
  SPIRIT_POS = 17; //vip
  BLOOD_BOTTLE_POS = 18; //血
}

