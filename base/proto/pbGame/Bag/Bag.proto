syntax = "proto3";

package proto.Bag;

option go_package = "world/common/pbGame/Bag;Bag";

//After are enums.
// 使用物品时的类型定义
// @go-enum-no-prefix
enum ItemUseType{
  None = 0; //未知
  Equip = 1; //装备
  UnEquip = 2; //
  Use = 3; //
  Lose = 4; //
  CheckUp = 5; //
  Enchase = 6; //
  Bind = 7; //
  UseByOneKey = 8; //
}

// 使用物品时的类型定义
// @go-enum-no-prefix
enum ItemUseSubType{
  NoWait = 0; //无需等待结果
  UsePetEgg = 1; //使用宠物蛋
  EquipPet = 2; //装备宠物
  UnEquipPet = 3; //卸下宠物
  UseChest = 4; //开启奖励盒子
  CheckUpSub = 5; //
  EnchaseSub = 6; //附魔
  BindSub = 7; //绑定
  CommandBook = 8; //使用指令书
  EquipSub = 9; //装备
  AddStoreNum = 10; //增加仓库格子数
  AddExp = 11; //增加经验
  AddPetExp = 12; //增加宠物经验
  PetRest = 13; //宠物洗髓
  PetAge = 14; //恢复宠物寿命
  Repair = 15; //修理
  GetTitle = 16; //获取称号
  UnEquipSub = 17; //卸下装备
  ChangeJob = 18; //更换职业
  PetItemAddSkill = 19; //宠物潜能石头
  Wait = 20; //
  AlertSex = 21; //
  AddCp = 22; //增加属性点
  AddSp = 23; //增加技能点
  AddProsperityDegree = 24; //
  SkillSlotPlayer = 25; //人物技能槽
  SkillSlotPet = 26; //宠物技能槽
  PetAddLife = 27; //恢复宠物寿命
}

