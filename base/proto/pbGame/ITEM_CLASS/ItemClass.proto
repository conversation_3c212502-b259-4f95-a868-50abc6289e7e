syntax = "proto3";

package proto.ITEM_CLASS;

option go_package = "world/common/pbGame/ITEM_CLASS;ITEM_CLASS";

//After are enums.
// 物品分类
// @go-enum-no-prefix
enum Type{
  None = 0; //自动添加
  WEAPON = 1; //武器
  ARMOR = 2; //防具
  PET = 3; //宠物
  USE_ITEM = 4; //可使用道具
  QUEST = 5; //材料
  GEM = 6; //宝石
  OTHER = 7; //其他
  BLOOD_BOTTLE = 8; //血瓶
  PET_EQUIP = 9; //宠物装备
  PET_EQUIP_EXP_BOOK = 10; //宠物装备经验
  HORSE = 11; //坐骑
  SEAL = 12; //赋灵
  ENERGY_ESSENCE = 13; //能量
  BOX_CHOOSE_ONE = 14; //自选盒
  PET_ADD_LIFE = 15; //宠物寿命石
  LN_STONE = 16; //传送石
}

