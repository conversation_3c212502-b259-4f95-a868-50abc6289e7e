<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbGame/ConditionType">
    <enum name="Type" explain="条件定义" allowAlias="false">
        <type name="None" number="0" explain="自动添加"/>
        <type name="Level" number="1" explain="等级"/>
        <type name="KillMonster" number="2" explain="杀怪数"/>
        <type name="Money1" number="3" explain="黄金"/>
        <type name="Money2" number="4" explain="金叶"/>
        <type name="Money3" number="5" explain="铜币"/>
        <type name="Honor" number="6" explain="荣誉"/>
        <type name="HaveItem" number="7" explain="物品"/>
        <type name="EquipItem" number="8" explain="装备物品"/>
        <type name="MissionDone" number="9" explain="完成任务"/>
        <type name="MissionDoing" number="10" explain="接受任务（当前持有某任务）"/>
        <type name="PlayerRace" number="11" explain="阵营"/>
        <type name="InMap" number="12" explain="在某个地图"/>
        <type name="AfterDate" number="13" explain="某个日期之后"/>
        <type name="AfterTime" number="14" explain="某个小时之后（每天）"/>
        <type name="PlayerSex" number="15" explain="性别"/>
        <type name="JoinCountry" number="16" explain="加入国家"/>
        <type name="HaveBuff" number="17" explain="持有Buff"/>
        <type name="PlayerHp" number="18" explain="生命值达到"/>
        <type name="PlayerMp" number="19" explain="法力值达到"/>
        <type name="PlayerCon" number="20" explain="体质"/>
        <type name="PlayerStr" number="21" explain="力量"/>
        <type name="PlayerIlt" number="22" explain="智力"/>
        <type name="PlayerAgi" number="23" explain="敏捷"/>
        <type name="Vip" number="24" explain="vip等级"/>
        <type name="CityLevel" number="27" explain="城市等级"/>
        <type name="CityBranch" number="28" explain="完成城市任务"/>
        <type name="CityDegree" number="29" explain="城市繁荣度"/>
        <type name="CityArmy" number="30" explain="城市军力"/>
        <type name="PlayerJob" number="32" explain="职业"/>
        <type name="CountryRank" number="33" explain="国家职位是xxx"/>
        <type name="CountryRank2" number="34" explain="国家职位达到xxx"/>
        <type name="PlayerExp" number="35" explain="经验值"/>
    </enum>
</messages>