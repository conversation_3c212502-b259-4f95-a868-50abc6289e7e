syntax = "proto3";

package proto;

option go_package = "world/common/pbGame;pbGame";

import "pbBase/Response/Response.proto";

import "pbGame/struct.proto";

//After are messages.
message C2S_PetAddSkillSureMessage {
  int64 petId = 1; //宠物id
}
message S2C_PetAddSkillSureMessage {
  proto.Response.Code code = 1; //响应码
}
message C2S_PetSealMessage {
  int32 slotPos = 1; //位置
  int64 petId = 2; //宠物id
  int32 type = 3; //封印类型
}
message S2C_PetSealMessage {
  proto.Response.Code code = 1; //响应码
  ItemData book = 2; //封印成功返回的技能书物品
}
message C2S_PetSkillBookLearnMessage {
  int32 slotPos = 1; //技能书位置
  int32 petItemSlotPos = 2; //宠物物品位置
}
message S2C_PetSkillBookLearnMessage {
  proto.Response.Code code = 1; //响应码
  SkillInfo skill = 2; //学习后的技能
}
