<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbLogin">
    <struct name="LoginResult" explain="登录结果信息">
        <field class="string" name="id" explain="用户id"/>
        <field class="string" name="token" explain="会话凭证"/>
    </struct>
    <struct name="AreaLine" explain="区服信息">
        <field class="int32" name="id" explain="区服id"/>
        <field class="string" name="name" explain="区服名称"/>
        <field class="int32" name="openTime" explain="区服名称"/>
        <field class="pbBase/AreaLineState/AreaLineState.Type" name="state" explain="区服状态"/>
        <field class="int32" name="actorCount" explain="拥有角色数量"/>
    </struct>
</messages>