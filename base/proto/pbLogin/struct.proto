syntax = "proto3";

package proto;

option go_package = "world/common/pbLogin;pbLogin";

import "pbBase/AreaLineState/AreaLineState.proto";

//After are structs.
// 登录结果信息
message LoginResult{
  string id = 1; //用户id
  string token = 2; //会话凭证
}

// 区服信息
message AreaLine{
  int32 id = 1; //区服id
  string name = 2; //区服名称
  int32 openTime = 3; //区服名称
  proto.AreaLineState.Type state = 4; //区服状态
  int32 actorCount = 5; //拥有角色数量
}

