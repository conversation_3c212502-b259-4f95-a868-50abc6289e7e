<?xml version="1.0" encoding="UTF-8"?>
<messages package="world/common/pbCross">
    <message type="S2R" name="OnLeave" module="game" explain="session离线">
    </message>
    <message type="S2R" name="OnPlayerLogin" module="game" explain="用户选区登录后通知逻辑服">
    </message>
    <message type="R2S" name="OnPlayerLogin" explain="逻辑服回应">
        <field class="string" name="err" explain="错误字符串" />
    </message>
    <message type="S2R" name="IsPlayerOnline" module="game" explain="查询玩家是不是在节点中在线">
        <field class="string" name="uid" explain="玩家uid" />
    </message>
    <message type="R2S" name="IsPlayerOnline" explain="查询玩家是不是在节点中在线响应">
        <field class="bool" name="is" explain="是否在线" />
    </message>
    <message type="S2R" name="KickPlayerForceByPid" module="game" explain="节点之间相互通知，使玩家离线">
        <field class="string" name="pid" explain="玩家pid" />
    </message>
    <message type="S2R" name="KickPlayerForceByUid" module="game" explain="节点之间相互通知，使玩家离线">
        <field class="string" name="uid" explain="玩家所属用户uid" />
    </message>
    <message type="S2R" name="KickSessionByUid" module="login" explain="节点之间相互通知，使会话失效，登录服使用">
        <field class="string" name="uid" explain="玩家所属用户uid" />
    </message>
    <message type="S2R" name="RemoveUserByUid" module="login"
        explain="game通知login，相关用户在game登录了某个角色，login用来移除user">
        <field class="string" name="uid" explain="用户uid" />
    </message>
    <message type="R2S" name="KickPlayerForce" explain="节点通知玩家离线结果">
        <field class="bool" name="is" explain="玩家在当前节点，并且使其离线，则返回true" />
    </message>
    <message type="R2S" name="SimpleResponse" explain="简单响应">
        <field class="pbBase/Response/Response.Code" name="code" explain="响应码" />
        <field class="string" name="data" explain="数据" />
    </message>
    <message type="S2R" name="NotifyPlayerEnterMap" module="game" explain="通知，玩家进入某个地图">
        <field class="int32" name="fromMapId" explain="之前的地图id" />
        <field class="int32" name="toMapId" explain="要进入的地图id" />
        <field class="int32" name="toMapX" explain="要进入的地图x" />
        <field class="int32" name="toMapY" explain="要进入的地图y" />
        <field class="pbCross/struct.CrossSimplePlayer" name="plr" explain="玩家简要数据" />
    </message>
    <message type="R2S" name="ReplayPlayerEnterMap" module="game" explain="玩家进入某个地图响应">
        <array class="pbCross/struct.CrossSimplePlayer" name="plr" explain="玩家简要数据" />
    </message>
    <message type="S2R" name="NotifyPlayerLeaveMap" module="game" explain="通知，玩家离开某个地图">
        <field class="int32" name="mapId" explain="地图id" />
        <field class="int32" name="id" explain="玩家id" />
    </message>
    <message type="S2R" name="NotifyPlayerMove" module="game" explain="通知，玩家移动">
        <field class="int32" name="mapId" explain="地图id" />
        <field class="int32" name="id" explain="玩家id" />
        <field class="int32" name="x" explain="x" />
        <field class="int32" name="y" explain="y" />
        <field class="int32" name="mode" explain="mode" />
    </message>
    <message type="S2R" name="NotifyTeamInvite" module="game" explain="通知，邀请玩家入队">
        <field class="int32" name="id" explain="玩家id" />
        <field class="pbCross/struct.CrossSimplePlayer" name="inviter" explain="邀请者" />
    </message>
    <message type="S2R" name="Forward" module="game" explain="消息转发">
        <field class="string" name="id" explain="玩家id" />
        <field class="int32" name="gameId" explain="玩家gameId" />
        <field class="string" name="router" explain="路由" />
        <field class="bytes" name="msg" explain="消息" />
        <field class="int32" name="count" explain="转发重试次数" />
    </message>
</messages>