<?xml version="1.0" encoding="UTF-8" ?>
<messages package="world/common/pbCross">
    <struct name="CrossSimplePlayer" explain="跨节点简要传递玩家数据">
        <field class="string" name="from" explain="发送方，即玩家当前所在节点"/>
        <field class="string" name="id" explain="玩家用户id"/>
        <field class="int32" name="gameId" explain="玩家游戏id"/>
        <field class="string" name="name" explain="玩家名称"/>
        <field class="int32" name="mapId" explain="所处地图id"/>
        <field class="int32" name="x" explain="所处地图x"/>
        <field class="int32" name="y" explain="所处地图y"/>
        <field class="int64" name="icon1" explain="icon1"/>
        <field class="int64" name="icon2" explain="icon2"/>
        <field class="int64" name="icon3" explain="icon3"/>
        <field class="int32" name="level" explain="等级"/>
        <field class="int32" name="level2" explain="传奇等级"/>
        <field class="string" name="title" explain="称号"/>
        <field class="int64" name="setting" explain="设置"/>
        <field class="int64" name="status" explain="状态"/>
        <field class="int32" name="mode" explain="设置"/>
        <field class="string" name="shopName" explain="摆摊名称"/>
        <field class="string" name="countryName" explain="国家名称"/>
        <field class="int32" name="vipLv" explain="vip等级"/>
        <field class="int32" name="vipLvMax" explain="历史最高vip等级"/>
        <field class="CrossSimplePet" name="pet" explain="宠物数据"/>
    </struct>
    <struct name="CrossSimplePet" explain="跨节点简要传递宠物数据">
        <field class="int32" name="cfgId" explain="宠物配置id"/>
        <field class="int64" name="id" explain="宠物id"/>
        <field class="string" name="name" explain="自定义名字，可能存在"/>
        <field class="int64" name="age" explain="寿命"/>
    </struct>
    <struct name="PlayerEvent" explain="玩家事件数据">
        <field class="int32" name="eventId" explain="事件id"/>
        <field class="int32" name="eventType" explain="事件类型"/>
        <field class="string" name="message" explain="事件消息"/>
        <field class="string" name="extraInfo" explain="额外信息"/>
        <field class="int64" name="expireTime" explain="过期时间"/>
        <field class="CrossSimplePlayer" name="player" explain="发起者"/>
    </struct>
</messages>