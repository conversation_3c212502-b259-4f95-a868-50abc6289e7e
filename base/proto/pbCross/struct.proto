syntax = "proto3";

package proto;

option go_package = "world/common/pbCross;pbCross";

//After are structs.
// 跨节点简要传递玩家数据
message CrossSimplePlayer{
  string from = 1; //发送方，即玩家当前所在节点
  string id = 2; //玩家用户id
  int32 gameId = 3; //玩家游戏id
  string name = 4; //玩家名称
  int32 mapId = 5; //所处地图id
  int32 x = 6; //所处地图x
  int32 y = 7; //所处地图y
  int64 icon1 = 8; //icon1
  int64 icon2 = 9; //icon2
  int64 icon3 = 10; //icon3
  int32 level = 11; //等级
  int32 level2 = 12; //传奇等级
  string title = 13; //称号
  int64 setting = 14; //设置
  int64 status = 15; //状态
  int32 mode = 16; //设置
  string shopName = 17; //摆摊名称
  string countryName = 18; //国家名称
  int32 vipLv = 19; //vip等级
  int32 vipLvMax = 20; //历史最高vip等级
  CrossSimplePet pet = 21; //宠物数据
}

// 跨节点简要传递宠物数据
message CrossSimplePet{
  int32 cfgId = 1; //宠物配置id
  int64 id = 2; //宠物id
  string name = 3; //自定义名字，可能存在
  int64 age = 4; //寿命
}

// 玩家事件数据
message PlayerEvent{
  int32 eventId = 1; //事件id
  int32 eventType = 2; //事件类型
  string message = 3; //事件消息
  string extraInfo = 4; //额外信息
  int64 expireTime = 5; //过期时间
  CrossSimplePlayer player = 6; //发起者
}

