package battle

import (
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/common/pbGame/BattleDefine"
	"world/game/gameBase/gameStruct"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sRunLocalBattleMessageHandler 校验本地战斗
func C2sRunLocalBattleMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_RunLocalBattleMessage) protoreflect.ProtoMessage {

		// 展示无校验逻辑
		return &pbGame.S2C_RunLocalBattleMessage{
			Code:   Response.NoError,
			Result: BattleDefine.RIGHT_WIN,
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
