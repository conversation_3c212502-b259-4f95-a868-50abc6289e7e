package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"
	ut "world/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rKickPlayerForceByUidMessageHandler 节点之间相互通知，使玩家离线
func S2rKickPlayerForceByUidMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbCross.S2R_KickPlayerForceByUidMessage) protoreflect.ProtoMessage {
		uid := msg.GetUid()
		plr, exists := gameMgr.Player().TryGetPlayerByUid(uid)
		if exists {
			lock := gameMgr.Player().LockByUid(plr.Id)
			defer ut.Unlock(lock)
			gameMgr.Player().UserKickOffline(plr, true)
		}
		return &pbCross.R2S_KickPlayerForceMessage{
			Is: exists,
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
