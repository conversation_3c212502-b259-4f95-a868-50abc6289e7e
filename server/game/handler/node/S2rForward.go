package node

import (
	"world/common/net_helper"
	"world/common/pbCross"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rForwardMessageHandler 消息转发
func S2rForwardMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_ForwardMessage) protoreflect.ProtoMessage {

		var player *gameStruct.Player
		// 先通过玩家id找
		id := msg.GetId()
		if id != "" {
			player, _ = gameMgr.Player().TryGetPlayerByUid(id)
		}
		// 如果玩家id找不到，再通过玩家gameId找
		gameId := int(msg.GetGameId())
		if player == nil && gameId > 0 {
			player, _ = gameMgr.Player().TryGetPlayerByGameId(gameId)
		}

		if player == nil {
			if msg.GetCount() < 3 {
				// 如果玩家找不到 可能是短暂的进行了一次节点切换 那就再发一次
				_, err := gameMgr.Sender().ForwardMsgWithBytes(id, gameId, msg.GetRouter(), msg.GetMsg(), int(msg.GetCount())+1)
				// 还是发失败了 就不再处理
				if net_helper.IsNodeNotFoundError(err) {

				}
			}
			return &pbCross.R2S_SimpleResponseMessage{}
		}
		gameMgr.Sender().SendWithBytes(player.Session, msg.GetRouter(), msg.GetMsg(), false)
		return &pbCross.R2S_SimpleResponseMessage{}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
