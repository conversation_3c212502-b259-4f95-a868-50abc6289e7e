package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rNotifyTeamInviteMessageHandler 通知，邀请玩家入队
func S2rNotifyTeamInviteMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(msg *pbCross.S2R_NotifyTeamInviteMessage) protoreflect.ProtoMessage {

		code := gameMgr.Team().ProcessTeamInvite(int(msg.GetId()), msg.GetInviter())
		return &pbCross.R2S_SimpleResponseMessage{Code: code}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
