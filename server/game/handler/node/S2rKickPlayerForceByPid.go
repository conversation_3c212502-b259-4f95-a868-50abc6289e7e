package node

import (
	"world/common/pbCross"
	"world/game/gameMgr"
	ut "world/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// S2rKickPlayerForceByPidMessageHandler 节点之间相互通知，使玩家离线
func S2rKickPlayerForceByPidMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbCross.S2R_KickPlayerForceByPidMessage) protoreflect.ProtoMessage {
		pid := msg.GetPid()
		plr, exists := gameMgr.Player().TryGetPlayerByUid(pid)
		if exists {
			lock := gameMgr.Player().LockByUid(plr.Id)
			defer ut.Unlock(lock)
			gameMgr.Player().UserKickOffline(plr, true)
		}
		return &pbCross.R2S_KickPlayerForceMessage{
			Is: exists,
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
