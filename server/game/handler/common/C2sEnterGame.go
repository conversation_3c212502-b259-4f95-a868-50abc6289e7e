package common

import (
	"world/common/net_helper"
	"world/common/pbBase/Response"
	"world/common/pbCross"
	"world/common/pbGame"
	"world/common/router"
	"world/game/gameBase/types/PlayerBag"
	"world/game/gameMgr"
	ut "world/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sEnterGameMessageHandler 角色登入
func C2sEnterGameMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbGame.C2S_EnterGameMessage) protoreflect.ProtoMessage {
		uid := session.GetUserID()
		if ut.IsEmpty(uid) {
			return &pbGame.S2C_CreateRoleMessage{
				Code: Response.ErrPleaseLoginFirst,
			}
		}
		// 让该用户关联的角色全部下线
		net_helper.CallGameSync(this, router.S2RKickPlayerForceByUidMessage, session, &pbCross.S2R_KickPlayerForceByUidMessage{Uid: uid})
		// 通知登录服移除user信息
		net_helper.CallLoginSync(this, router.S2RRemoveUserByUidMessage, &pbCross.S2R_RemoveUserByUidMessage{Uid: uid})
		code, plr := gameMgr.Player().PreloadPlayer(uid, int(msg.GetGameId()), session)
		if code == Response.NoError {
			// 下发背包数据
			gameMgr.Sender().SendTo(plr, router.S2CBagDataMessage, &pbGame.S2C_BagDataMessage{Bag: plr.Bag.ToPb(PlayerBag.BAG)}, true)
			// 下发玩家基础数据
			gameMgr.Sender().SendTo(plr, router.S2CEnterGameMessage, &pbGame.S2C_EnterGameMessage{
				Code: code,
				Data: plr.ToPb(),
			}, true)
			// 玩家进入地图
			gameMgr.Map().EnterMap(plr, plr.MapId, plr.X, plr.Y)
		}
		return nil
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
