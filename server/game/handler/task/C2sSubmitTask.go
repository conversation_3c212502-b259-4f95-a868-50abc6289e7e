package task

import (
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sSubmitTaskMessageHandler 提交任务
func C2sSubmitTaskMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_SubmitTaskMessage) protoreflect.ProtoMessage {
		myPet := player.GetPet()
		orgGrowExp := 0
		orgGrowLevel := 0
		if myPet != nil {
			orgGrowExp = myPet.GrowExp
			orgGrowLevel = myPet.GrowLevel
		}
		code := gameMgr.Task().DoSubmitTask(player, int(msg.GetNpcId()), int(msg.GetTaskId()), int(msg.GetItemId()))
		data := &pbGame.S2C_SubmitTaskMessage{
			Code: code,
		}
		myPet = player.GetPet()
		if myPet != nil && code == Response.NoError {
			if myPet.GrowExp != orgGrowExp || myPet.GrowLevel != orgGrowLevel {
				data.MyPet = myPet.ToPb()
			}
		}

		return data
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
