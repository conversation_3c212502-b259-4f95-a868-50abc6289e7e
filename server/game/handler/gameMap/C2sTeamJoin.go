package gameMap

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sTeamJoinMessageHandler 加入队伍
func C2sTeamJoinMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_TeamJoinMessage) protoreflect.ProtoMessage {

		return nil
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
