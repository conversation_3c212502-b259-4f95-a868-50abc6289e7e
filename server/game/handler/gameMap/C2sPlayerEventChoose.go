package gameMap

import (
	"world/base/enum/Define"
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/common/router"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"
	ut "world/utils"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sPlayerEventChooseMessageHandler 玩家对于场景事件的选择
func C2sPlayerEventChooseMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_PlayerEventChooseMessage) protoreflect.ProtoMessage {

		id := int(msg.GetId())
		yes := msg.GetYes()
		handlePlayerEventChoose(this, player, id, yes)
		return nil
	}
	/* action-code-end */
}

/* logic-code-start */

func handlePlayerEventChoose(app module.RPCModule, player *gameStruct.Player, eventId int, yes bool) Response.Code {
	evt := player.GetPlayerEvent(eventId, false)
	if evt == nil || evt.ExpireTime < ut.Now() {
		return Response.E22
	}

	code := Response.NoError
	switch evt.EventType {
	case int32(Define.PLAYER_EVENT_TEAM_INVITE):
		code = teamInvite(app, player, eventId, yes)
	}

	// 通知事件发起者 事件选择的结果
	msg := &pbGame.S2C_GetPlayerEventChooseResultMessage{EventType: int32(evt.EventType), Yes: yes}
	gameMgr.Sender().ForwardMsgById(evt.Player.Id, router.S2CGetPlayerEventChooseResultMessage, msg)
	return code
}

func teamInvite(app module.RPCModule, player *gameStruct.Player, eventId int, yes bool) Response.Code {
	evt := player.GetPlayerEvent(eventId, true)
	if !yes {
		evt.ExpireTime = 0
	}

	return Response.NoError
}

/* logic-code-end */
