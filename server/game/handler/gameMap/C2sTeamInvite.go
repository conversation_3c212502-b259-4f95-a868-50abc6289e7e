package gameMap

import (
	"world/common/pbGame"
	"world/game/gameBase/gameStruct"
	"world/game/gameMgr"

	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sTeamInviteMessageHandler 邀请加入队伍
func C2sTeamInviteMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(player *gameStruct.Player, msg *pbGame.C2S_TeamInviteMessage) protoreflect.ProtoMessage {

		id := int(msg.GetOtherId())
		gameMgr.Team().DoTeamInvite(player, id)
		return &pbGame.S2C_TeamInviteMessage{}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
