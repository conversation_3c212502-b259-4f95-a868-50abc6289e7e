package gameStruct

import (
	"fmt"
	"world/base/cfg"
	"world/common/pbGame"
	"world/common/pbGame/MyDefine"
	ut "world/utils"

	"github.com/samber/lo"
)

type Skill struct {
	Id            int  `bson:"id"`
	BaseLevel     int  `bson:"baseLevel, omitempty"`
	AddLevel      int  `bson:"addLevel, omitempty"` // 这个增加等级通常是装备附加的
	IsLearnByBook bool `bson:"isLearn, omitempty"`  // 是不是学习而来 主要是针对宠物技能书
}

// IsCanLearn 判断技能是否可以学习 主要是看超出配置等级没
func (s *Skill) IsCanLearn() bool {
	if !s.IsLearnByBook {
		return false
	}
	return s.GetJson(true).ConfigMaxLevel > s.BaseLevel
}

// GetJson 获取技能配置
//
// Parameters:
//   - ignoreAdd bool 是否忽略额外增加的等级,通常情况下这个值是false
//
// Returns:
//   - *cfg.Skill[string]
func (s *Skill) GetJson(ignoreAdd ...bool) *cfg.Skill[string] {
	ignore := false
	if len(ignoreAdd) > 0 {
		ignore = ignoreAdd[0]
	}
	maxLv := cfg.GetSkillConfigMaxLevel(s.Id)
	lv := s.BaseLevel + lo.If(ignore, 0).Else(s.AddLevel)
	lv = ut.Min(maxLv, lv)
	bean, _ := cfg.ContainerSkill.GetBeanByUnique(fmt.Sprintf("%d-%d", s.Id, lv))
	return bean
}

// ToPb 转换成pb数据
//
// Returns:
//   - *pbGame.SkillInfo
func (s *Skill) ToPb() *pbGame.SkillInfo {
	if s == nil {
		return nil
	}
	return &pbGame.SkillInfo{
		Id:        int32(s.Id),
		BaseLevel: int32(s.BaseLevel),
		AddLevel:  int32(s.AddLevel),
		IsLearn:   s.IsLearnByBook,
	}
}

// GetPowerValue 获取技能的某个属性值
//
// Parameters:
//   - powerType MyDefine.POWER 属性类型
//
// Returns:
//   - int
func (s *Skill) GetPowerValue(powerType MyDefine.POWER) int {
	val := 0
	json := s.GetJson()
	if json.Power1 == powerType {
		val += json.PowerValue1
	}
	if json.Power2 == powerType {
		val += json.PowerValue2
	}
	if json.Power3 == powerType {
		val += json.PowerValue3
	}
	return val
}
