package gameStruct

import (
	"math"
	"world/common/pbBase/ModelConst"
	"world/common/pbGame/EQP"
	"world/common/pbGame/ITEM_TYPE"
	"world/common/pbGame/MyDefine"
	"world/common/pbGame/SKILL_TYPE"
	"world/game/gameBase/types/PlayerBag"
	ut "world/utils"
	"world/utils/bit"

	"github.com/samber/lo"
)

type GameVo interface {

	// 是不是玩家
	IsPlayer() bool
	// 是不是宠物
	IsPet() bool
	// Get 属性get，不能处理则返回-1024
	//
	// Parameters:
	//   - typ ModelConst.Type
	//
	// Returns:
	//   - int
	Get(typ ModelConst.Type) int
	AttrModule() *AttrModule

	// BagModule 获取背包模块  千万要注意nil
	//
	// Returns:
	//   - *BagModule
	BagModule() *BagModule
	SkillModule() *SkillModule

	// 初始化所有模块
	InitAllModule()
	// 获取套装数据
	GetItemSetData() []int
}

func GetBaseValue(p GameVo, base int, baseDefine, percentDefine MyDefine.POWER, skillType SKILL_TYPE.SKILL_TYPE, min, max int) int {
	// 固定值
	val := base
	// 百分比加成
	percent := 0
	// 技能增加的基础固定数值
	val += p.SkillModule().GetSkillPowerValue(skillType, baseDefine)
	// 技能增加的基础百分比
	percent = p.SkillModule().GetSkillPowerValue(skillType, percentDefine)
	// 装备增加的百分比
	percent += GetBagEquipPowerValue(p, percentDefine, false)
	// buff基础
	val += GetPowerValueByBuffer(p, baseDefine)
	// buff 百分比
	percent += GetPowerValueByBuffer(p, percentDefine)
	if p.SkillModule().FormationSkill != nil {
		val += p.SkillModule().FormationSkill.GetPowerValue(baseDefine)
		percent += p.SkillModule().FormationSkill.GetPowerValue(percentDefine)
	}
	// 变身卡 todo
	// _ := p.playerTurnMonster;
	//  null != _ && (s += _.getPowerValue(t), l += _.getPowerValue(i));
	val += val * percent / 100
	// 装备增加的基础
	val += GetBagEquipPowerValue(p, baseDefine, false)
	val = ut.SumValue(val, 0, min, max)
	return 0 | val
}

// CheckHPMP 检查HP和MP
func CheckHPMP(p GameVo) {
	hpMax := p.Get(ModelConst.HPMAX)
	attr := p.AttrModule()
	if attr.Hp > hpMax {
		attr.Hp = hpMax
	}
	mpMax := p.Get(ModelConst.MPMAX)
	if attr.Mp > mpMax {
		attr.Mp = mpMax
	}
}

// ResumeHPMP 恢复HP和MP
func ResumeHPMP(p GameVo) {
	attr := p.AttrModule()
	attr.Hp = p.Get(ModelConst.HPMAX)
	attr.Mp = p.Get(ModelConst.MPMAX)
}

// 不要直接使用这个方法
func baseGet(p GameVo, typ ModelConst.Type) int {
	ret := 0
	switch typ {
	case ModelConst.SP:
		return p.SkillModule().Sp
	case ModelConst.MONEY1:
		if p.BagModule() != nil {
			return p.BagModule().Money1
		}
	case ModelConst.MONEY2:
		if p.BagModule() != nil {
			return p.BagModule().Money2
		}
	case ModelConst.MONEY3:
		if p.BagModule() != nil {
			return p.BagModule().Money3
		}
	case ModelConst.LEVEL:
		return p.AttrModule().Level
	case ModelConst.LEVEL2:
		return p.AttrModule().Level2
	case ModelConst.HP:
		return p.AttrModule().Hp
	case ModelConst.MP:
		return p.AttrModule().Mp
	case ModelConst.STR: // 力量
		return GetBaseValue(p, p.AttrModule().Str, MyDefine.POWER_STR, MyDefine.POWER_STR_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE))
	case ModelConst.CON: // 体质
		return GetBaseValue(p, p.AttrModule().Con, MyDefine.POWER_CON, MyDefine.POWER_CON_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE))
	case ModelConst.AGI: // 敏捷
		return GetBaseValue(p, p.AttrModule().Agi, MyDefine.POWER_AGI, MyDefine.POWER_AGI_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE))
	case ModelConst.ILT: // 智力
		return GetBaseValue(p, p.AttrModule().Ilt, MyDefine.POWER_ILT, MyDefine.POWER_ILT_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE))
	case ModelConst.WIS: // 感知
		return GetBaseValue(p, p.AttrModule().Wis, MyDefine.POWER_WIS, MyDefine.POWER_WIS_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE))
	case ModelConst.HPMAX: // 最大生命值
		lv := p.Get(ModelConst.LEVEL) + p.Get(ModelConst.LEVEL2)
		conHp := p.Get(ModelConst.CON)
		strHp := p.Get(ModelConst.STR)
		value := 65*conHp/10 + 3*strHp + 100 + 40*(lv-1)
		return GetPowerValue(p, value, p.AttrModule().HpMax, MyDefine.POWER_HPMAX, MyDefine.POWER_HPMAX_PERCENT, SKILL_TYPE.PASSIVE, 1, int(ModelConst.MAX_PLAYER_HP), false)
	case ModelConst.MPMAX: // 最大法力值
		lv := p.Get(ModelConst.LEVEL) + p.Get(ModelConst.LEVEL2)
		iltMp := p.Get(ModelConst.ILT)
		wisMp := p.Get(ModelConst.WIS)
		value := 75*iltMp/10 + 35*wisMp/10 + 50 + 10*(lv-1)
		return GetPowerValue(p, value, p.AttrModule().MpMax, MyDefine.POWER_MPMAX, MyDefine.POWER_MPMAX_PERCENT, SKILL_TYPE.PASSIVE, 1, int(ModelConst.MAX_PLAYER_MP), false)
	case ModelConst.SPEED: // 出手速度
		value := 3*p.Get(ModelConst.AGI) + 15*p.Get(ModelConst.WIS)/10
		return GetPowerValue(p, value, p.AttrModule().Speed, MyDefine.POWER_SPEED, MyDefine.POWER_SPEED_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
	case ModelConst.LEFT_WEAPON_TYPE: // 左手武器类型
		return lo.If(p.BagModule() == nil, -1).Else(p.BagModule().GetForPos(EQP.ITEM_TYPE, PlayerBag.WEAPON_LEFT_POS))
	case ModelConst.RIGHT_WEAPON_TYPE: // 右手武器类型
		return lo.If(p.BagModule() == nil, -1).Else(p.BagModule().GetForPos(EQP.ITEM_TYPE, PlayerBag.WEAPON_RIGHT_POS))
	case ModelConst.LEFT_ATK_MIN: // 左手武器最小伤害
		if p.BagModule() != nil {
			value := p.BagModule().GetForPos(EQP.ATK_MIN, PlayerBag.WEAPON_LEFT_POS)
			value = ut.Max(0, value)
			value = AddWeaponSkillDamageOrDur(p, value, PlayerBag.WEAPON_LEFT_POS)
			return ut.SumValue(value, 0, 0, int(ModelConst.MAX_ATK))
		}
		return 0
	case ModelConst.RIGHT_ATK_MIN: // 右手武器最小伤害
		if p.BagModule() != nil {
			value := p.BagModule().GetForPos(EQP.ATK_MIN, PlayerBag.WEAPON_RIGHT_POS)
			value = ut.Max(0, value)
			value = AddWeaponSkillDamageOrDur(p, value, PlayerBag.WEAPON_RIGHT_POS)
			return ut.SumValue(value, 0, 0, int(ModelConst.MAX_ATK))
		}
		return 0
	case ModelConst.ATK_MIN: // 最小武伤
		left := p.Get(ModelConst.LEFT_ATK_MIN)
		right := p.Get(ModelConst.RIGHT_ATK_MIN)
		return ut.Max(left, right)
	case ModelConst.LEFT_ATK_MAX: // 左手最大武伤
		if p.BagModule() != nil {
			value := p.BagModule().GetForPos(EQP.ATK_MAX, PlayerBag.WEAPON_LEFT_POS)
			value = ut.Max(0, value)
			value = AddWeaponSkillDamageOrDur(p, value, PlayerBag.WEAPON_LEFT_POS)
			return ut.SumValue(value, 0, 0, int(ModelConst.MAX_ATK))
		}
		return 0
	case ModelConst.RIGHT_ATK_MAX: // 右手最大武伤
		if p.BagModule() != nil {
			value := p.BagModule().GetForPos(EQP.ATK_MAX, PlayerBag.WEAPON_RIGHT_POS)
			value = ut.Max(0, value)
			value = AddWeaponSkillDamageOrDur(p, value, PlayerBag.WEAPON_RIGHT_POS)
			return ut.SumValue(value, 0, 0, int(ModelConst.MAX_ATK))
		}
		return 0
	case ModelConst.ATK_MAX: // 最大武伤
		left := p.Get(ModelConst.LEFT_ATK_MAX)
		right := p.Get(ModelConst.RIGHT_ATK_MAX)
		return ut.Max(left, right)
	case ModelConst.LEFT_ATK_TIME: // 左手武器攻击次数
		if p.BagModule() != nil {
			value := p.BagModule().GetForPos(EQP.HIT_TIME, PlayerBag.WEAPON_LEFT_POS)
			if value < 0 {
				return 0
			}
			return ut.SumValue(value+1, 0, int(ModelConst.MIN_HIT_TIME), int(ModelConst.MAX_HIT_TIME))
		}
		return 0
	case ModelConst.RIGHT_ATK_TIME: // 右手武器攻击次数
		if p.BagModule() != nil {
			value := p.BagModule().GetForPos(EQP.HIT_TIME, PlayerBag.WEAPON_RIGHT_POS)
			if value < 0 {
				return 0
			}
			return ut.SumValue(value+1, 0, int(ModelConst.MIN_HIT_TIME), int(ModelConst.MAX_HIT_TIME))
		}
		return 0
	case ModelConst.ATK_TIME: //攻击次数
		weaponType := GetEquipWeaponType(p)
		value := 0
		if weaponType == int(MyDefine.BACK_ERROR_NULL_HAND) {
			value = 1
			value += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_HAND_ATK_TIME)
			value += GetBagEquipPowerValue(p, MyDefine.POWER_HAND_ATK_TIME, true)
		} else {
			value = p.Get(ModelConst.LEFT_ATK_TIME) + p.Get(ModelConst.RIGHT_ATK_TIME)
		}
		value = AddWeaponSkillAtkTime(p, value, ITEM_TYPE.Type(weaponType))
		return ut.SumValue(value, p.AttrModule().AtkTime, int(ModelConst.MIN_HIT_TIME), int(ModelConst.MAX_HIT_TIME))
	case ModelConst.ATK_STR: // 劈砍攻击力
		value := 3*p.Get(ModelConst.AGI) + 5*p.Get(ModelConst.STR)
		return GetPowerValue(p, value, p.AttrModule().AtkStr, MyDefine.POWER_ATK_STR, MyDefine.POWER_ATK_STR_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), true)
	case ModelConst.ATK_AGI: // 穿刺攻击力
		value := 5*p.Get(ModelConst.STR) + 5*p.Get(ModelConst.AGI)
		return GetPowerValue(p, value, p.AttrModule().AtkAgi, MyDefine.POWER_ATK_AGI, MyDefine.POWER_ATK_AGI_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), true)
	case ModelConst.ATK_MAGIC: // 魔法攻击力
		value := (3*p.Get(ModelConst.ILT) + 2*p.Get(ModelConst.WIS)) * (p.Get(ModelConst.HIT_MAGIC) + 900) / 1000
		return GetPowerValue(p, value, p.AttrModule().AtkMagic, MyDefine.POWER_ATK_MAGIC, MyDefine.POWER_ATK_MAGIC_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
	case ModelConst.ATK_STR_NEARBY: // 近身劈砍攻击力
		value := p.Get(ModelConst.ATK_STR)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_ATK_STR_NEARBY, MyDefine.POWER_ATK_STR_NEARBY_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_ATK_STR, MyDefine.POWER_ATK_STR_NEARBY_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_ATK_STR_NEARBY, MyDefine.POWER_ATK_STR_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		return value
	case ModelConst.ATK_STR_RANGE: // 远程劈砍攻击力
		value := p.Get(ModelConst.ATK_STR)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_ATK_STR_RANGE, MyDefine.POWER_ATK_STR_RANGE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_ATK_STR, MyDefine.POWER_ATK_STR_RANGE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_ATK_STR_RANGE, MyDefine.POWER_ATK_STR_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		return value
	case ModelConst.ATK_AGI_NEARBY: // 近身穿刺攻击力
		value := p.Get(ModelConst.ATK_AGI)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_ATK_AGI_NEARBY, MyDefine.POWER_ATK_AGI_NEARBY_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_ATK_AGI, MyDefine.POWER_ATK_AGI_NEARBY_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_ATK_AGI_NEARBY, MyDefine.POWER_ATK_AGI_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		return value
	case ModelConst.ATK_AGI_RANGE: // 远程穿刺攻击力
		value := p.Get(ModelConst.ATK_AGI)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_ATK_AGI_RANGE, MyDefine.POWER_ATK_AGI_RANGE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_ATK_AGI, MyDefine.POWER_ATK_AGI_RANGE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_ATK_AGI_RANGE, MyDefine.POWER_ATK_AGI_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		return value
	case ModelConst.DEF_STR: // 劈砍防御力
		value := 8 * p.Get(ModelConst.CON)
		value += p.BagModule().Get(EQP.DEF_STR)
		return GetPowerValue(p, value, p.AttrModule().DefStr, MyDefine.POWER_DEF_STR, MyDefine.POWER_DEF_STR_PERCENT, SKILL_TYPE.PASSIVE, 0, math.MaxInt, false)
	case ModelConst.DEF_STR_RANGE: // 远程劈砍防御力
		value := p.Get(ModelConst.DEF_STR)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_DEF_STR_RANGE, MyDefine.POWER_DEF_STR_RANGE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_DEF_STR, MyDefine.POWER_DEF_STR_RANGE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		return value + GetPowerValue(p, 0, 0, MyDefine.POWER_DEF_STR_RANGE, MyDefine.POWER_DEF_STR_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
	case ModelConst.DEF_STR_NEARBY: //近身劈砍防御力
		value := p.Get(ModelConst.DEF_STR)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_DEF_STR_NEARBY, MyDefine.POWER_DEF_STR_NEARBY_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_DEF_STR, MyDefine.POWER_DEF_STR_NEARBY_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		return value + GetPowerValue(p, 0, 0, MyDefine.POWER_DEF_STR_NEARBY, MyDefine.POWER_DEF_STR_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
	case ModelConst.DEF_AGI_RANGE: // 远程穿刺防御力
		value := p.Get(ModelConst.DEF_AGI)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_DEF_AGI_RANGE, MyDefine.POWER_DEF_AGI_RANGE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_DEF_AGI, MyDefine.POWER_DEF_AGI_RANGE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		return value + GetPowerValue(p, 0, 0, MyDefine.POWER_DEF_AGI_RANGE, MyDefine.POWER_DEF_AGI_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
	case ModelConst.DEF_AGI_NEARBY: // 近身穿刺防御力
		value := p.Get(ModelConst.DEF_AGI)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_DEF_AGI_NEARBY, MyDefine.POWER_DEF_AGI_NEARBY_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		value += GetPowerValue(p, 0, 0, MyDefine.POWER_DEF_AGI, MyDefine.POWER_DEF_AGI_NEARBY_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
		return value + GetPowerValue(p, 0, 0, MyDefine.POWER_DEF_AGI_NEARBY, MyDefine.POWER_DEF_AGI_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
	case ModelConst.DEF_AGI: // 穿刺防御力
		value := 8 * p.Get(ModelConst.CON)
		if p.BagModule() != nil {
			value += p.BagModule().Get(EQP.DEF_AGI)
		}
		return GetPowerValue(p, value, p.AttrModule().DefAgi, MyDefine.POWER_DEF_AGI, MyDefine.POWER_DEF_AGI_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_DEF), false)
	case ModelConst.DEF_MAGIC: // 魔法攻击力
		value := 2 * (2*p.Get(ModelConst.WIS) + p.Get(ModelConst.ILT))
		if p.BagModule() != nil {
			value += p.BagModule().Get(EQP.DEF_MAGIC)
		}
		return GetPowerValue(p, value, p.AttrModule().DefMagic, MyDefine.POWER_DEF_MAGIC, MyDefine.POWER_DEF_MAGIC_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_DEF), false)
	case ModelConst.DODGE: // 闪避
		value := 10 * (p.Get(ModelConst.AGI) + p.Get(ModelConst.WIS)) / 25
		return GetPowerValue(p, value, p.AttrModule().Dodge, MyDefine.POWER_DODGE, MyDefine.POWER_DODGE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.HIT_RATE: // 物理命中
		value := 0
		if p.BagModule() != nil {
			value = p.BagModule().GetForPos(EQP.HIT_RATE, PlayerBag.WEAPON_LEFT_POS)
			value = ut.Max(value, 0)
			rightHit := p.BagModule().GetForPos(EQP.HIT_RATE, PlayerBag.WEAPON_RIGHT_POS)
			if rightHit > 0 {
				if value > 0 {
					value = ut.Min(value, rightHit)
				} else {
					value = rightHit
				}
			}
			value += p.Get(ModelConst.AGI) / 5
		}
		return GetPowerValue(p, value, p.AttrModule().HitMagic, MyDefine.POWER_HITRATE, MyDefine.POWER_HITRATE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.HIT_MAGIC: // 魔法命中
		return GetPowerValue(p, 100, p.AttrModule().HitMagic, MyDefine.POWER_MAGIC_HITRATE, MyDefine.POWER_MAGIC_HITRATE_PERCENT, SKILL_TYPE.PASSIVE, int(ModelConst.MIN_HIT_MAGIC), int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.CRITICAL: // 致命点
		return GetPowerValue(p, 0, p.AttrModule().Critical, MyDefine.POWER_CRITICAL, MyDefine.POWER_CRITICAL_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.FORCE_HIT: // 强制命中
		return GetPowerValue(p, int(ModelConst.MIN_FORCE_HITRATE), p.AttrModule().ForceHit, MyDefine.POWER_HIT_FORCE, MyDefine.POWER_HIT_FORCE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_FORCE_RATE), false)
	case ModelConst.EXP_UP: // 经验加成
		return 0
	case ModelConst.WIL: // 状态抵抗
		value := (3*p.Get(ModelConst.WIS) + p.Get(ModelConst.ILT)) / 10
		return GetPowerValue(p, value, p.AttrModule().Wil, MyDefine.POWER_WIL, MyDefine.POWER_WIL_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
	case ModelConst.TOUGH: // 伤害减免
		value := (2*p.Get(ModelConst.CON) + p.Get(ModelConst.STR)) / 10
		return GetPowerValue(p, value, p.AttrModule().Tough, MyDefine.POWER_TOUGH, MyDefine.POWER_TOUGH_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
	case ModelConst.BLOCK: // 格挡
		value := p.Get(ModelConst.AGI) / 5
		return GetPowerValue(p, value, p.AttrModule().Block, MyDefine.POWER_BLOCK, MyDefine.POWER_BLOCK_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.BRK_ARMOR: // 破甲
		value := p.Get(ModelConst.AGI) + 3*p.Get(ModelConst.STR)
		return GetPowerValue(p, value, p.AttrModule().BrkArmor, MyDefine.POWER_PENETRATION, MyDefine.POWER_PENETRATION_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.MAGIC_PENETRATION: // 魔法穿透
		value := p.Get(ModelConst.WIS) + 2*p.Get(ModelConst.ILT)
		return GetPowerValue(p, value, p.AttrModule().MagicPenetration, MyDefine.POWER_MAGIC_PENETRATION, MyDefine.POWER_MAGIC_PENETRATION_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.INSIGHT: // 洞察
		value := (2*p.Get(ModelConst.WIS) + p.Get(ModelConst.ILT)) / 10
		return GetPowerValue(p, value, p.AttrModule().Insight, MyDefine.POWER_INSIGHT, MyDefine.POWER_INSIGHT_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.DEF_FIELD: // 法力护盾
		value := 2*p.Get(ModelConst.ILT) + 3*p.Get(ModelConst.WIS)
		return GetPowerValue(p, value, p.AttrModule().DefField, MyDefine.POWER_DEF_FIELD, MyDefine.POWER_DEF_FIELD_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.BACK: // 反击
		return GetPowerValue(p, 0, p.AttrModule().Back, MyDefine.POWER_BACK, MyDefine.POWER_BACK_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.MAGIC_BACK: // 魔法反馈
		return GetPowerValue(p, 0, p.AttrModule().MagicBack, MyDefine.POWER_MAGIC_BACK, MyDefine.POWER_MAGIC_BACK_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.LIFE_ABSORPTION: // 生命吸收
		return GetPowerValue(p, 0, p.AttrModule().LifeAbsorption, MyDefine.POWER_LIFE_ABSORPTION, MyDefine.POWER_LIFE_ABSORPTION_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_PROBABILITY), false)
	case ModelConst.MANA_ABSORPTION: // 法力吸收
		return GetPowerValue(p, 0, p.AttrModule().ManaAbsorption, MyDefine.POWER_MANA_ABSORPTION, MyDefine.POWER_MANA_ABSORPTION_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_PROBABILITY), false)
	case ModelConst.HEAL_RECOVERY: // 自动恢复生命
		return GetPowerValue(p, 0, p.AttrModule().HealRecovery, MyDefine.POWER_HEAL_RECOVERY, MyDefine.POWER_HEAL_RECOVERY_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.MANA_RECOVERY: // 自动恢复法力
		return GetPowerValue(p, 0, p.AttrModule().ManaRecovery, MyDefine.POWER_MANA_RECOVERY, MyDefine.POWER_MANA_RECOVERY_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.IGNORE_BACK: // 无视反击
		return GetPowerValue(p, p.AttrModule().IgnoreBack, 0, MyDefine.POWER_IGNORE_BACK, 0, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.IGNORE_MAGIC_BACK: // 无视魔法反馈
		return GetPowerValue(p, p.AttrModule().IgnoreMagicBack, 0, MyDefine.POWER_IGNORE_MAGIC_BACK, 0, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.IGNORE_BLOCK: // 无视格挡
		return GetPowerValue(p, p.AttrModule().IgnoreBlock, 0, MyDefine.POWER_IGNORE_BLOCK, 0, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.IGNORE_INSIGHT: // 无视洞察
		return GetPowerValue(p, p.AttrModule().IgnoreInsight, 0, MyDefine.POWER_IGNORE_INSIGHT, 0, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.IGNORE_WIL: // 无视状态抵抗
		return GetPowerValue(p, p.AttrModule().IgnoreWil, 0, MyDefine.POWER_IGNORE_WIL, 0, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.IGNORE_TOUCH: // 无视伤害减免
		return GetPowerValue(p, p.AttrModule().IgnoreTouch, 0, MyDefine.POWER_IGNORE_TOUCH, 0, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), false)
	case ModelConst.IGNORE_CRITICAL: // 抗致命
		return GetPowerValue(p, p.AttrModule().IgnoreCritical, 0, MyDefine.POWER_IGNORE_CRITICAL, MyDefine.POWER_IGNORE_CRITICAL_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
	case ModelConst.KEEPOUT_ATK_TIME: // 免伤护盾
		return GetPowerValue(p, 0, p.AttrModule().KeepOutAtkTime, MyDefine.POWER_KEEPOUT_ATK_TIME, 0, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_KEEPOUT_ATK_TIME), false)
	case ModelConst.CRITICAL_DAMAGE: // 致命伤害
		return GetPowerValue(p, 0, p.AttrModule().CriticalDmg, MyDefine.POWER_CRITICAL_DAMAGE, MyDefine.POWER_CRITICAL_DAMAGE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), false)
	case ModelConst.RECOVERY:
		return p.AttrModule().Recovery
	case ModelConst.ARGO:
		return p.AttrModule().Argo
	case ModelConst.BACK_MAX:
		return 70
	}

	return ret
}

func AddWeaponSkillAtkTime(p GameVo, value int, weaponType ITEM_TYPE.Type) int {
	add := 0
	switch weaponType {
	case ITEM_TYPE.WEAPON_ONEHAND_SWORD:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_SWORD_L_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_SWORD_L_ATK_TIME, true)
		add += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_SWORD_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_SWORD_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_TWOHAND_SWORD:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_SWORD_H_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_SWORD_H_ATK_TIME, true)
		add += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_SWORD_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_SWORD_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_ONEHAND_BLADE:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BLADE_L_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_BLADE_L_ATK_TIME, true)
		add += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BLADE_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_BLADE_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_TWOHAND_BLADE:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BLADE_H_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_BLADE_H_ATK_TIME, true)
		add += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BLADE_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_BLADE_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_ONEHAND_HEAVY:
	case ITEM_TYPE.WEAPON_TWOHAND_HEAVY:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_HEAVY_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_HEAVY_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_TWOHAND_STAFF:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_STAFF_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_STAFF_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_BALL:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BALL_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_BALL_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_TWOHAND_LANCE:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_LANCE_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_LANCE_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_ONEHAND_CROSSBOW:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_CROSSBOW_L_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_CROSSBOW_L_ATK_TIME, true)
		add += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BOW_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_BOW_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_TWOHAND_CROSSBOW:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_CROSSBOW_H_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_CROSSBOW_H_ATK_TIME, true)
		add += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BOW_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_BOW_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_TWOHAND_BOW:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_ARROW_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_ARROW_ATK_TIME, true)
		add += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BOW_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_BOW_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_ONEHAND_HAND:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_HAND_ITEM_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_HAND_ITEM_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_ONEHAND_GUN:
	case ITEM_TYPE.WEAPON_TWOHAND_GUN:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_GUN_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_GUN_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_ONEHAND_HAMMER:
	case ITEM_TYPE.WEAPON_TWOHAND_HAMMER:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_HAMMER_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_HAMMER_ATK_TIME, true)
	case ITEM_TYPE.WEAPON_TWOHAND_FAN:
		add = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_FAN_ATK_TIME)
		add += GetBagEquipPowerValue(p, MyDefine.POWER_FAN_ATK_TIME, true)
	default:
		add = 0
	}
	add += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_ALL_ATK_TIME)
	add += GetBagEquipPowerValue(p, MyDefine.POWER_ALL_ATK_TIME, true)
	return value + add
}

func AddWeaponSkillDamageOrDur(p GameVo, value int, pos int) int {
	bag := p.BagModule()
	if value == 0 || bag == nil {
		return value
	}
	item := bag.GetItem(pos)
	if item == nil {
		return value
	}

	percent := 0
	switch item.Config().Type {
	case ITEM_TYPE.WEAPON_ONEHAND_HEAVY:
	case ITEM_TYPE.WEAPON_TWOHAND_HEAVY:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_HEAVY_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_HEAVY_PERCENT, false)
	case ITEM_TYPE.WEAPON_TWOHAND_STAFF:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_STAFF_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_STAFF_PERCENT, false)
	case ITEM_TYPE.WEAPON_BALL:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BALL_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_BALL_PERCENT, false)
	case ITEM_TYPE.WEAPON_TWOHAND_LANCE:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_LANCE_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_LANCE_PERCENT, false)
	case ITEM_TYPE.WEAPON_TWOHAND_BOW:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_ARROW_DAMAGE_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_ARROW_DAMAGE_PERCENT, false)
		percent += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BOW_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_BOW_PERCENT, false)
	case ITEM_TYPE.WEAPON_ONEHAND_GUN:
	case ITEM_TYPE.WEAPON_TWOHAND_GUN:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_GUN_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_GUN_PERCENT, false)
	case ITEM_TYPE.WEAPON_ONEHAND_HAMMER:
	case ITEM_TYPE.WEAPON_TWOHAND_HAMMER:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_HAMMER_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_HAMMER_PERCENT, false)
	case ITEM_TYPE.WEAPON_TWOHAND_FAN:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_FAN_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_FAN_PERCENT, false)
	case ITEM_TYPE.WEAPON_ONEHAND_SWORD:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_SWORD_L_DAMAGE_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_SWORD_L_DAMAGE_PERCENT, false)
		percent += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_SWORD_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_SWORD_PERCENT, false)
	case ITEM_TYPE.WEAPON_TWOHAND_SWORD:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_SWORD_H_DAMAGE_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_SWORD_H_DAMAGE_PERCENT, false)
		percent += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_SWORD_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_SWORD_PERCENT, false)
	case ITEM_TYPE.WEAPON_ONEHAND_BLADE:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BLADE_L_DAMAGE_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_BLADE_L_DAMAGE_PERCENT, false)
		percent += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BLADE_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_BLADE_PERCENT, false)
	case ITEM_TYPE.WEAPON_TWOHAND_BLADE:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BLADE_H_DAMAGE_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_BLADE_H_DAMAGE_PERCENT, false)
		percent += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BLADE_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_BLADE_PERCENT, false)
	case ITEM_TYPE.WEAPON_ONEHAND_CROSSBOW:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_CROSSBOW_H_DAMAGE_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_CROSSBOW_H_DAMAGE_PERCENT, false)
		percent += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BOW_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_BOW_PERCENT, false)
	case ITEM_TYPE.WEAPON_TWOHAND_CROSSBOW:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_CROSSBOW_L_DAMAGE_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_CROSSBOW_L_DAMAGE_PERCENT, false)
		percent += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_BOW_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_BOW_PERCENT, false)
	case ITEM_TYPE.WEAPON_ONEHAND_HAND:
		percent = p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_HAND_ITEM_PERCENT)
		percent += GetBagEquipPowerValue(p, MyDefine.POWER_HAND_ITEM_PERCENT, false)
	default:
		percent = 0
	}
	percent += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_ALL_PERCENT)
	percent += GetBagEquipPowerValue(p, MyDefine.POWER_ALL_PERCENT, false)
	if percent == 0 {
		return 0
	}
	return value + value*percent/100
}

func GetPowerValue(p GameVo, base, add int, powerType MyDefine.POWER, powerPercentType MyDefine.POWER, skillType SKILL_TYPE.SKILL_TYPE, min, max int, weapon bool) int {
	val := base
	// 技能加成
	val += p.SkillModule().GetSkillPowerValue(skillType, powerType)
	// 技能百分比加成
	percent := p.SkillModule().GetSkillPowerValue(skillType, powerPercentType)
	// 装备加成
	val += GetPowerValueByBuffer(p, powerType)
	// buff加成
	percent += GetPowerValueByBuffer(p, powerPercentType)
	bag := p.BagModule()
	// 装备加成
	val += GetBagEquipPowerValue(p, powerType, false)
	percent += GetBagEquipPowerValue(p, powerPercentType, false)
	// 阵法技能
	if p.SkillModule().FormationSkill != nil {
		val += p.SkillModule().FormationSkill.GetPowerValue(powerType)
		percent += p.SkillModule().FormationSkill.GetPowerValue(powerPercentType)
	}
	// var c = this.playerTurnMonster;
	// null != c && (_ += c.getPowerValue(i), d += c.getPowerValue(o));

	if weapon && GetEquipWeaponType(p) == int(MyDefine.BACK_ERROR_NULL_HAND) {
		if bag != nil {
			percent += GetBagEquipPowerValue(p, MyDefine.POWER_HAND_PERCENT, false)
		}
		percent += p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_HAND_PERCENT)
	}
	val += val * percent / 100

	return ut.SumValue(val, add, min, max)
}

// 宠物使用
func GetPetPowerValue(p *Pet, base, add int, powerType MyDefine.POWER, powerPercentType MyDefine.POWER, skillType SKILL_TYPE.SKILL_TYPE, min, max int, percentMax MyDefine.POWER) int {
	val := base
	// 技能加成
	val += p.SkillModule().GetSkillPowerValue(skillType, powerType)
	// 技能百分比加成
	percent := p.SkillModule().GetSkillPowerValue(skillType, powerPercentType)
	owner := p.GetOwner()
	if owner != nil && percentMax > 0 {
		percent += GetBagEquipPowerValue(owner, percentMax, false)
		percent += owner.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, percentMax)
	}
	bag := p.BagModule()
	if bag != nil {
		val += GetBagEquipPowerValue(p, powerType, false)
		val += GetBagEquipPowerValue(p, powerPercentType, false)
	}
	// 阵法技能
	if owner != nil && owner.SkillModule().FormationSkill != nil {
		val += owner.SkillModule().FormationSkill.GetPowerValue(powerType)
		percent += owner.SkillModule().FormationSkill.GetPowerValue(powerPercentType)
	}
	if percent > 0 {
		val += val * percent / 100
	}
	return ut.SumValue(val, add, min, max)

}

func GetPetBaseValue(p *Pet, base int, baseType, percentType MyDefine.POWER, skillType SKILL_TYPE.SKILL_TYPE, min, max int, petPercentType MyDefine.POWER) int {
	val := base
	val += p.SkillModule().GetSkillPowerValue(skillType, baseType)
	percent := p.SkillModule().GetSkillPowerValue(skillType, percentType)
	owner := p.GetOwner()
	if owner != nil {
		percent += GetBagEquipPowerValue(owner, petPercentType, false)
		percent += owner.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, petPercentType)
	}
	bag := p.BagModule()
	if bag != nil {
		val += GetBagEquipPowerValue(p, baseType, false)
		val += GetBagEquipPowerValue(p, percentType, false)
	}
	// 阵法技能
	if owner != nil && owner.SkillModule().FormationSkill != nil {
		val += owner.SkillModule().FormationSkill.GetPowerValue(baseType)
		percent += owner.SkillModule().FormationSkill.GetPowerValue(petPercentType)
	}
	if percent > 0 {
		val += val * percent / 100
	}
	return ut.SumValue(val, 0, min, max)
}

// GetEquipWeaponType 获取装备的武器类型
//
// Returns:
//   - int
func GetEquipWeaponType(p GameVo) int {
	bag := p.BagModule()
	if bag == nil {
		return int(MyDefine.BACK_ERROR_NULL_HAND)
	}
	for _, pos := range []int{PlayerBag.WEAPON_LEFT_POS, PlayerBag.WEAPON_RIGHT_POS} {
		equip := bag.GetItem(pos)
		if equip != nil && equip.Durability > 0 && equip.Config().Type != ITEM_TYPE.WEAPON_ONEHAND_HAND {
			return int(equip.Config().Type)
		}
	}

	return int(MyDefine.BACK_ERROR_NULL_HAND)
}

// GetBagEquipPowerValue 获取装备对于指定属性的加成值
//
// Parameters:
//   - powerType MyDefine.POWER
//
// Returns:
//   - int
func GetBagEquipPowerValue(p GameVo, powerType MyDefine.POWER, calculateBuffer bool) int {
	bag := p.BagModule()
	if bag == nil {
		return 0
	}
	val := 0
	// 先计算装备属性
	for i := PlayerBag.EQUIP_POS_START; i < PlayerBag.EQUIP_POS_END; i++ {
		item := bag.GetItem(i)
		if item == nil {
			continue
		}
		val += item.GetPowerValue(powerType)
	}
	// 再计算套装
	itemSetData := p.GetItemSetData()
	for i := 0; i < len(itemSetData); i++ {
		bitVal := itemSetData[i]
		i++
		// 套装效果属性类型
		typ := itemSetData[i]
		i++
		// 套装效果属性值
		powerValue := itemSetData[i]
		i++
		if powerType != MyDefine.POWER(typ) {
			continue
		}
		itemSetId := bit.GetItemSetID(bitVal)
		itemSetNum := bit.GetItemSetNum(bitVal)
		if bag.GetEquipItemSetNum(itemSetId) >= itemSetNum {
			val += powerValue
		}
	}
	if calculateBuffer {
		val += GetPowerValueByBuffer(p, powerType)
	}

	return val
}

// GetPowerValueByBuffer 计算属性 药水 + 称号 + 战斗 + 国家(todo)
//
// Parameters:
//   - powerType MyDefine.POWER
//
// Returns:
//   - int
func GetPowerValueByBuffer(p GameVo, powerType MyDefine.POWER) int {
	val := 0
	attr := p.AttrModule()
	// 吃药属性
	if attr.Power != nil && attr.Power.Type == powerType {
		val += int(attr.Power.Value)
	}
	// 称号属性
	if attr.TitlePower1 != nil && attr.TitlePower1.Type == powerType {
		val += int(attr.TitlePower1.Value)
	}
	if attr.TitlePower2 != nil && attr.TitlePower2.Type == powerType {
		val += int(attr.TitlePower2.Value)
	}

	if len(attr.FightPowerList) > 0 {
		for i := 0; i < len(attr.FightPowerList); i++ {
			pw := attr.FightPowerList[i]
			if pw != nil && pw.Type == powerType {
				val += int(pw.Value)
			}
		}
	}
	return val
}
