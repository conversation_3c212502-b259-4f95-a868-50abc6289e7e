package gameStruct

import (
	"fmt"
	"world/base/cfg"
	"world/common/pbBase/ModelConst"
	"world/common/pbCross"
	"world/common/pbGame"
	"world/common/pbGame/MyDefine"
	"world/common/pbGame/SKILL_TYPE"
	ut "world/utils"

	"github.com/huyangv/vmqant/log"
)

type Pet struct {
	GameVo       `bson:"-" json:"-"`
	CfgId        int          `bson:"cfgId, omitempty"`     // 配置id
	Name         string       `bson:"name, omitempty"`      // 自定义名称
	Grow         int          `bson:"grow, omitempty"`      // 成长
	Learn        int          `bson:"learn, omitempty"`     // 领悟
	GrowLevel    int          `bson:"growLevel, omitempty"` // 成长等级
	GrowExp      int          `bson:"growExp, omitempty"`   // 成长经验
	Attr         *AttrModule  `bson:"attr, omitempty"`      // 属性
	Skill        *SkillModule `bson:"skill, omitempty"`     // 技能模块
	Age          int64        `bson:"age, omitempty"`       // 寿命
	Owner        GameVo       `bson:"-"`                    // 所属玩家
	Item         *Item        `bson:"-"`                    // 物品载体
	PotencySkill []*Skill     `bson:"-"`                    // 潜能技能，用于替换，只存在于内存
}

func (p *Pet) IsPlayer() bool          { return false }
func (p *Pet) IsPet() bool             { return true }
func (p *Pet) AttrModule() *AttrModule { return p.Attr }

// 宠物暂时没用背包
func (p *Pet) BagModule() *BagModule     { return nil }
func (p *Pet) SkillModule() *SkillModule { return p.Skill }
func (p *Pet) SetOwner(owner GameVo)     { p.Owner = owner }
func (p *Pet) GetOwner() GameVo          { return p.Owner }
func (p *Pet) GetAge() int               { return int(ut.Max(0, int64(p.Age-ut.Now())/ut.TIME_DAY)) }
func (p *Pet) GetId() int64              { return p.Item.PetId }

// 宠物暂时没有套装
func (p *Pet) GetItemSetData() []int { return nil }
func (p *Pet) Get(typ ModelConst.Type) int {
	switch typ {
	case ModelConst.PET_COLOR:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
		return int(bean.Grade)
	case ModelConst.PET_GRADE:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
		return int(bean.Grade)
	case ModelConst.PET_GROW_LEVEL:
		return p.GrowLevel
	case ModelConst.ATK_TYPE:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
		return bean.AtkType
	case ModelConst.HIT_RATE:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
		return GetPetPowerValue(p, bean.HitRate, p.AttrModule().HitRate, MyDefine.POWER_HITRATE, MyDefine.POWER_HITRATE_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_NONE)
	case ModelConst.HIT_MAGIC:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
		return GetPetPowerValue(p, bean.HitRate, p.AttrModule().HitMagic, MyDefine.POWER_MAGIC_HITRATE, MyDefine.POWER_MAGIC_HITRATE_PERCENT, SKILL_TYPE.PASSIVE, int(ModelConst.MIN_HIT_MAGIC), int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_NONE)
	case ModelConst.LEFT_ATK_TIME:
		fallthrough
	case ModelConst.ATK_TIME:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
		atkTime := ut.SumValue(bean.PetAtkTime+1, p.AttrModule().AtkTime, int(ModelConst.MIN_HIT_TIME), int(ModelConst.MAX_HIT_TIME))
		return atkTime + p.SkillModule().GetSkillPowerValue(SKILL_TYPE.PASSIVE, MyDefine.POWER_ALL_ATK_TIME)
	case ModelConst.RIGHT_ATK_TIME:
		return 0
	case ModelConst.LEFT_ATK_MIN:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
		atkMin := GetPetPowerValue(p, bean.AtkMin, 0, MyDefine.POWER_NONE, MyDefine.POWER_NONE, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), MyDefine.POWER_PET_DAMAGE)
		return ut.SumValue(atkMin, 0, 0, int(ModelConst.MAX_ATK))
	case ModelConst.LEFT_ATK_MAX:
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
		atkMax := GetPetPowerValue(p, bean.AtkMax, 0, MyDefine.POWER_NONE, MyDefine.POWER_NONE, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_OTHER_ATTRIBUTE), MyDefine.POWER_PET_DAMAGE)
		return ut.SumValue(atkMax, 0, 0, int(ModelConst.MAX_ATK))
	case ModelConst.RIGHT_ATK_MIN:
		return 0
	case ModelConst.RIGHT_ATK_MAX:
		return 0
	case ModelConst.HPMAX:
		hpMax := 65*p.Get(ModelConst.CON)/10 + 3*p.Get(ModelConst.STR) + 100 + 20*(p.Get(ModelConst.LEVEL)-1)
		hpMax = GetPetPowerValue(p, hpMax, 0, MyDefine.POWER_HPMAX, MyDefine.POWER_HPMAX_PERCENT, SKILL_TYPE.PASSIVE, 1, int(ModelConst.MAX_PLAYER_HP), MyDefine.POWER_PET_HPMAX_PERCENT)
		return hpMax
	case ModelConst.MPMAX:
		mpMax := 5*p.Get(ModelConst.ILT) + 2*p.Get(ModelConst.WIS) + 40 + 5*(p.Get(ModelConst.LEVEL)-1)
		mpMax = GetPetPowerValue(p, mpMax, 0, MyDefine.POWER_MPMAX, MyDefine.POWER_MPMAX_PERCENT, SKILL_TYPE.PASSIVE, 1, int(ModelConst.MAX_PLAYER_MP), MyDefine.POWER_PET_MPMAX_PERCENT)
		return mpMax
	case ModelConst.STR: // 力量
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
		return GetPetBaseValue(p, bean.Str*p.GrowLevel, MyDefine.POWER_STR, MyDefine.POWER_STR_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_PET_STR_PERCENT)
	case ModelConst.CON: // 体质
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
		return GetPetBaseValue(p, bean.Con*p.GrowLevel, MyDefine.POWER_CON, MyDefine.POWER_CON_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_PET_CON_PERCENT)
	case ModelConst.AGI: // 敏捷
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
		return GetPetBaseValue(p, bean.Agi*p.GrowLevel, MyDefine.POWER_AGI, MyDefine.POWER_AGI_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_PET_AGI_PERCENT)
	case ModelConst.ILT: // 智力
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
		return GetPetBaseValue(p, bean.Ilt*p.GrowLevel, MyDefine.POWER_ILT, MyDefine.POWER_ILT_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_PET_ILT_PERCENT)
	case ModelConst.WIS: // 感知
		bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
		return GetPetBaseValue(p, bean.Wis*p.GrowLevel, MyDefine.POWER_WIS, MyDefine.POWER_WIS_PERCENT, SKILL_TYPE.PASSIVE, 0, int(ModelConst.MAX_BASE_ATTRIBUTE), MyDefine.POWER_PET_WIS_PERCENT)
	}
	return baseGet(p, typ)
}

func (p *Pet) InitAllModule() {
	p.initAttrModule().BindVo(p)
	p.initSkillModule().BindVo(p)
}

// GetJson 获取宠物配置
func (p *Pet) GetJson() *cfg.Pet[int] {
	bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
	return bean
}

func (p *Pet) initAttrModule() *AttrModule {
	if p.Attr == nil {
		p.Attr = &AttrModule{
			Level: 1,
			Exp:   0,
			Hp:    100,
			Mp:    50,
		}
	}
	return p.Attr
}
func (p *Pet) initSkillModule() *SkillModule {
	if p.Skill == nil {
		p.Skill = &SkillModule{
			Sp:                10,
			List:              make(map[int]*Skill),
			Cnt:               14,
			AutoSkillID:       make([]int, 4),
			ActiveAutoSkillID: -1,
		}
	}
	return p.Skill
}

func (p *Pet) ToPb() *pbGame.PetData {
	if p == nil {
		return nil
	}
	info := &pbGame.PetData{
		CfgId:     int32(p.CfgId),
		Name:      p.Name,
		Grow:      int32(p.Grow),
		Learn:     int32(p.Learn),
		GrowLevel: int32(p.GrowLevel),
		Attr:      p.Attr.ToPb(),
		Skill:     p.Skill.ToPb(),
		Age:       p.Age,
		GrowExp:   int32(p.GrowExp),
		Id:        p.GetId(),
	}
	return info
}

func (p *Pet) ToCrossSimplePb() *pbCross.CrossSimplePet {
	if p == nil {
		return nil
	}
	return &pbCross.CrossSimplePet{
		CfgId: int32(p.CfgId),
		Id:    p.GetId(),
		Name:  p.Name,
		Age:   p.Age,
	}
}

// 宠物重生
func (p *Pet) Rebirth() {
	p.Age = 100*ut.TIME_DAY + ut.Now()
	p.GrowLevel = 1
	p.GrowExp = 0
	p.Attr = nil
	p.Skill = nil
	p.InitAllModule()
	// 固定天赋技能
	bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
	for _, v := range bean.BornSkill {
		skillBean, _ := cfg.ContainerSkill.GetBeanByUnique(fmt.Sprintf("%d-%d", v.Id, 1))
		if skillBean == nil {
			panic(fmt.Sprintf("找不到技能 : %d", v.Id))
		}
		p.Skill.List[v.Id] = &Skill{
			Id:        v.Id,
			BaseLevel: 1,
		}
	}

	ResumeHPMP(p)
}

// 宠物洗髓
func (p *Pet) Create(plr *Player) {
	bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
	// 重塑成长和领悟值
	p.Learn = bean.RandomGetLearn()
	p.Grow = bean.RandomGetGrow()
	// 重生其他属性
	p.Rebirth()
	p.SetOwner(plr)
}

// GetTalentSkill 获取天赋技能 2个
func (p *Pet) GetTalentSkill() []*Skill {
	bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
	ary := make([]*Skill, 0)
	for _, v := range bean.BornSkill {
		s, ok := p.Skill.List[v.Id]
		if !ok {
			log.Error(fmt.Sprintf("宠物不存在天赋技能？？？？%d", v.Id))
			continue
		}
		ary = append(ary, s)
	}
	return ary
}

// GetLearnSkill 获取2个领悟技能,宠物有2个升级领悟技能和2个潜能领悟技能
//
// Parameters:
//   - potency bool 是不是潜能
//
// Returns:
//   - []*Skill
func (p *Pet) GetLearnSkill(potency bool) []*Skill {
	bean, _ := cfg.ContainerPet.GetBeanByUnique(p.CfgId)
	ary := make([]*Skill, 0)
	for _, v := range bean.LearnSkill {
		s, ok := p.Skill.List[v.Id]
		if !ok {
			continue
		}
		if potency && !s.IsLearnByBook {
			continue
		}
		ary = append(ary, s)
	}
	return ary
}
