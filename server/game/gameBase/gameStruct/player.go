package gameStruct

import (
	"context"
	"errors"
	"time"
	"world/base/cfg"
	"world/base/enum"
	"world/base/enum/ModelHelper"
	"world/base/enum/key"
	"world/base/structs"
	"world/common/pbBase/Camp"
	"world/common/pbBase/Job"
	"world/common/pbBase/ModelConst"
	"world/common/pbBase/Sex"
	"world/common/pbCross"
	"world/common/pbGame"
	"world/common/pb_helper"
	"world/common/router"
	"world/db"
	"world/game/gameBase/types/PlayerBag"
	ut "world/utils"
	"world/utils/bit"

	"github.com/huyangv/vmqant/module"
	"github.com/samber/lo"
	"google.golang.org/protobuf/reflect/protoreflect"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Player struct {
	GameVo          `bson:"-" json:"-"`
	Session         gate.Session                 `bson:"-"`               // 内存数据,会话通道
	Destroy         bool                         `bson:"-"`               // 内部数据，如果这个字段为true，说明数据已不可用，不处理后续请求
	GameId          int                          `bson:"gameId"`          // 游戏id
	Id              string                       `bson:"id"`              // 这是用户id 表示这个角色属于哪个用户
	CreateTime      int64                        `bson:"createTime"`      // 创建时间
	LastLoginTime   int64                        `bson:"lastLoginTime"`   // 上一次登录时间
	LastLogoutTime  int64                        `bson:"lastLogoutTime"`  // 上一次登出时间
	LastUpdateTime  int64                        `bson:"lastUpdateTime"`  // 上一次刷新时间
	GM              bool                         `bson:"gm"`              // 是不是gm
	TotalOnlineTime int64                        `bson:"totalOnlineTime"` // 总在线时长
	MapId           int                          `bson:"mapId"`           // 地图id
	X               int                          `bson:"x"`               // 地图x
	Y               int                          `bson:"y"`               // 地图y
	Name            string                       `bson:"name"`            // 角色名
	DeleteEndTime   int64                        `bson:"deleteEndTime"`   // 到期删除的时间
	Attr            *AttrModule                  `bson:"attr"`            // 属性模块
	Bag             *BagModule                   `bson:"bag"`             // 背包模块
	Task            *TaskModule                  `bson:"task"`            // 任务模块
	OfflineTask     *OfflineTaskModule           `bson:"offlineTask"`     // 离线任务模块
	Setting         int64                        `bson:"setting"`         // 设置
	Mode            int                          `bson:"mode"`            // 当前状态模型
	PartnerId       int                          `bson:"partnerId"`       // 伴侣id
	ItemSetData     []int                        `bson:"itemSetData"`     // 玩家套装数据
	Skill           *SkillModule                 `bson:"skill"`           // 技能模块
	KillCount       int                          `bson:"killCount"`       // 杀怪数
	PetId           int64                        `bson:"petId"`           // 上阵的宠物
	Leader          int                          `bson:"-"`               // 队长id
	EventId         int                          `bson:"-"`               // 事件id
	playerEvent     map[int]*pbCross.PlayerEvent `bson:"-"`               // 普通事件列表
}

func (p *Player) IsPlayer() bool            { return true }
func (p *Player) IsPet() bool               { return false }
func (p *Player) AttrModule() *AttrModule   { return p.Attr }
func (p *Player) BagModule() *BagModule     { return p.Bag }
func (p *Player) SkillModule() *SkillModule { return p.Skill }
func (p *Player) GetItemSetData() []int     { return p.ItemSetData }
func (p *Player) GetPet(petId ...int64) *Pet {
	findId := p.PetId
	if len(petId) > 0 {
		findId = petId[0]
	}
	ary := p.Bag.GetPetItemAry(func(item *Item) bool {
		return item.PetId == findId
	})
	if len(ary) == 1 {
		return ary[0].PetItem
	}
	return nil
}
func (p *Player) Get(typ ModelConst.Type) int {
	switch typ {
	case ModelConst.KILL_COUNT:
		return p.KillCount // 杀怪数
	}
	return baseGet(p, typ)
}

func (p *Player) GetPlayerEvent(id int, remove bool) *pbCross.PlayerEvent {
	if p.playerEvent == nil {
		return nil
	}
	v := p.playerEvent[id]
	if remove {
		delete(p.playerEvent, id)
	}
	return v
}

func (p *Player) AddPlayerEvent(data *pbCross.PlayerEvent) {
	if p.playerEvent == nil {
		p.playerEvent = make(map[int]*pbCross.PlayerEvent)
	}
	p.playerEvent[p.EventId] = data
	data.EventId = int32(p.EventId)
	p.EventId++
	if len(p.playerEvent) > 20 {
		for id := range p.playerEvent {
			if id < p.EventId-20 {
				delete(p.playerEvent, id)
			}
		}
	}
	p.TellPlayerMsg(router.S2CScenePlayerEventMessage, &pbGame.S2C_ScenePlayerEventMessage{Event: data})
}

// GetSid 获取玩家所在区服
func (p *Player) GetSid() int {
	return cast.ToInt(p.Session.Get(key.PlayerSid))
}

// GetClientVersion 获取客户端版本
func (p *Player) GetClientVersion() string {
	return p.Session.Get(key.ClientVersion)
}

// GetOs 获取客户端版本
func (p *Player) GetOs() string {
	return p.Session.Get(key.OS)
}

// 初始化所有模块
func (p *Player) InitAllModule() {
	p.initAttrModule().BindVo(p)
	p.initBagModule().BindVo(p)
	p.initTaskModule().BindVo(p)
	p.initOfflineTaskModule().BindVo(p)
	p.initSkillModule().BindVo(p)
}

// 属性模块
func (p *Player) initAttrModule() *AttrModule {
	if p.Attr == nil {
		miscObj := cfg.ContainerMisc_C.GetObj()
		p.Attr = &AttrModule{
			Level: 1,
			Exp:   0,
			Hp:    100,
			Mp:    50,
			Cp:    miscObj.GetAttributePoint(),
		}
	}
	return p.Attr
}

// 背包模块
func (p *Player) initBagModule() *BagModule {
	if p.Bag == nil {
		bag := &BagModule{}
		bag.BindVo(p)
		p.Bag = bag
		obj := cfg.ContainerMisc_C.GetObj()
		bag.BagSize = obj.Bag.BagNum
		bag.StoreSize = obj.Bag.StoreNum
		bag.Store = make([]*Item, PlayerBag.MAX_BAG_SIZE+PlayerBag.MAX_STORE_SIZE+PlayerBag.MAX_VIP9STORE_SIZE+PlayerBag.MAX_HIGHT_VIP_9_STORE_SIZE)
	}
	// 为宠物绑定玩家和物品
	for _, item := range p.Bag.GetPetItemAry(nil) {
		if item.PetItem != nil {
			item.PetItem.SetOwner(p)
			item.PetItem.Item = item
		}
	}

	return p.Bag
}

// 技能模块
func (p *Player) initSkillModule() *SkillModule {
	if p.Skill == nil {
		cnt := cfg.ContainerMisc_C.GetObj().Skill.DefaultSkillCnt
		p.Skill = &SkillModule{
			Sp:                10,
			List:              make(map[int]*Skill),
			Cnt:               cnt,
			AutoSkillID:       make([]int, 4),
			ActiveAutoSkillID: -1,
		}
	}
	return p.Skill
}

// Online 会重新绑定会话
func (p *Player) Online(session gate.Session) {
	if p.Session != nil && p.Session == session {
		return
	}
	// 关闭之前与登录服的绑定
	// if session.GetUserID() != "" {
	// 	log.Debug("session级别的切换玩家会话通道？？")
	// 	session.UnBind()
	// }

	p.Session = session
	now := p.GetNowTime()
	p.LastLoginTime = now
	p.InitAllModule()
	if p.IsStatusBit(ModelConst.STATUS_NEW) {

	}
	//初始化一些时间相关的字段
	p.CheckAndUpdateDaily()
	// 判断是不是重连
	_, ok := p.Session.Load(enum.IsReconnect)
	if ok {

	}
}

// CheckAndUpdateDaily 刷新每日相关数据
func (p *Player) CheckAndUpdateDaily() bool {
	if !p.CheckRefreshDaily(p.LastUpdateTime, 1) {
		return false
	}
	p.LastUpdateTime = p.GetNowTime()

	log.Info("[%s] Reset daily", p.GameId)
	return true
}

// GetNowTime 获取真实时间
func (p *Player) GetNowTime() int64 {
	return ut.Now()
}

// Offline 角色离线
func (p *Player) Offline() {
	if p.Session != nil {
		p.DelNodeId()
		p.Session.UnBind()
		p.Session.Push()
		p.Session.Close()
		p.Session = nil
	}
}

// IsOnline 获取玩家是否在线
func (p *Player) IsOnline() bool {
	return p.Session != nil
}

// IsValid 判断数据还是否可用
func (p *Player) IsValid() bool {
	return !p.Destroy
}

// TryGetPlayerFromDbByGameId
/*
 * @description 从数据库获取玩家数据
 * @param gameId 玩家游戏id
 * @return plr
 * @return err
 */
func TryGetPlayerFromDbByGameId(gameId int) (plr *Player, err error) {
	res := db.GetCollection(db.PLAYER).FindOne(context.TODO(), &bson.M{
		"gameId": gameId,
	})
	return DecodeSinglePlr(res)
}

// TryGetPlayerFromDbByRoleId
/*
 * @description 从数据库获取玩家数据
 * @param roleId 玩家角色id
 * @return plr
 * @return err
 */
func TryGetPlayerFromDbByRoleId(roleId string) (plr *Player, err error) {
	mongoId, err := ut.NewMongoIdFrom(roleId)
	if err != nil {
		return nil, err
	}
	res := db.GetCollection(db.PLAYER).FindOne(context.TODO(), &bson.M{
		"_id": mongoId.ObjectId(),
	})
	return DecodeSinglePlr(res)
}

func DecodeSinglePlr(res *mongo.SingleResult) (plr *Player, err error) {
	if res == nil {
		return
	}
	plr = &Player{}

	var result bson.M
	err = res.Decode(&result)
	if err != nil {
		return nil, err
	}
	err = res.Decode(plr)
	if err != nil {
		return nil, err
	}

	// 补全id信息
	// plr.Pid = result["_id"].(primitive.ObjectID).Hex()
	// plr.PMid, _ = ut.NewMongoIdFrom(plr.Pid)
	return
}

// NewPlayer 创建一个角色，需要指定所属账号id
func NewPlayer(id, name string, sex Sex.Type, job Job.Type, race Camp.Type, model int32) (plr *Player, err error) {
	if ut.IsEmpty(id) {
		return nil, errors.New("创建角色失败，必须指定所属账号id")
	}
	now := ut.Now()
	plr = &Player{
		Id:            id,
		CreateTime:    now,
		LastLoginTime: now,
		GameId:        structs.GetNextPlayerId(),
	}
	log.Info("CreateRole: id:%s, name:%s, gameId:%d", id, name, plr.GameId)
	plr.Name = name
	attrMod := plr.initAttrModule()
	attrMod.Icon1 = 0
	attrMod.Icon2 = 0
	attrMod.Icon3 = 0

	// 标记为新玩家
	plr.SetStatusBit(ModelConst.STATUS_NEW)
	// 设置基本样式
	attrMod.SetSex(sex)
	attrMod.SetJob(job)
	attrMod.SetRace(race)
	attrMod.SetFeet(1)
	attrMod.SetHand(1)
	// 根据阵营设置地图
	plr.MapId = lo.If[int](race == 0, 303).Else(302)
	plr.X = 15
	plr.Y = 16

	// 根据造型计算发型
	switch model {
	case 0:
		attrMod.SetHairStyle(ModelHelper.GetCreateHair(job, sex))
		attrMod.SetHairColor(ModelHelper.GetCreateHairColor(job, sex))
		attrMod.SetFaceStyle(1)
	case 1:
		attrMod.SetHairStyle(2)
		attrMod.SetHairColor(0)
		attrMod.SetFaceStyle(1)
	case 2:
		attrMod.SetHairStyle(3)
		attrMod.SetHairColor(0)
		attrMod.SetFaceStyle(2)
	default:
		attrMod.SetHairStyle(1)
		attrMod.SetHairColor(0)
		attrMod.SetFaceStyle(1)
	}
	// 初始化属性模块 因为里面有部分必须属性
	plr.initAttrModule()

	// 插入数据库
	_, err = db.PLAYER.GetCollection().InsertOne(context.TODO(), plr)
	if err != nil {
		if !mongo.IsDuplicateKeyError(err) {
			log.Error("NewPlayer Error:%s", err.Error())
		}
		return nil, err
	}
	// objId := result.InsertedID.(primitive.ObjectID)
	// plr.Pid = objId.Hex()
	// plr.PMid, _ = ut.NewMongoIdFrom(plr.Pid)
	return
}

func (p *Player) ReadySave() {
	// 同步一些字段的值
	now := p.GetNowTime()
	p.TotalOnlineTime += now - p.LastLoginTime
	p.LastLogoutTime = now
}

// Save 全字段save
func (p *Player) Save() {
	p.ReadySave()
	p.SaveToDb(ut.FieldToBsonAuto(p))
}

func (p *Player) GetDefine() *db.TableDef {
	return db.PLAYER
}

func (p *Player) GetUnique() string {
	return p.Id
}

// 删除上线的节点信息
func (p *Player) DelNodeId() {
	db.GetRedis().Del(context.TODO(), db.RedisKeySessionNodeId(p.Id))
	db.GetRedis().Del(context.TODO(), db.RedisKeySessionNodeByGameId(p.GameId))
}

func (p *Player) BulkSaveOperation() *mongo.UpdateOneModel {
	// bsonAuto := ut.FieldToBsonAuto(p)
	// mongoId, _ := ut.NewMongoIdFrom(p.Pid)
	// filter := &bson.M{"_id": mongoId}
	// update := &bson.M{"$set": &bsonAuto}
	// operation := mongo.NewUpdateOneModel()
	// operation.SetFilter(filter)
	// operation.SetUpdate(update)
	// operation.SetUpsert(true)
	// return operation
	return nil
}

// SaveToDb 轻量级分字段save
func (p *Player) SaveToDb(v bson.M) {
	if len(v) == 0 {
		log.Warning("player:%s - SaveToDb,but v is empty :%v", p.GameId, v)
		return
	}
	sTime := time.Now()
	_, err := db.PLAYER.GetCollection().UpdateOne(context.TODO(), &bson.M{
		"gameId": p.GameId,
	}, &bson.M{
		"$set": &v,
	}, options.Update().SetUpsert(true))
	if err != nil {
		log.Error("player:%d - SaveToDb:%v", p.GameId, err.Error())
	}
	log.Info("player:%d - SaveToDb ,during :%fs", p.GameId, time.Since(sTime).Seconds())
}

func (p *Player) TellPlayerMsg(topic string, msg protoreflect.ProtoMessage) bool {
	if p.Session == nil {
		return false
	}
	err := p.Session.SendNR(topic, pb_helper.ProtoMarshalForce(msg))
	if err == "" {
		return true
	}
	log.Error("TellPlayerMsg 发送给客户端消息时出错:%s", err)
	return false
}

// CheckRefreshDaily
/*
 * @description 检查是否刷新每日数据
 * @param lastTime
 * @param day
 * @return bool
 */
func (p *Player) CheckRefreshDaily(lastTime int64, day int) bool {
	return p.GetToDaySurplusTime(lastTime, day) <= 0
}

// GetToDaySurplusTime
/*
 * @description 获取到今天剩余的时间
 * @param lastTime
 * @param day
 * @return int
 */
func (p *Player) GetToDaySurplusTime(lastTime int64, day int) int64 {
	now := p.GetNowTime()
	refreshTime := ut.DateZeroTime(lastTime) //下次刷新时间
	refreshTime += ut.TIME_DAY * int64(day)
	return refreshTime - now
}

// ToPbSimplePlayerInfo
/*
 * @description 转换为简要角色数据
 * @return *pb.SimplePlayerInfo
 */
func (p *Player) ToPbSimplePlayerInfo() *pbGame.SimplePlayerInfo {
	return &pbGame.SimplePlayerInfo{
		Id:            int32(p.GameId),
		MapId:         int32(p.MapId),
		X:             int32(p.X),
		Y:             int32(p.Y),
		Name:          p.Name,
		Attr:          p.Attr.ToSimplePb(),
		DeleteEndTime: p.DeleteEndTime,
		OfflineTask:   p.OfflineTask.ToPb(),
	}
}

// ToPb
/*
 * @description 转换为pb数据
 * @return *pb.PlayerInfo
 */
func (p *Player) ToPb() *pbGame.PlayerInfo {
	if p == nil {
		return nil
	}
	return &pbGame.PlayerInfo{
		Id:    int32(p.GameId),
		MapId: int32(p.MapId),
		X:     int32(p.X),
		Y:     int32(p.Y),
		Name:  p.Name,
		Attr:  p.Attr.ToPb(),
		Bag:   nil,
		Task:  p.Task.ToPb(),
		Skill: p.Skill.ToPb(),
		PetId: p.PetId,
	}
}

func (p *Player) ToCrossSimplePb(app module.RPCModule) *pbCross.CrossSimplePlayer {
	return &pbCross.CrossSimplePlayer{
		From:        app.GetServerID(),
		Id:          p.Id,
		GameId:      int32(p.GameId),
		MapId:       int32(p.MapId),
		X:           int32(p.X),
		Y:           int32(p.Y),
		Name:        p.Name,
		Icon1:       p.Attr.Icon1,
		Icon2:       p.Attr.Icon2,
		Icon3:       p.Attr.Icon3,
		Level:       int32(p.Attr.Level),
		Level2:      int32(p.Attr.Level2),
		Title:       "世界新手",
		Setting:     p.Setting,
		Status:      p.Attr.Status,
		Mode:        int32(p.Mode),
		ShopName:    "",
		CountryName: "",
		VipLv:       int32(p.Attr.VipLv),
		VipLvMax:    int32(p.Attr.VipLvMax),
		Pet:         p.GetPet(p.PetId).ToCrossSimplePb(),
	}
}

// IsStatusBit
/*
 * @description 判断玩家状态是否包含状态
 * @param bit
 */
func (p *Player) IsStatusBit(bit ModelConst.Type) bool {
	return p.Attr.IsStatusBit(bit)
}

// SetStatusBit
/*
 * @description 设置玩家状态
 * @param bit
 */
func (p *Player) SetStatusBit(bit ModelConst.Type) {
	p.Attr.SetStatusBit(bit)
}

// ClearStatusBit
/*
 * @description 清除玩家状态
 * @param bit
 */
func (p *Player) ClearStatusBit(bit ModelConst.Type) {
	p.Attr.ClearStatusBit(bit)
}

// GetName 获取名字
func (p *Player) GetName() string {
	return p.Name
}

// GetVipLevel 当前vip等级
//
// Returns:
//   - int
func (p *Player) GetVipLevel() int {
	return p.Attr.VipLv
}

// GetVipLevel2 历史最高vip等级
//
// Returns:
//   - int
func (p *Player) GetVipLevel2() int {
	return p.Attr.VipLvMax
}

// IsVipOverDue vip是否过期
//
// Returns:
//   - bool
func (p *Player) IsVipOverDue() bool {
	bag := p.Bag
	if bag == nil {
		return true
	}
	item := bag.GetItem(PlayerBag.SPIRIT_POS)
	if item == nil {
		return true
	}
	return item.IsExpired()
}

// UpdateItemSetData 更新玩家套装数据
func (p *Player) UpdateItemSetData() {
	bag := p.Bag
	if bag == nil {
		return
	}
	info := make(map[int]int)
	for i := PlayerBag.EQUIP_POS_START; i < PlayerBag.EQUIP_POS_END; i++ {
		item := bag.GetItem(i)
		if item == nil {
			continue
		}
		setId := item.GetItemSetID()
		if setId == 0 {
			continue
		}
		num := bag.GetEquipItemSetNum(setId)
		info[setId] = num
	}

	if len(info) == 0 {
		return
	}

	ary := make([]int, 0)
	for setId, cnt := range info {
		suit, _ := cfg.ContainerSuit.GetBeanByUnique(setId)
		if suit == nil {
			log.Error("未能找到套装配置:%d", setId)
			continue
		}
		suitBit := suit.Bit
		for i := 0; i < len(suitBit); {
			reqCnt := suitBit[i]
			i++
			powerType := suitBit[i]
			i++
			powerValue := suitBit[i]
			i++
			vipLvReq := suitBit[i]
			i++
			open := suitBit[i]
			i++
			// TODO 检查vip等级
			if cnt >= reqCnt {
				if open > 0 || p.GetVipLevel() >= vipLvReq {
					// 将套装id和套装达成数量打包成一个数字
					id := bit.SetItemSetDataKey(setId, reqCnt)
					ary = append(ary, id)
					ary = append(ary, powerType)
					ary = append(ary, powerValue)
				}
			}
		}
	}
	p.ItemSetData = ary
}

// UpdateIcon 更新玩家icon数据
func (p *Player) UpdateIcon() {
	bag := p.Bag
	attr := p.Attr
	if bag == nil || attr == nil {
		return
	}

	// 清理样式
	bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HAND_STYLE, ModelConst.OFFSET_HAND_STYLE)))
	bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HAND_ADD, ModelConst.OFFSET_HAND_ADD+bit.HIGHT)))
	bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HAND_COLOR, ModelConst.OFFSET_HAND_COLOR)))
	bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_FEET_STYLE, ModelConst.OFFSET_FEET_STYLE)))
	bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_FEET_ADD, ModelConst.OFFSET_FEET_ADD+bit.HIGHT)))
	bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_FEET_COLOR, ModelConst.OFFSET_FEET_COLOR)))

	ut.SetHandAndFeet(&attr.Icon1)
	bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HELMET_STYLE, ModelConst.OFFSET_HELMET_STYLE)))
	bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HELMET_ADD, ModelConst.OFFSET_HELMET_ADD+bit.HIGHT)))
	bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HELMET_COLOR, ModelConst.OFFSET_HELMET_COLOR)))

	// 头盔
	tmpEquip := bag.GetItem(PlayerBag.ARMOR_HEAD_POS)
	if tmpEquip != nil && tmpEquip.Config().Icon > 0 {
		icon := tmpEquip.Config().Icon - int(ModelConst.START_HELMET)
		if icon > 0 {
			i := 1 + ((icon-1)/4 | 0)
			o := (icon - 1) % 4
			bit.OrAssign(&attr.Icon1, bit.ShiftLeft(bit.And(i, ModelConst.LEN_HELMET_STYLE), ModelConst.OFFSET_HELMET_STYLE))
			bit.OrAssign(&attr.Icon1, bit.ShiftLeft(bit.And(o, ModelConst.LEN_HELMET_COLOR), ModelConst.OFFSET_HELMET_COLOR))
			n := bit.ShiftRight(i, bit.GetBitNum(ModelConst.LEN_HELMET_STYLE))
			bit.OrAssign(&attr.Icon1, bit.ShiftLeft(bit.And(n, ModelConst.LEN_HELMET_ADD), ModelConst.OFFSET_HELMET_ADD+bit.HIGHT))
		}
	}

	// 肩部
	bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_SHOULDER_STYLE, ModelConst.OFFSET_SHOULDER_STYLE)))
	bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_SHOULDER_COLOR, ModelConst.OFFSET_SHOULDER_COLOR)))
	bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_SHOULDER_ADD, ModelConst.OFFSET_SHOULDER_ADD)))
	bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_SHOULDER_ADD_2, ModelConst.OFFSET_SHOULDER_ADD_2+bit.HIGHT)))
	tmpEquip = bag.GetItem(PlayerBag.ARMOR_SHOULDER_POS)

	if tmpEquip != nil && tmpEquip.Config().Icon > 0 {
		icon := tmpEquip.Config().Icon - int(ModelConst.START_SHOULDER)
		if icon > 0 {
			i := 1 + ((icon-1)/8 | 0)
			o := (icon - 1) % 8
			bit.OrAssign(&attr.Icon2, bit.ShiftLeft(bit.And(i, ModelConst.LEN_SHOULDER_STYLE), ModelConst.OFFSET_SHOULDER_STYLE))
			bit.OrAssign(&attr.Icon2, bit.ShiftLeft(bit.And(o, ModelConst.LEN_SHOULDER_COLOR), ModelConst.OFFSET_SHOULDER_COLOR))
			n := bit.ShiftRight(i, bit.GetBitNum(ModelConst.LEN_SHOULDER_STYLE))
			bit.OrAssign(&attr.Icon2, bit.ShiftLeft(bit.And(n, ModelConst.LEN_SHOULDER_ADD), ModelConst.OFFSET_SHOULDER_ADD))
			r := bit.ShiftRight(i, bit.GetBitNum(ModelConst.LEN_SHOULDER_STYLE)+bit.GetBitNum(ModelConst.LEN_SHOULDER_ADD))
			bit.OrAssign(&attr.Icon2, bit.ShiftLeft(bit.And(r, ModelConst.LEN_SHOULDER_ADD), ModelConst.OFFSET_SHOULDER_ADD+bit.HIGHT))
		}
	}

	// 武器闪光
	weaponFlashType := 0
	// 左手武器
	bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_WEAPON_FLASH, ModelConst.OFFSET_WEAPON_FLASH)))
	bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_LWEAPON_STYLE, ModelConst.OFFSET_LWEAPON_STYLE)))
	bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_LWEAPON_COLOR, ModelConst.OFFSET_LWEAPON_COLOR)))
	bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_LWEAPON_ADD, ModelConst.OFFSET_LWEAPON_ADD+bit.HIGHT)))
	tmpEquip = bag.GetItem(PlayerBag.WEAPON_LEFT_POS)
	if tmpEquip != nil && tmpEquip.Config().Icon > 0 {
		equipIcon := tmpEquip.Config().Icon
		icon := equipIcon - int(ModelConst.START_WEAPON)
		if equipIcon > 30000 {
			icon = equipIcon - int(ModelConst.START_WEAPON_ADD1)
		}

		if icon > 0 {
			i := 1 + ((icon-1)/4 | 0)
			o := (icon - 1) % 4

			bit.OrAssign(&attr.Icon2, bit.ShiftLeft(bit.And(i, ModelConst.LEN_LWEAPON_STYLE), ModelConst.OFFSET_LWEAPON_STYLE))
			bit.OrAssign(&attr.Icon2, bit.ShiftLeft(bit.And(o, ModelConst.LEN_LWEAPON_COLOR), ModelConst.OFFSET_LWEAPON_COLOR))
			n := bit.ShiftRight(i, bit.GetBitNum(ModelConst.LEN_LWEAPON_STYLE))
			bit.OrAssign(&attr.Icon2, bit.ShiftLeft(bit.And(n, ModelConst.LEN_LWEAPON_ADD), ModelConst.OFFSET_LWEAPON_ADD+bit.HIGHT))
			weaponFlashType = tmpEquip.GetWeaponFlashType()
		}
	}

	// 右手武器
	bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_RWEAPON_STYLE, ModelConst.OFFSET_RWEAPON_STYLE)))
	bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_RWEAPON_COLOR, ModelConst.OFFSET_RWEAPON_COLOR)))
	bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_RWEAPON_ADD, ModelConst.OFFSET_RWEAPON_ADD+bit.HIGHT)))
	tmpEquip = bag.GetItem(PlayerBag.WEAPON_RIGHT_POS)
	if tmpEquip != nil && tmpEquip.Config().Icon > 0 {
		equipIcon := tmpEquip.Config().Icon
		icon := equipIcon - int(ModelConst.START_WEAPON)
		if equipIcon > 30000 {
			icon = equipIcon - int(ModelConst.START_WEAPON_ADD1)
		}

		if icon > 0 {
			i := 1 + ((icon-1)/4 | 0)
			o := (icon - 1) % 4
			bit.OrAssign(&attr.Icon2, bit.ShiftLeft(bit.And(i, ModelConst.LEN_RWEAPON_STYLE), ModelConst.OFFSET_RWEAPON_STYLE))
			bit.OrAssign(&attr.Icon2, bit.ShiftLeft(bit.And(o, ModelConst.LEN_RWEAPON_COLOR), ModelConst.OFFSET_RWEAPON_COLOR))
			n := bit.ShiftRight(i, bit.GetBitNum(ModelConst.LEN_RWEAPON_STYLE))
			bit.OrAssign(&attr.Icon2, bit.ShiftLeft(bit.And(n, ModelConst.LEN_RWEAPON_ADD), ModelConst.OFFSET_RWEAPON_ADD+bit.HIGHT))
			rightWeaponFlashType := tmpEquip.GetWeaponFlashType()
			// 取高的闪光效果
			if rightWeaponFlashType > 0 && rightWeaponFlashType > weaponFlashType {
				weaponFlashType = rightWeaponFlashType
			}
		}
	}
	if weaponFlashType > 0 {
		bit.OrAssign(&attr.Icon2, bit.ShiftLeft(bit.And(weaponFlashType, ModelConst.LEN_WEAPON_FLASH), ModelConst.OFFSET_WEAPON_FLASH))
	}
	// 背部翅膀
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_BACK_STYLE, ModelConst.OFFSET_BACK_STYLE)))
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_BACK_COLOR, ModelConst.OFFSET_BACK_COLOR)))
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_BACK_ADD, ModelConst.OFFSET_BACK_ADD)))
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_BACK_ADD_2, ModelConst.OFFSET_BACK_ADD_2+bit.HIGHT)))
	tmpEquip = bag.GetItem(PlayerBag.ARMOR_BACK_POS)

	if tmpEquip != nil && tmpEquip.Config().Icon > 0 {
		icon := tmpEquip.Config().Icon - int(ModelConst.START_BACK)
		if icon > 0 {
			i := 1 + ((icon-1)/4 | 0)
			o := (icon - 1) % 4
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(i, ModelConst.LEN_BACK_STYLE), ModelConst.OFFSET_BACK_STYLE))
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(o, ModelConst.LEN_BACK_COLOR), ModelConst.OFFSET_BACK_COLOR))
			c := bit.ShiftRight(i, bit.GetBitNum(ModelConst.LEN_BACK_STYLE))
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(c, ModelConst.LEN_BACK_ADD), ModelConst.OFFSET_BACK_ADD))
			p := bit.ShiftRight(i, bit.GetBitNum(ModelConst.LEN_BACK_STYLE)+int64(ModelConst.LEN_BACK_ADD))
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(p, ModelConst.LEN_BACK_ADD_2), ModelConst.OFFSET_BACK_ADD_2+bit.HIGHT))
		}
	}

	// 衣服
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_CLOTHES_STYLE, ModelConst.OFFSET_CLOTHES_STYLE)))
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_CLOTHES_COLOR, ModelConst.OFFSET_CLOTHES_COLOR)))
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_CLOTHES_ADD, ModelConst.OFFSET_CLOTHES_ADD+bit.HIGHT)))
	tmpEquip = bag.GetItem(PlayerBag.ARMOR_CLOTHES_POS)
	if tmpEquip != nil && tmpEquip.Config().Icon > 0 {
		icon := tmpEquip.Config().Icon - int(ModelConst.START_CLOTHES)
		if icon > 0 {
			i := 1 + ((icon-1)/8 | 0)
			o := (icon - 1) % 8
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(i, ModelConst.LEN_CLOTHES_STYLE), ModelConst.OFFSET_CLOTHES_STYLE))
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(o, ModelConst.LEN_CLOTHES_COLOR), ModelConst.OFFSET_CLOTHES_COLOR))
			n := bit.ShiftRight(i, bit.GetBitNum(ModelConst.LEN_CLOTHES_STYLE))
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(n, ModelConst.LEN_CLOTHES_ADD), ModelConst.OFFSET_CLOTHES_ADD+bit.HIGHT))
		}
	}
	// 裤子
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TROUSERS_STYLE, ModelConst.OFFSET_TROUSERS_STYLE)))
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TROUSERS_COLOR, ModelConst.OFFSET_TROUSERS_COLOR)))
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TROUSERS_ADD, ModelConst.OFFSET_TROUSERS_ADD+bit.HIGHT)))
	tmpEquip = bag.GetItem(PlayerBag.ARMOR_TROUSERS_POS)
	if tmpEquip != nil && tmpEquip.Config().Icon > 0 {
		icon := tmpEquip.Config().Icon - int(ModelConst.START_TROUSERS)
		if icon > 0 {
			i := 1 + ((icon-1)/8 | 0)
			o := (icon - 1) % 8
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(i, ModelConst.LEN_TROUSERS_STYLE), ModelConst.OFFSET_TROUSERS_STYLE))
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(o, ModelConst.LEN_TROUSERS_COLOR), ModelConst.OFFSET_TROUSERS_COLOR))
			n := bit.ShiftRight(i, bit.GetBitNum(ModelConst.LEN_TROUSERS_STYLE))
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(n, ModelConst.LEN_TROUSERS_ADD), ModelConst.OFFSET_TROUSERS_ADD+bit.HIGHT))
		}
	}

	// 坐骑
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TRANSPORT_STYLE, ModelConst.OFFSET_TRANSPORT_STYLE)))
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TRANSPORT_COLOR, ModelConst.OFFSET_TRANSPORT_COLOR)))
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TRANSPORT_ADD, ModelConst.OFFSET_TRANSPORT_ADD)))
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TRANSPORT_ADD_2, ModelConst.OFFSET_TRANSPORT_ADD_2)))
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TRANSPORT_ADD_3, ModelConst.OFFSET_TRANSPORT_ADD_3+bit.HIGHT)))
	tmpEquip = bag.GetItem(PlayerBag.ARMOR_TRANSPORT_POS)

	if tmpEquip != nil && tmpEquip.IllusionItemId > 0 {
		// 幻化处理
	}
	if nil != tmpEquip && tmpEquip.Config().Icon > 0 {
		expired := tmpEquip.IsTimeItem() && tmpEquip.IsExpired()
		icon := 0
		if !expired {
			icon = tmpEquip.Config().Icon - int(ModelConst.START_TRANSPORT)
		}
		if icon > 0 {
			i := 1 + ((icon-1)/4 | 0)
			o := (icon - 1) % 4
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(i, ModelConst.LEN_TRANSPORT_STYLE), ModelConst.OFFSET_TRANSPORT_STYLE))
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(o, ModelConst.LEN_TRANSPORT_COLOR), ModelConst.OFFSET_TRANSPORT_COLOR))
			T := bit.ShiftRight(i, bit.GetBitNum(ModelConst.LEN_TRANSPORT_STYLE))
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(T, ModelConst.LEN_TRANSPORT_ADD), ModelConst.OFFSET_TRANSPORT_ADD))
			E := bit.ShiftRight(i, bit.GetBitNum(ModelConst.LEN_TRANSPORT_STYLE)+int64(ModelConst.LEN_TRANSPORT_ADD))
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(E, ModelConst.LEN_TRANSPORT_ADD_2), ModelConst.OFFSET_TRANSPORT_ADD_2))
			y := bit.ShiftRight(i, bit.GetBitNum(ModelConst.LEN_TRANSPORT_STYLE)+int64(ModelConst.LEN_TRANSPORT_ADD)+int64(ModelConst.LEN_TRANSPORT_ADD_2))
			bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(y, ModelConst.LEN_TRANSPORT_ADD_3), ModelConst.OFFSET_TRANSPORT_ADD_3+bit.HIGHT))
		}
	}
	p.UpdateIconByFashion()
	log.Debug("icon1: %d", attr.Icon1)
	log.Debug("icon2: %d", attr.Icon2)
	log.Debug("icon3: %d", attr.Icon3)
}

// UpdateIconByFashion 更新时装效果
func (p *Player) UpdateIconByFashion() {
	attr := p.Attr

	// 去掉时装效果
	bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_FASH_ADD, ModelConst.OFFSET_FASH_ADD)))
	bag := p.Bag
	if bag == nil {
		return
	}
	fashionEquip := bag.GetItem(PlayerBag.ARMOR_FASHION_POS)
	if fashionEquip == nil {
		return
	}
	if fashionEquip.IllusionItemId > 0 {
		// todo 时装幻化
	}

	// 过期了
	if fashionEquip.IsTimeItem() && fashionEquip.IsExpired() {
		return
	}
	fashionIcon1 := fashionEquip.Config().FashionIcon1
	fashionIcon2 := fashionEquip.Config().FashionIcon2
	fashionIcon3 := fashionEquip.Config().FashionIcon3

	if fashionIcon1 > 0 {
		// 头发
		hairStyle := bit.And(bit.ShiftRight(fashionIcon1, ModelConst.OFFSET_HAIR_STYLE), ModelConst.LEN_HAIR_STYLE)
		hairAdd := bit.And(bit.ShiftRight(fashionIcon1, ModelConst.OFFSET_HAIR_ADD+bit.HIGHT), ModelConst.LEN_HAIR_ADD)

		if hairStyle > 0 || hairAdd > 0 {
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HAIR_STYLE, ModelConst.OFFSET_HAIR_STYLE)))
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HAIR_COLOR, ModelConst.OFFSET_HAIR_COLOR)))
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HAIR_ADD, ModelConst.OFFSET_HAIR_ADD+bit.HIGHT)))
		}
		// 脸
		faceStyle := bit.And(bit.ShiftRight(fashionIcon1, ModelConst.OFFSET_FACE_STYLE), ModelConst.LEN_FACE_STYLE)
		faceAdd := bit.And(bit.ShiftRight(fashionIcon1, ModelConst.OFFSET_FACE_ADD+bit.HIGHT), ModelConst.LEN_FACE_ADD)
		if faceStyle > 0 || faceAdd > 0 {
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_FACE_STYLE, ModelConst.OFFSET_FACE_STYLE)))
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_FACE_ADD, ModelConst.OFFSET_FACE_ADD+bit.HIGHT)))
		}
		// 手
		handStyle := bit.And(bit.ShiftRight(fashionIcon1, ModelConst.OFFSET_HAND_STYLE), ModelConst.LEN_HAND_STYLE)
		handAdd := bit.And(bit.ShiftRight(fashionIcon1, ModelConst.OFFSET_HAND_ADD+bit.HIGHT), ModelConst.LEN_HAND_ADD)
		if handStyle > 0 || handAdd > 0 {
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HAND_STYLE, ModelConst.OFFSET_HAND_STYLE)))
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HAND_COLOR, ModelConst.OFFSET_HAND_COLOR)))
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HAND_ADD, ModelConst.OFFSET_HAND_ADD+bit.HIGHT)))
		}
		// 脚
		feetStyle := bit.And(bit.ShiftRight(fashionIcon1, ModelConst.OFFSET_FEET_STYLE), ModelConst.LEN_FEET_STYLE)
		feetAdd := bit.And(bit.ShiftRight(fashionIcon1, ModelConst.OFFSET_FEET_ADD+bit.HIGHT), ModelConst.LEN_FEET_ADD)
		if feetStyle > 0 || feetAdd > 0 {
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_FEET_STYLE, ModelConst.OFFSET_FEET_STYLE)))
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_FEET_COLOR, ModelConst.OFFSET_FEET_COLOR)))
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_FEET_ADD, ModelConst.OFFSET_FEET_ADD+bit.HIGHT)))
		}
		// 头盔
		helmetStyle := bit.And(bit.ShiftRight(fashionIcon1, ModelConst.OFFSET_HELMET_STYLE), ModelConst.LEN_HELMET_STYLE)
		helmetAdd := bit.And(bit.ShiftRight(fashionIcon1, ModelConst.OFFSET_HELMET_ADD+bit.HIGHT), ModelConst.LEN_HELMET_ADD)
		if helmetStyle > 0 || helmetAdd > 0 {
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HELMET_STYLE, ModelConst.OFFSET_HELMET_STYLE)))
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HELMET_COLOR, ModelConst.OFFSET_HELMET_COLOR)))
			bit.AndAssign(&attr.Icon1, bit.Not(bit.ShiftLeft(ModelConst.LEN_HELMET_ADD, ModelConst.OFFSET_HELMET_ADD+bit.HIGHT)))
		}

		attr.Icon1 = ut.SetIconValue(attr.Icon1, fashionIcon1, ModelConst.OFFSET_HAIR_STYLE, ModelConst.LEN_HAIR_STYLE, ModelConst.OFFSET_HAIR_COLOR, ModelConst.LEN_HAIR_COLOR, false)
		attr.Icon1 = ut.SetIconValue(attr.Icon1, fashionIcon1, ModelConst.OFFSET_HAIR_ADD, ModelConst.LEN_HAIR_ADD, ModelConst.OFFSET_HAIR_COLOR, ModelConst.LEN_HAIR_COLOR, true)
		attr.Icon1 = ut.SetIconValue(attr.Icon1, fashionIcon1, ModelConst.OFFSET_FACE_STYLE, ModelConst.LEN_FACE_STYLE, -1, -1, false)
		attr.Icon1 = ut.SetIconValue(attr.Icon1, fashionIcon1, ModelConst.OFFSET_FACE_ADD, ModelConst.LEN_FACE_ADD, -1, -1, true)
		attr.Icon1 = ut.SetIconValue(attr.Icon1, fashionIcon1, ModelConst.OFFSET_HAND_STYLE, ModelConst.LEN_HAND_STYLE, ModelConst.OFFSET_HAND_COLOR, ModelConst.LEN_HAND_COLOR, false)
		attr.Icon1 = ut.SetIconValue(attr.Icon1, fashionIcon1, ModelConst.OFFSET_HAND_ADD, ModelConst.LEN_HAND_ADD, ModelConst.OFFSET_HAND_COLOR, ModelConst.LEN_HAND_COLOR, true)
		attr.Icon1 = ut.SetIconValue(attr.Icon1, fashionIcon1, ModelConst.OFFSET_FEET_STYLE, ModelConst.LEN_FEET_STYLE, ModelConst.OFFSET_FEET_COLOR, ModelConst.LEN_FEET_COLOR, false)
		attr.Icon1 = ut.SetIconValue(attr.Icon1, fashionIcon1, ModelConst.OFFSET_FEET_ADD, ModelConst.LEN_FEET_ADD, ModelConst.OFFSET_FEET_COLOR, ModelConst.LEN_FEET_COLOR, true)
		attr.Icon1 = ut.SetIconValue(attr.Icon1, fashionIcon1, ModelConst.OFFSET_HELMET_STYLE, ModelConst.LEN_HELMET_STYLE, ModelConst.OFFSET_HELMET_COLOR, ModelConst.LEN_HELMET_COLOR, false)
		attr.Icon1 = ut.SetIconValue(attr.Icon1, fashionIcon1, ModelConst.OFFSET_HELMET_ADD, ModelConst.LEN_HELMET_ADD, ModelConst.OFFSET_HELMET_COLOR, ModelConst.LEN_HELMET_COLOR, true)
	}

	if fashionIcon2 != 0 {
		shoulderStyle := bit.And(bit.ShiftRight(fashionIcon2, ModelConst.OFFSET_SHOULDER_STYLE), ModelConst.LEN_SHOULDER_STYLE)
		shoulderAdd := bit.And(bit.ShiftRight(fashionIcon2, ModelConst.OFFSET_SHOULDER_ADD), ModelConst.LEN_SHOULDER_ADD)
		shoulderAdd2 := bit.And(bit.ShiftRight(fashionIcon2, ModelConst.OFFSET_SHOULDER_ADD_2+bit.HIGHT), ModelConst.LEN_SHOULDER_ADD_2)
		if shoulderStyle > 0 || shoulderAdd > 0 || shoulderAdd2 > 0 {
			bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_SHOULDER_STYLE, ModelConst.OFFSET_SHOULDER_STYLE)))
			bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_SHOULDER_COLOR, ModelConst.OFFSET_SHOULDER_COLOR)))
			bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_SHOULDER_ADD, ModelConst.OFFSET_SHOULDER_ADD)))
			bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_SHOULDER_ADD_2, ModelConst.OFFSET_SHOULDER_ADD_2+bit.HIGHT)))
		}

		attr.Icon2 = ut.SetIconValue(attr.Icon2, fashionIcon2, ModelConst.OFFSET_SHOULDER_STYLE, ModelConst.LEN_SHOULDER_STYLE, ModelConst.OFFSET_SHOULDER_COLOR, ModelConst.LEN_SHOULDER_COLOR, false)
		attr.Icon2 = ut.SetIconValue(attr.Icon2, fashionIcon2, ModelConst.OFFSET_SHOULDER_ADD, ModelConst.LEN_SHOULDER_ADD, -1, -1, false)
		attr.Icon2 = ut.SetIconValue(attr.Icon2, fashionIcon2, ModelConst.OFFSET_SHOULDER_ADD_2, ModelConst.LEN_SHOULDER_ADD_2, -1, -1, true)

		leftWeaponStyle := bit.And(bit.ShiftRight(fashionIcon2, ModelConst.OFFSET_LWEAPON_STYLE), ModelConst.LEN_LWEAPON_STYLE)
		leftWeaponAdd := bit.And(bit.ShiftRight(fashionIcon2, ModelConst.OFFSET_LWEAPON_ADD+bit.HIGHT), ModelConst.LEN_LWEAPON_ADD)
		rightWeaponStyle := bit.And(bit.ShiftRight(fashionIcon2, ModelConst.OFFSET_RWEAPON_STYLE), ModelConst.LEN_RWEAPON_STYLE)
		rightWeaponAdd := bit.And(bit.ShiftRight(fashionIcon2, ModelConst.OFFSET_RWEAPON_ADD+bit.HIGHT), ModelConst.LEN_RWEAPON_ADD)

		if leftWeaponStyle > 0 || rightWeaponStyle > 0 || leftWeaponAdd > 0 || rightWeaponAdd > 0 {

			bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_LWEAPON_STYLE, ModelConst.OFFSET_LWEAPON_STYLE)))
			bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_LWEAPON_ADD, ModelConst.OFFSET_LWEAPON_ADD+bit.HIGHT)))
			bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_LWEAPON_COLOR, ModelConst.OFFSET_LWEAPON_COLOR)))

			bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_RWEAPON_STYLE, ModelConst.OFFSET_RWEAPON_STYLE)))
			bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_RWEAPON_ADD, ModelConst.OFFSET_RWEAPON_ADD+bit.HIGHT)))
			bit.AndAssign(&attr.Icon2, bit.Not(bit.ShiftLeft(ModelConst.LEN_RWEAPON_COLOR, ModelConst.OFFSET_RWEAPON_COLOR)))
		}

		attr.Icon2 = ut.SetIconValue(attr.Icon2, fashionIcon2, ModelConst.OFFSET_LWEAPON_STYLE, ModelConst.LEN_LWEAPON_STYLE, ModelConst.OFFSET_LWEAPON_COLOR, ModelConst.LEN_LWEAPON_COLOR, false)
		attr.Icon2 = ut.SetIconValue(attr.Icon2, fashionIcon2, ModelConst.OFFSET_LWEAPON_ADD, ModelConst.LEN_LWEAPON_ADD, ModelConst.OFFSET_LWEAPON_COLOR, ModelConst.LEN_LWEAPON_COLOR, true)
		attr.Icon2 = ut.SetIconValue(attr.Icon2, fashionIcon2, ModelConst.OFFSET_RWEAPON_STYLE, ModelConst.LEN_RWEAPON_STYLE, ModelConst.OFFSET_RWEAPON_COLOR, ModelConst.LEN_RWEAPON_COLOR, false)
		attr.Icon2 = ut.SetIconValue(attr.Icon2, fashionIcon2, ModelConst.OFFSET_RWEAPON_ADD, ModelConst.LEN_RWEAPON_ADD, ModelConst.OFFSET_RWEAPON_COLOR, ModelConst.LEN_RWEAPON_COLOR, true)
		attr.Icon2 = ut.SetIconValue(attr.Icon2, fashionIcon2, ModelConst.OFFSET_WEAPON_FLASH, ModelConst.LEN_WEAPON_FLASH, -1, -1, false)
	}

	if fashionIcon3 != 0 {
		backStyle := bit.And(bit.ShiftRight(fashionIcon3, ModelConst.OFFSET_BACK_STYLE), ModelConst.LEN_BACK_STYLE)
		backAdd := bit.And(bit.ShiftRight(fashionIcon3, ModelConst.OFFSET_BACK_ADD), ModelConst.LEN_BACK_ADD)
		backAdd2 := bit.And(bit.ShiftRight(fashionIcon3, ModelConst.OFFSET_BACK_ADD_2+bit.HIGHT), ModelConst.LEN_BACK_ADD_2)

		if backStyle > 0 || backAdd > 0 || backAdd2 > 0 {
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_BACK_STYLE, ModelConst.OFFSET_BACK_STYLE)))
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_BACK_COLOR, ModelConst.OFFSET_BACK_COLOR)))
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_BACK_ADD, ModelConst.OFFSET_BACK_ADD)))
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_BACK_ADD_2, ModelConst.OFFSET_BACK_ADD_2+bit.HIGHT)))
		}

		clothesStyle := bit.And(bit.ShiftRight(fashionIcon3, ModelConst.OFFSET_CLOTHES_STYLE), ModelConst.LEN_CLOTHES_STYLE)
		clothesAdd := bit.And(bit.ShiftRight(fashionIcon3, ModelConst.OFFSET_CLOTHES_ADD+bit.HIGHT), ModelConst.LEN_CLOTHES_ADD)
		if clothesStyle > 0 || clothesAdd > 0 {
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_CLOTHES_STYLE, ModelConst.OFFSET_CLOTHES_STYLE)))
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_CLOTHES_COLOR, ModelConst.OFFSET_CLOTHES_COLOR)))
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_CLOTHES_ADD, ModelConst.OFFSET_CLOTHES_ADD+bit.HIGHT)))
		}
		trousersStyle := bit.And(bit.ShiftRight(fashionIcon3, ModelConst.OFFSET_TROUSERS_STYLE), ModelConst.LEN_TROUSERS_STYLE)
		trousersAdd := bit.And(bit.ShiftRight(fashionIcon3, ModelConst.OFFSET_TROUSERS_ADD+bit.HIGHT), ModelConst.LEN_TROUSERS_ADD)
		if trousersStyle > 0 || trousersAdd > 0 {
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TROUSERS_STYLE, ModelConst.OFFSET_TROUSERS_STYLE)))
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TROUSERS_COLOR, ModelConst.OFFSET_TROUSERS_COLOR)))
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TROUSERS_ADD, ModelConst.OFFSET_TROUSERS_ADD+bit.HIGHT)))
		}
		transportStyle := bit.And(bit.ShiftRight(fashionIcon3, ModelConst.OFFSET_TRANSPORT_STYLE), ModelConst.LEN_TRANSPORT_STYLE)
		transportAdd := bit.And(bit.ShiftRight(fashionIcon3, ModelConst.OFFSET_TRANSPORT_ADD), ModelConst.LEN_TRANSPORT_ADD)
		transportAdd2 := bit.And(bit.ShiftRight(fashionIcon3, ModelConst.OFFSET_TRANSPORT_ADD_2), ModelConst.LEN_TRANSPORT_ADD_2)
		transportAdd3 := bit.And(bit.ShiftRight(fashionIcon3, ModelConst.OFFSET_TRANSPORT_ADD_3+bit.HIGHT), ModelConst.LEN_TRANSPORT_ADD_3)

		if transportStyle > 0 || transportAdd > 0 || transportAdd2 > 0 || transportAdd3 > 0 {
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TRANSPORT_STYLE, ModelConst.OFFSET_TRANSPORT_STYLE)))
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TRANSPORT_COLOR, ModelConst.OFFSET_TRANSPORT_COLOR)))
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TRANSPORT_ADD, ModelConst.OFFSET_TRANSPORT_ADD)))
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TRANSPORT_ADD_2, ModelConst.OFFSET_TRANSPORT_ADD_2)))
			bit.AndAssign(&attr.Icon3, bit.Not(bit.ShiftLeft(ModelConst.LEN_TRANSPORT_ADD_3, ModelConst.OFFSET_TRANSPORT_ADD_3+bit.HIGHT)))
		}
		attr.Icon3 = ut.SetIconValue(attr.Icon3, fashionIcon3, ModelConst.OFFSET_BACK_STYLE, ModelConst.LEN_BACK_STYLE, ModelConst.OFFSET_BACK_COLOR, ModelConst.LEN_BACK_COLOR, false)
		attr.Icon3 = ut.SetIconValue(attr.Icon3, fashionIcon3, ModelConst.OFFSET_BACK_ADD, ModelConst.LEN_BACK_ADD, -1, -1, false)
		attr.Icon3 = ut.SetIconValue(attr.Icon3, fashionIcon3, ModelConst.OFFSET_BACK_ADD_2, ModelConst.LEN_BACK_ADD_2, -1, -1, true)
		attr.Icon3 = ut.SetIconValue(attr.Icon3, fashionIcon3, ModelConst.OFFSET_CLOTHES_STYLE, ModelConst.LEN_CLOTHES_STYLE, ModelConst.OFFSET_CLOTHES_COLOR, ModelConst.LEN_CLOTHES_COLOR, false)
		attr.Icon3 = ut.SetIconValue(attr.Icon3, fashionIcon3, ModelConst.OFFSET_CLOTHES_ADD, ModelConst.LEN_CLOTHES_ADD, ModelConst.OFFSET_CLOTHES_COLOR, ModelConst.LEN_CLOTHES_COLOR, true)
		attr.Icon3 = ut.SetIconValue(attr.Icon3, fashionIcon3, ModelConst.OFFSET_TROUSERS_STYLE, ModelConst.LEN_TROUSERS_STYLE, ModelConst.OFFSET_TROUSERS_COLOR, ModelConst.LEN_TROUSERS_COLOR, false)
		attr.Icon3 = ut.SetIconValue(attr.Icon3, fashionIcon3, ModelConst.OFFSET_TROUSERS_ADD, ModelConst.LEN_TROUSERS_ADD, ModelConst.OFFSET_TROUSERS_COLOR, ModelConst.LEN_TROUSERS_COLOR, true)
		attr.Icon3 = ut.SetIconValue(attr.Icon3, fashionIcon3, ModelConst.OFFSET_TRANSPORT_STYLE, ModelConst.LEN_TRANSPORT_STYLE, ModelConst.OFFSET_TRANSPORT_COLOR, ModelConst.LEN_TRANSPORT_COLOR, false)
		attr.Icon3 = ut.SetIconValue(attr.Icon3, fashionIcon3, ModelConst.OFFSET_TRANSPORT_ADD, ModelConst.LEN_TRANSPORT_ADD, -1, -1, false)
		attr.Icon3 = ut.SetIconValue(attr.Icon3, fashionIcon3, ModelConst.OFFSET_TRANSPORT_ADD_2, ModelConst.LEN_TRANSPORT_ADD_2, -1, -1, false)
		attr.Icon3 = ut.SetIconValue(attr.Icon3, fashionIcon3, ModelConst.OFFSET_TRANSPORT_ADD_3, ModelConst.LEN_TRANSPORT_ADD_3, -1, -1, true)
	}
	if fashionIcon1 != 0 || fashionIcon2 != 0 || fashionIcon3 != 0 {
		bit.OrAssign(&attr.Icon3, bit.ShiftLeft(bit.And(1, ModelConst.LEN_FASH_ADD), ModelConst.OFFSET_FASH_ADD))
	}
}
