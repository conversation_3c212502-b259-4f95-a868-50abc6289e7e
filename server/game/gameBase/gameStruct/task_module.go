package gameStruct

import (
	"world/common/pbGame"
	ut "world/utils"

	"github.com/samber/lo"
)

// 目前任务id不会超过4000，超过4000的都是不需要记录的任务
const defaultSize = 4096

func (p *Player) initTaskModule() *TaskModule {
	if p.Task == nil {
		p.Task = &TaskModule{
			TaskStatus: ut.NewBitMap(defaultSize),
			Tasks:      make([]*Task, 0),
			KillRecord: make(map[int]int),
		}
	}
	return p.Task
}

type TaskModule struct {
	GameModule `bson:"-" json:"-"`
	TaskStatus *ut.BitMap  `bson:"taskStatus, omitempty"` // 任务数据bitmap
	Tasks      []*Task     `bson:"tasks, omitempty"`      // 当前进行中的任务
	KillRecord map[int]int `bson:"killRecord, omitempty"` // 任务杀怪记录
}

// IsTaskFinished
/*
 * @description 判断任务是否完成
 * @param id 任务id
 * @return bool
 */
func (t *TaskModule) IsTaskFinished(id int) bool {
	return t.TaskStatus.Get(id)
}

// SetTaskStatus
/*
 * @description 设置任务状态
 * @param id 任务id
 * @param done true完成，false未完成
 */
func (t *TaskModule) SetTaskStatus(id int, done bool) {
	if done {
		t.TaskStatus.Set(id)
	} else {
		t.TaskStatus.Clear(id)
	}
}

func (t *TaskModule) ToPb() *pbGame.TaskData {
	self := t
	return &pbGame.TaskData{
		Tasks:      lo.Map(t.Tasks, func(t *Task, i int) *pbGame.TaskInfo { return t.ToPb(self) }),
		TaskStatus: t.TaskStatus.Bits,
	}
}

// GetTask
/*
 * @description 从任务数据中获取任务
 * @param taskId
 * @return *Task
 */
func (t *TaskModule) GetTask(taskId int) *Task {
	task, _ := lo.Find(t.Tasks, func(task *Task) bool {
		return task.Id == taskId
	})
	return task
}

// DeleteTask
/*
 * @description 从任务数据中删除任务,返回删除的任务
 * @param taskId
 * @return *Task
 */
func (t *TaskModule) DeleteTask(taskId int) *Task {
	delTask, _ := lo.Find(t.Tasks, func(task *Task) bool {
		return task.Id == taskId
	})
	if delTask != nil {
		t.Tasks = lo.Filter(t.Tasks, func(task *Task, i int) bool {
			return task.Id != delTask.Id
		})
	}
	return delTask
}
