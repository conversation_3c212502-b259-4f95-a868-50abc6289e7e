package gameStruct

import (
	"world/base/cfg"
	"world/common/pbGame"
	"world/common/pbGame/ConditionType"
	ut "world/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

// NewCondition 创建条件
func NewCondition(typ ConditionType.Type, id int, num int) *Condition {
	return NewEmptyCondition().Init(typ, id, num)
}

// NewEmptyCondition 创建空条件
func NewEmptyCondition() *Condition {
	return &Condition{
		Type: ConditionType.None,
	}
}

type Condition struct {
	Type  ConditionType.Type `bson:"type, omitempty"` // 条件类型
	Id    int                `bson:"id, omitempty"`
	Num   int                `bson:"num, omitempty"`
	Extra string             `bson:"extra, omitempty"`
}

// Init
/*
 * @description 使用新数据设置条件
 * @param typ
 * @param id
 * @param num
 * @return *Condition
 */
func (c *Condition) Init(typ ConditionType.Type, id int, num int) *Condition {
	c.Type = typ
	c.Id = id
	c.Num = num
	return c
}

// SetType 设置条件类型
func (c *Condition) SetType(typ ConditionType.Type) *Condition {
	c.Type = typ
	return c
}

// SetId 设置条件id
func (c *Condition) SetId(id int) *Condition {
	c.Id = id
	return c
}

// SetNum 设置条件数量
func (c *Condition) SetNum(num int) *Condition {
	c.Num = num
	return c
}

// SetExtra 设置额外信息
func (c *Condition) SetExtra(extra string) *Condition {
	c.Extra = extra
	return c
}

func (c *Condition) GetType() ConditionType.Type { return c.Type }
func (c *Condition) GetId() int                  { return c.Id }
func (c *Condition) GetNum() int                 { return c.Num }
func (c *Condition) GetExtra() string            { return c.Extra }

// 是不是需要记录的条件类型
func (c *Condition) isNeedRecord() bool {
	if c.Type == ConditionType.KillMonster {
		return true
	}
	return false
}

func (c *Condition) ToPb() *pbGame.Condition {
	return &pbGame.Condition{
		Type:  c.Type,
		Id:    int32(c.Id),
		Num:   int32(c.Num),
		Extra: c.Extra,
	}
}

// MergeConditions
/*
 * @description 合并condition
 * @param orgCons 原始
 * @param newConds 新加
 * @return []*Condition
 */
func MergeConditions(orgCons []*Condition, newConds []*Condition) []*Condition {
	if orgCons == nil {
		orgCons = make([]*Condition, 0)
	}
	for _, cond := range newConds {
		c, _ := lo.Find(orgCons, func(c *Condition) bool {
			return c.Type == cond.Type && cast.ToString(c.Id) == cast.ToString(cond.Id)
		})
		if c == nil {
			orgCons = append(orgCons, ut.Clone(cond).(*Condition))
		} else {
			c.Num += cond.Num
		}
	}
	return orgCons
}

// isNeedRecord 是否需要记录
//
// Parameters:
//   - typ ConditionType.Type
//
// Returns:
//   - bool
func isNeedRecord(typ ConditionType.Type) bool {
	if typ == ConditionType.KillMonster {
		return true
	}
	return false
}

// ConfigConditionConvert
/*
 * @description 用于转换配置数据中的ConfigCondition
 * @param inputConfigConditions
 * @return *configConditionConvertResult
 */
func ConfigConditionConvert(inputConfigConditions ...*cfg.ConfigCondition) *configConditionConvertResult {
	conditions := make([]*Condition, 0)
	for _, inputConfigCondition := range inputConfigConditions {
		typ := ConditionType.Type(inputConfigCondition.Type)
		if typ == ConditionType.None {
			log.Error("未知的条件类型！")
		}
		num := inputConfigCondition.Num
		conditions = MergeConditions(conditions, []*Condition{
			{
				Id:   inputConfigCondition.Id,
				Num:  num,
				Type: typ,
			},
		})
	}
	return &configConditionConvertResult{conditions: conditions}
}

// RewardItemConvert 用于配置中，将 RewardItem 转换成 Condition (类型只会是物品)
//
// Parameters:
//   - inputRewardItems ...*cfg.RewardItem
//
// Returns:
//   - *configConditionConvertResult
func RewardItemConvert(inputRewardItems ...*cfg.RewardItem) *configConditionConvertResult {
	conditions := make([]*Condition, 0)
	for _, inputRewardItem := range inputRewardItems {
		conditions = MergeConditions(conditions, []*Condition{
			{
				Id:   inputRewardItem.Id,
				Num:  inputRewardItem.Quantity,
				Type: ConditionType.HaveItem,
			},
		})
	}
	return &configConditionConvertResult{conditions: conditions}
}

type configConditionConvertResult struct {
	conditions []*Condition
}

// One 获取单个结果 如果有多个结果则返回第一个
func (c *configConditionConvertResult) One() *Condition {
	if len(c.conditions) == 0 {
		return nil
	}
	return c.conditions[0]
}

// All 获取所有结果
func (c *configConditionConvertResult) All() []*Condition {
	return c.conditions
}
