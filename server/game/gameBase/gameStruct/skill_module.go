package gameStruct

import (
	"world/common/pbGame"
	"world/common/pbGame/MyDefine"
	"world/common/pbGame/SKILL_TYPE"
	ut "world/utils"

	"github.com/samber/lo"
)

type SkillModule struct {
	GameModule        `bson:"-" json:"-"`
	Sp                int            `bson:"sp, omitempty"`                // 未使用的技能点
	List              map[int]*Skill `bson:"list, omitempty"`              // 技能列表
	Cnt               int            `bson:"cnt, omitempty"`               // 可学习技能数量
	FormationSkill    *Skill         `bson:"formationSkill, omitempty"`    // 阵法技能
	ActiveAutoSkillID int            `bson:"activeAutoSkillId, omitempty"` // 自动释放的主动技能id
	AutoSkillID       []int          `bson:"autoSkillId, omitempty"`       // 自动释放的自动技能id
}

// GetSkillById 使用id获取技能
//
// Parameters:
//   - id int
//
// Returns:
//   - *Skill
func (s *SkillModule) GetSkillById(id int) *Skill { return s.List[id] }

// GetFreeSlot 获取空闲技能槽位
//
// Returns:
//   - int
func (s *SkillModule) GetFreeSlot() int {
	return s.Cnt - lo.CountBy(lo.Values(s.List), func(v *Skill) bool { return v.IsLearnByBook })
}

// ToPb 转换成pb数据
//
// Returns:
//   - *pbGame.SkillData
func (s *SkillModule) ToPb() *pbGame.SkillData {
	if s == nil {
		return nil
	}
	list := make(map[int32]*pbGame.SkillInfo)
	for k, v := range s.List {
		list[int32(k)] = v.ToPb()
	}
	// 兼容
	if s.ActiveAutoSkillID == 0 {
		s.ActiveAutoSkillID = -1
	}
	if len(s.AutoSkillID) == 0 {
		s.AutoSkillID = make([]int, 4)
	}
	return &pbGame.SkillData{
		Sp:                int32(s.Sp),
		List:              list,
		Cnt:               int32(s.Cnt),
		ActiveAutoSkillId: int32(s.ActiveAutoSkillID),
		AutoSkillId:       ut.ToInt32(s.AutoSkillID),
	}
}

// GetSkillPowerValue 获取某个属性类型的技能增加
//
// Parameters:
//   - typ SKILL_TYPE.SKILL_TYPE 技能类型
//   - powerType MyDefine.POWER 属性定义
//
// Returns:
//   - int
func (s *SkillModule) GetSkillPowerValue(typ SKILL_TYPE.SKILL_TYPE, powerType MyDefine.POWER) int {
	val := 0
	if len(s.List) == 0 {
		return val
	}
	for _, v := range s.List {
		if v != nil && v.GetJson().Type == typ {
			val += v.GetPowerValue(powerType)
		}
	}
	return 0 | val
}

// SetAutoSkillID 设置自动释放的技能 存在则取消 不存在则设置
//
// Parameters:
//   - id int
//
// Returns:
//   - bool 自动技能最多设置4个，超过就失败
func (s *SkillModule) SetAutoSkillID(id int) bool {
	_, index, exists := lo.FindIndexOf(s.AutoSkillID, func(v int) bool { return v == id })
	if exists {
		s.AutoSkillID[index] = 0
		return true
	}
	for i, v := range s.AutoSkillID {
		if v == 0 {
			s.AutoSkillID[i] = id
			return true
		}
	}
	return false
}
