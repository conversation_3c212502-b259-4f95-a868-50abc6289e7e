package gameStruct

import (
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/common/pbGame/EQP"
	"world/common/pbGame/ITEM_TYPE"
	"world/game/gameBase/types/PlayerBag"
	ut "world/utils"

	"github.com/samber/lo"
)

type BagModule struct {
	GameModule              `bson:"-" json:"-"`
	Money1                  int                 `bson:"money1, omitempty"`    // Deprecated:黄金
	Money2                  int                 `bson:"money2, omitempty"`    // Deprecated:金叶
	Money3                  int                 `bson:"money3, omitempty"`    // Deprecated:铜币
	BagSize                 int                 `bson:"bagSize, omitempty"`   // Deprecated:背包格子扩展数量
	StoreSize               int                 `bson:"storeSize, omitempty"` // Deprecated:仓库格子扩展数量
	Store                   []*Item             `bson:"store, omitempty"`     // 物品数据
	LastResetTime           int64               `bson:"-"`                    // 上一次整理背包的时间
	LastIdentifyItemId      int                 `bson:"-"`                    // 上一次鉴定的物品id
	LastIdentifyItemSlotPos int                 `bson:"-"`                    // 上一次鉴定的物品位置
	LastIdentifyIsUpgrade   bool                `bson:"-"`                    // 上一次鉴定是否是进阶鉴定
	LastIdentifyResult      []*pbGame.PowerData `bson:"-"`                    // 上一次鉴定的结果
}

// ToPb
/*
 * @description 转换为pb
 * @return *pbGame.BagData
 */
func (b *BagModule) ToPb(bagType PlayerBag.Type) *pbGame.BagData {
	items := make([]*Item, 0)
	if bagType == PlayerBag.BAG {
		start, end := b.GetStartEndPos(PlayerBag.EQUIP)
		items = append(items, b.Store[start:end+1]...)
	}
	start, end := b.GetStartEndPos(bagType)
	items = append(items, b.Store[start:end+1]...)

	plr := b.GetVo()
	itemMap := make(map[int32]*pbGame.ItemData)
	for _, item := range items {
		if item != nil {
			itemMap[int32(item.SlotPos)] = item.ToPb(plr.(*Player))
		}
	}

	return &pbGame.BagData{
		Money1:        int32(b.Money1),
		Money2:        int32(b.Money2),
		Money3:        int32(b.Money3),
		BagSize:       int32(b.BagSize),
		SelfStoreSize: int32(b.StoreSize),
		Store:         itemMap,
	}
}

func (b *BagModule) GetMoney1() int  { return b.Money1 }
func (b *BagModule) GetMoney2() int  { return b.Money2 }
func (b *BagModule) GetMoney3() int  { return b.Money3 }
func (b *BagModule) AddMoney1(v int) { b.Money1 += v }
func (b *BagModule) AddMoney2(v int) { b.Money2 += v }
func (b *BagModule) AddMoney3(v int) { b.Money3 += v }

// AddItem1
/*
 * @description 添加物品到背包
 * @param item 物品数据
 * @param justCheck 只进行检查能否添加，不真正添加
 * @return bool 成功返回添加的位置，失败返回小于0的原因值
 */
func (b *BagModule) AddItem1(item *Item, justCheck bool) int {
	if item == nil {
		return -1
	}
	return b.AddItem2(item, item.Quantity, justCheck)
}

// AddItem2
/*
 * @description 添加指定数量的物品到背包
 * @param item 物品
 * @param addNum 数量
 * @param justCheck 只进行检查能否添加，不真正添加
 * @return bool 成功返回添加的位置，失败返回小于0的原因值
 */
func (b *BagModule) AddItem2(item *Item, addNum int, justCheck bool) int {
	return b.AddItem3(PlayerBag.BAG, item, addNum, justCheck)
}

// AddItem3
/*
 * @description 添加指定数量的物品到指定容器
 * @param bagType 容器类型
 * @param item 物品
 * @param addNum 数量
 * @param justCheck 只进行检查能否添加，不真正添加
 * @return bool 成功返回添加的位置，失败返回小于0的原因值
 */
func (b *BagModule) AddItem3(bagType PlayerBag.Type, item *Item, addNum int, justCheck bool) int {
	if item == nil {
		// 物品数据为空
		return -1
	}
	// 校验数量
	if addNum <= 0 {
		return -2
	}
	// 物品可以叠加，但是单次添加的数量超过叠加上限
	if item.IsStackAble() && item.Config().StackNum < addNum {
		return -3
	}
	// 可用于添加的位置
	pos := b.NextFreePos(bagType, item, addNum, true)
	// 找不到可以用来添加物品的位置
	if !b.IsValidPos(pos) {
		return int(Response.AddItemBagIsFull)
	}
	hasCurItem := b.Store[pos]
	if nil != hasCurItem {
		// 目标位置已经存在物品 但是物品不能叠加存放
		if !item.IsStackAble() {
			return -5
		}
		// 目标位置已经存在物品 并且要添加的数量超过可堆积的上限
		if hasCurItem.Quantity+addNum > hasCurItem.Config().StackNum {
			return -6
		}
		if !justCheck {
			hasCurItem.Quantity += addNum
		}
		return pos
	}
	if !justCheck {
		item.Quantity = lo.If(item.IsStackAble(), addNum).Else(1)
		item.SlotPos = pos
		b.Store[pos] = item
	}
	return pos
}

// NextFreePos
/*
 * @description 获取指定容器的下一个空闲位置 用来存放物品
 * @param bagType 容器类型
 * @param item 物品数据
 * @param num 物品数量
 * @param allowFallback 指示是否在找不到合适位置时继续尝试通过其他逻辑查找位置
 * @return int
 */
func (b *BagModule) NextFreePos(bagType PlayerBag.Type, item *Item, num int, allowFallback bool) int {
	if item == nil {
		return -1
	}
	addPos := -1
	start, end := b.GetStartEndPos(bagType)
	if item.IsStackAble() {
		for i := start; i <= end; i++ {
			if !b.IsValidPos(i) {
				continue
			}
			cur := b.Store[i]
			// 物品不存在或者物品摆摊出售中
			if cur == nil || cur.IsShopLocked() {
				continue
			}
			// id不匹配
			if cur.Id != item.Id {
				continue
			}
			// 堆积上限
			if cur.Quantity+num > cur.Config().StackNum {
				continue
			}
			addPos = i
			break
		}
	}
	// 位置合法，则返回
	if b.IsValidPos(addPos) {
		return addPos
	}
	// 不需要尝试通过其他逻辑查找
	if !allowFallback {
		return addPos
	}
	// 尝试其他逻辑
	return b.NextFreePosForType(bagType)
}

// NextFreePosForType
/*
 * @description 获取指定容器的下一个空闲位置 用来存放物品
 * @param bagType
 * @return int 找不到返回-1
 */
func (b *BagModule) NextFreePosForType(bagType PlayerBag.Type) int {
	start, end := b.GetStartEndPos(bagType)
	return b.FindFreePos(start, end)
}

// FindFreePos
/*
 * @description 在给定的范围内找到一个空闲位置
 * @param start
 * @param end
 * @return int 找不到返回-1
 */
func (b *BagModule) FindFreePos(start, end int) int {
	for ; start <= end; start++ {
		if b.IsValidPos(start) && nil == b.Store[start] {
			return start
		}
	}
	return -1
}

// GetStartEndPos
/*
 * @description 获取不同容器背包类型的起始结束位置
 * @param bagType 容器类型
 * @return startPos 开始位置
 * @return endPos 结束位置
 */
func (b *BagModule) GetStartEndPos(bagType PlayerBag.Type) (startPos, endPos int) {
	switch bagType {
	case PlayerBag.EQUIP:
		startPos = PlayerBag.EQUIP_POS_START
		endPos = PlayerBag.EQUIP_POS_END - 1
	case PlayerBag.BAG:
		startPos = PlayerBag.BAG_START
		endPos = b.GetBagEnd()
	case PlayerBag.STORAGE:
		startPos = PlayerBag.STORE_START
		endPos = b.GetStorageEnd()
	case PlayerBag.VIP_STORAGE:
		startPos = PlayerBag.VIPSTORE_START
		endPos = b.GetVipStorageEnd()
	case PlayerBag.HIGHT_VIP_STORAGE:
		startPos = PlayerBag.HIGHT_VIPSTORE_START
		endPos = b.GetHighVipStorageEnd()
	}
	return
}

func (b *BagModule) GetStartEndItems(bagType PlayerBag.Type) []*Item {
	start, end := b.GetStartEndPos(bagType)
	return b.Store[start : end+1]
}

func (b *BagModule) GetBagEnd() int { return PlayerBag.BAG_START + b.BagSize - 1 }

// GetStorageEnd
/*
 * @description 获取个人仓库结束位置
 * @return int
 */
func (b *BagModule) GetStorageEnd() int {
	return PlayerBag.STORE_START + b.StoreSize - 1
}

// GetVipStorageEnd
/*
 * @description 获取vip仓库结束位置
 * @return int
 */
func (b *BagModule) GetVipStorageEnd() int {
	lv := b.GetVo().AttrModule().VipLv
	if 7 == lv {
		return PlayerBag.VIP7STORE_END - 1
	}
	if 8 == lv {
		return PlayerBag.VIP8STORE_END - 1
	}
	if 9 == lv {
		return PlayerBag.VIP9STORE_END - 1
	}
	return PlayerBag.VIPSTORE_END - 1
}

// GetHighVipStorageEnd
/*
 * @description 获取高级vip仓库结束位置
 * @return int
 */
func (b *BagModule) GetHighVipStorageEnd() int {
	lv := b.GetVo().AttrModule().VipLv
	if 5 == lv {
		return PlayerBag.HIGHT_VIP_5_STORE_END - 1
	}
	if 6 == lv {
		return PlayerBag.HIGHT_VIP_6_STORE_END - 1
	}
	if 7 == lv {
		return PlayerBag.HIGHT_VIP_7_STORE_END - 1
	}
	if 8 == lv {
		return PlayerBag.HIGHT_VIP_8_STORE_END - 1
	}
	if 9 == lv {
		return PlayerBag.HIGHT_VIP_9_STORE_END - 1
	}
	return -1
}

// IsValidPos
/*
 * @description 判断格子位置是否合法
 * @param pos
 * @return bool
 */
func (b *BagModule) IsValidPos(pos int) bool { return pos >= 0 && pos < len(b.Store) }

// IsValidBagPos
/*
 * @description 判断格子位置是不是一个合法背包位置
 * @param pos
 * @return bool
 */
func (b *BagModule) IsValidBagPos(pos int) bool {
	return pos >= PlayerBag.BAG_START && pos <= b.GetBagEnd()
}

// IsValidEquipPos 是不是一个合理的装备穿戴位置
//
// Parameters:
//   - pos int
//
// Returns:
//   - bool
func (b *BagModule) IsValidEquipPos(pos int) bool {
	return pos >= PlayerBag.EQUIP_POS_START && pos < PlayerBag.EQUIP_POS_END
}

// IsValidWeaponPos 是不是一个合理的武器穿戴位置
//
// Parameters:
//   - pos int
//
// Returns:
//   - bool
func (b *BagModule) IsValidWeaponPos(pos int) bool {
	return pos == PlayerBag.WEAPON_LEFT_POS || pos == PlayerBag.WEAPON_RIGHT_POS
}

// GetItem
/*
 * @description 获取指定位置的物品
 * @param pos
 * @return *Item
 */
func (b *BagModule) GetItem(pos int) *Item {
	if !b.IsValidPos(pos) {
		return nil
	}
	return b.Store[pos]
}

// GetBagItem
/*
 * @description 从背包中获取指定位置的物品
 * @param pos
 * @return *Item
 */
func (b *BagModule) GetBagItem(pos int) *Item {
	if !b.IsValidBagPos(pos) {
		return nil
	}
	return b.GetItem(pos)
}

// RemoveBagItemByPos
/*
 * @description 从背包中移除指定位置指定数量的物品
 * @param pos
 * @param removeNum
 * @return int
 */
func (b *BagModule) RemoveBagItemByPos(pos, removeNum int) int {
	item := b.GetItem(pos)
	if nil == item {
		return -3
	}
	if removeNum > item.Quantity {
		return -4
	}
	if removeNum < 0 {
		return -5
	}
	item.Quantity -= removeNum
	if item.Quantity <= 0 {
		b.Store[pos] = nil
	}
	return 0
}

// RemoveBagItemByNum 从背包中移除指定数量的物品
//
// Parameters:
//   - id int
//   - removeNum int
//
// Returns:
//   - bool
func (b *BagModule) RemoveBagItemByNum(id, removeNum int) bool {
	has := b.GetItemNumById(id)
	if has < removeNum {
		return false
	}

	for i := b.GetBagEnd(); i >= PlayerBag.BAG_START; i-- {
		item := b.GetItem(i)
		if item == nil || item.Id != id {
			continue
		}
		if item.Quantity > removeNum {
			item.Quantity -= removeNum
			return true
		}
		if item.Quantity == removeNum {
			b.Store[i] = nil
			return true
		}
		removeNum -= item.Quantity
		b.Store[i] = nil
	}
	return removeNum == 0
}

// GetItemNumById
/*
 * @description 使用物品id在背包中获取物品的数量
 * @param id 物品的id
 * @return int
 */
func (b *BagModule) GetItemNumById(id int) (cnt int) {
	for i := PlayerBag.BAG_START; i <= b.GetBagEnd(); i++ {
		cur := b.Store[i]
		if cur == nil || cur.Id != id {
			continue
		}
		cnt += cur.Quantity
	}
	return
}

// GetEquipItemSetNum 获取装备套装穿戴的数量
//
// Parameters:
//   - setId int 套装id
//
// Returns:
//   - cnt int 达成数量
func (b *BagModule) GetEquipItemSetNum(setId int) (cnt int) {
	if setId <= 0 {
		return
	}
	for i := PlayerBag.EQUIP_POS_START; i < PlayerBag.EQUIP_POS_END; i++ {
		item := b.GetItem(i)
		if item == nil {
			continue
		}
		if item.Get(EQP.ITEM_SET_ID) != setId {
			continue
		}
		cnt += 1
	}
	return
}

// CountFreePos 统计背包中空闲位置的数量（只是背包）
//
// Returns:
//   - cnt int
func (b *BagModule) CountFreePos() (cnt int) {
	for i := PlayerBag.BAG_START; i <= b.GetBagEnd(); i++ {
		cur := b.Store[i]
		if cur == nil {
			cnt++
		}
	}
	return
}

// SwapItem 交换背包中的两个物品
//
// Parameters:
//   - posA int 物品位置a
//   - posB int 物品位置b
//
// Returns:
//   - Response.Code
func (b *BagModule) SwapItem(posA, posB int) Response.Code {
	if !b.IsValidPos(posA) || !b.IsValidPos(posB) {
		return Response.ItemSlotPosError
	}
	itemA := b.Store[posA]
	itemB := b.Store[posB]
	if itemA != nil {
		itemA.SlotPos = posB
		if posB >= PlayerBag.EQUIP_POS_START && posB < PlayerBag.EQUIP_POS_END {
			itemA.Durability = ut.Min(itemA.Config().DurMax, itemA.Durability)
		}
	}
	b.Store[posB] = itemA

	if itemB != nil {
		itemB.SlotPos = posA
		if posA >= PlayerBag.EQUIP_POS_START && posA < PlayerBag.EQUIP_POS_END {
			itemB.Durability = ut.Min(itemB.Config().DurMax, itemB.Durability)
		}
	}
	b.Store[posA] = itemB
	return Response.NoError
}

func (b *BagModule) GetForPos(typ EQP.Type, pos int) int {
	item := b.GetItem(pos)
	if item == nil {
		return -1
	}
	return item.Get(typ)
}

func (b *BagModule) Get(typ EQP.Type) int {
	if b == nil {
		return 0
	}
	switch typ {
	case EQP.ATK_MAX:
		fallthrough
	case EQP.ATK_MIN:
		fallthrough
	case EQP.HIT_TIME:
		fallthrough
	case EQP.HIT_RATE:
		return b.GetForArray(typ, []int{PlayerBag.WEAPON_LEFT_POS, PlayerBag.WEAPON_RIGHT_POS})
	case EQP.DEF_STR:
		fallthrough
	case EQP.DEF_AGI:
		fallthrough
	case EQP.DEF_MAGIC:
		return b.Get_3(typ, PlayerBag.EQUIP_POS_START, PlayerBag.EQUIP_POS_END)
	case EQP.ATK_TYPE:
		return int(b.GetItemAtkType())
	}
	return 0
}

func (b *BagModule) GetItemAtkType() pbGame.AtkType {
	val := b.GetForPos(EQP.ATK_TYPE, PlayerBag.WEAPON_LEFT_POS)
	if val != -1 {
		return pbGame.AtkType(val)
	}
	val = b.GetForPos(EQP.ATK_TYPE, PlayerBag.WEAPON_RIGHT_POS)
	if val != -1 {
		return pbGame.AtkType(val)
	}
	return -1
}

// Get_3 使用起始获取背包道具数值
//
// Parameters:
//   - typ EQP.Type
//   - start int
//   - end int
//
// Returns:
//   - int
func (b *BagModule) Get_3(typ EQP.Type, start, end int) int {
	value := 0
	for i := start; i < end; i++ {
		if item := b.GetItem(i); item != nil {
			value += item.Get(typ)
		}
	}
	return value
}

// GetForArray 使用位置数组获取背包道具数值
//
// Parameters:
//   - typ EQP.Type
//   - posAry []int 位置数组
//
// Returns:
//   - int
func (b *BagModule) GetForArray(typ EQP.Type, posAry []int) int {
	if len(posAry) == 0 {
		return 0
	}
	val := 0

	for _, v := range posAry {
		if !b.IsValidPos(v) {
			continue
		}
		if item := b.GetItem(v); item != nil {
			val += item.Get(typ)
		}
	}
	return val
}

// GetPetItemAry 获取背包中的宠物列表 包含仓库
//
// Returns:
//   - []*Item
func (b *BagModule) GetPetItemAry(filter func(item *Item) bool) []*Item {
	ary := make([]*Item, 0)

	for i := 0; i < len(b.Store); i++ {
		item := b.GetItem(i)
		if item == nil || item.Config().Type != ITEM_TYPE.PET || item.PetItem == nil {
			continue
		}
		if filter != nil && !filter(item) {
			continue
		}
		ary = append(ary, item)
	}
	return ary
}

func (b *BagModule) GetItemBy(is func(item *Item) bool) []*Item {
	ary := make([]*Item, 0)
	for i := PlayerBag.BAG_START; i <= b.GetBagEnd(); i++ {
		item := b.Store[i]
		if item != nil && is(item) {
			ary = append(ary, item)
		}
	}
	return ary
}

func (b *BagModule) GetItemNumBy(is func(item *Item) bool) int {
	cnt := 0
	for i := PlayerBag.BAG_START; i <= b.GetBagEnd(); i++ {
		item := b.Store[i]
		if item != nil && is(item) {
			cnt += item.Quantity
		}
	}
	return cnt
}

// GetIdentifyScrollNum 获取背包中普通鉴定卷轴数量
//
// Returns:
//   - int
func (b *BagModule) GetIdentifyScrollNum() int {
	return b.GetItemNumBy(func(item *Item) bool { return item.IsIdentifyScrollItem() })
}

// GetHighIdentifyScrollNum 获取背包中高级鉴定卷轴数量
//
// Returns:
//   - int
func (b *BagModule) GetHighIdentifyScrollNum() int {
	return b.GetItemNumBy(func(item *Item) bool { return item.IsHighIdentifyScrollItem() })
}

// GetUpgradeIdentifyScrollNum 获取背包中进阶鉴定卷轴数量
//
// Returns:
//   - int
func (b *BagModule) GetUpgradeIdentifyScrollNum() int {
	return b.GetItemNumBy(func(item *Item) bool { return item.IsUpgradeIdentifyScrollItem() })
}

// 获取背包中装备强化卷轴数量
func (b *BagModule) GetStarScrollNum() int {
	return b.GetItemNumBy(func(item *Item) bool { return item.IsStarScroll() })
}
