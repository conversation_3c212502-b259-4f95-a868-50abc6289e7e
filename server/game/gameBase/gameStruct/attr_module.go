package gameStruct

import (
	"world/base/cfg"
	"world/common/pbBase/Camp"
	"world/common/pbBase/Job"
	"world/common/pbBase/ModelConst"
	"world/common/pbBase/Sex"
	"world/common/pbGame"
)

type AttrModule struct {
	GameModule       `bson:"-" json:"-"`
	Icon1            int64               `bson:"icon1, omitempty"`    // icon1
	Icon2            int64               `bson:"icon2, omitempty"`    // icon2
	Icon3            int64               `bson:"icon3, omitempty"`    // icon3
	Status           int64               `bson:"status, omitempty"`   // 状态数据
	Level            int                 `bson:"level, omitempty"`    // 等级
	Level2           int                 `bson:"level2, omitempty"`   // 传奇等级
	Exp              int                 `bson:"exp, omitempty"`      // 普通经验
	Exp2             int                 `bson:"exp2, omitempty"`     // 传奇经验
	Hp               int                 `bson:"hp, omitempty"`       // 当前血
	Mp               int                 `bson:"mp, omitempty"`       // 当前蓝
	HpMax            int                 `bson:"hpMax, omitempty"`    // 最大血
	MpMax            int                 `bson:"mpMax, omitempty"`    // 最大蓝
	VipLv            int                 `bson:"vipLv, omitempty"`    // 当前vip等级
	VipLvMax         int                 `bson:"vipLvMax, omitempty"` // 历史最高vip等级
	Cp               int                 `bson:"cp, omitempty"`       // 未分配的属性点
	Str              int                 `bson:"str, omitempty"`      // 力量
	Agi              int                 `bson:"agi, omitempty"`      // 敏捷
	Con              int                 `bson:"con, omitempty"`      // 体质
	Ilt              int                 `bson:"ilt, omitempty"`      // 智力
	Wis              int                 `bson:"wis, omitempty"`      // 感知
	Power            *pbGame.PowerData   `bson:"-"`                   //
	TitlePower1      *pbGame.PowerData   `bson:"-"`                   // 称号属性1
	TitlePower2      *pbGame.PowerData   `bson:"-"`                   // 称号属性2
	FightPowerList   []*pbGame.PowerData `bson:"-"`                   //
	Speed            int                 `bson:"-"`                   // 出手速度
	AtkTime          int                 `bson:"-"`                   // 攻击次数
	AtkStr           int                 `bson:"-"`                   // 劈砍攻击力
	AtkAgi           int                 `bson:"-"`                   // 穿刺攻击力
	AtkMagic         int                 `bson:"-"`                   // 魔法攻击力
	DefStr           int                 `bson:"-"`                   // 劈砍防御力
	DefAgi           int                 `bson:"-"`                   // 穿刺防御力
	DefMagic         int                 `bson:"-"`                   // 魔法防御力
	Dodge            int                 `bson:"-"`                   // 闪避
	HitRate          int                 `bson:"-"`                   // 命中
	HitMagic         int                 `bson:"-"`                   // 魔法命中
	Critical         int                 `bson:"-"`                   // 暴击
	ForceHit         int                 `bson:"-"`                   // 强制命中
	Wil              int                 `bson:"-"`                   // 状态抵抗
	Tough            int                 `bson:"-"`                   // 伤害减免
	Block            int                 `bson:"-"`                   // 格挡
	BrkArmor         int                 `bson:"-"`                   // 破甲
	MagicPenetration int                 `bson:"-"`                   // 魔法穿透
	Insight          int                 `bson:"-"`                   // 洞察
	DefField         int                 `bson:"-"`                   // 法力护盾
	Back             int                 `bson:"-"`                   // 反伤
	MagicBack        int                 `bson:"-"`                   // 魔法反伤
	LifeAbsorption   int                 `bson:"-"`                   // 生命吸收
	ManaAbsorption   int                 `bson:"-"`                   // 法力吸收
	HealRecovery     int                 `bson:"-"`                   // 生命恢复
	ManaRecovery     int                 `bson:"-"`                   // 法力恢复
	IgnoreBack       int                 `bson:"-"`                   // 忽视反伤
	IgnoreMagicBack  int                 `bson:"-"`                   // 忽视魔法反伤
	IgnoreBlock      int                 `bson:"-"`                   // 忽视格挡
	IgnoreInsight    int                 `bson:"-"`                   // 忽视洞察
	IgnoreWil        int                 `bson:"-"`                   // 忽视意志
	IgnoreTouch      int                 `bson:"-"`                   // 无视伤害减免
	IgnoreCritical   int                 `bson:"-"`                   // 忽视暴击
	KeepOutAtkTime   int                 `bson:"-"`                   // 免伤护盾
	CriticalDmg      int                 `bson:"-"`                   // 暴击伤害
	Recovery         int                 `bson:"-"`                   // 恢复
	Argo             int                 `bson:"-"`                   // 仇恨值
}

// ToSimplePb
/*
 * @description  只返回重要信息
 * @return *pbGame.AttrData
 */
func (a *AttrModule) ToSimplePb() *pbGame.AttrData {
	return &pbGame.AttrData{
		Icon1:  a.Icon1,
		Icon2:  a.Icon2,
		Icon3:  a.Icon3,
		Status: a.Status,
		Level:  int32(a.Level),
		VipLv:  int32(a.VipLv),
	}
}

// ToPb
/*
 * @description 转换为pb
 * @return *pbGame.AttrData
 */
func (a *AttrModule) ToPb() *pbGame.AttrData {
	if a == nil {
		return nil
	}
	data := a.ToSimplePb()
	data.Exp = int32(a.Exp)
	data.Exp2 = int32(a.Exp2)
	data.Level2 = int32(a.Level2)
	data.Cp = int32(a.Cp)
	data.Str = int32(a.Str)
	data.Agi = int32(a.Agi)
	data.Con = int32(a.Con)
	data.Ilt = int32(a.Ilt)
	data.Wis = int32(a.Wis)
	data.Hp = int32(a.Hp)
	data.Mp = int32(a.Mp)
	return data
}

// SetSex 设置性别
func (a *AttrModule) SetSex(sex Sex.Type) *AttrModule {
	val := int64(sex)
	lenSex := int64(ModelConst.LEN_SEX)
	offsetSex := int64(ModelConst.OFFSET_SEX)
	a.Icon1 &= ^(lenSex << offsetSex)
	a.Icon1 |= (val & lenSex) << offsetSex
	return a
}

// SetJob 设置职业
func (a *AttrModule) SetJob(job Job.Type) *AttrModule {
	val := int64(job)
	lenJob := int64(ModelConst.LEN_JOB)
	offsetJob := int64(ModelConst.OFFSET_JOB)
	a.Icon1 &= ^(lenJob << offsetJob)
	a.Icon1 |= (val & lenJob) << offsetJob
	return a
}

// GetJob 获取职业
func (a *AttrModule) GetJob() int64 {
	return a.Icon1 >> int64(ModelConst.OFFSET_JOB) & int64(ModelConst.LEN_JOB)
}

// SetRace 设置脸型
func (a *AttrModule) SetRace(race Camp.Type) *AttrModule {
	val := int64(race)
	lenRace := int64(ModelConst.LEN_RACE)
	offsetRace := int64(ModelConst.OFFSET_RACE)
	a.Icon1 &= ^(lenRace << offsetRace)
	a.Icon1 |= (val & lenRace) << offsetRace
	return a
}

// SetHand 设置手样式
func (a *AttrModule) SetHand(hand int64) *AttrModule {
	lenHandStyle := int64(ModelConst.LEN_HAND_STYLE)
	offsetHandStyle := int64(ModelConst.OFFSET_HAND_STYLE)
	a.Icon1 &= ^(lenHandStyle << offsetHandStyle)
	a.Icon1 |= (hand & lenHandStyle) << offsetHandStyle
	return a
}

// SetFeet 设置脚样式
func (a *AttrModule) SetFeet(feet int64) *AttrModule {
	lenFeetStyle := int64(ModelConst.LEN_FEET_STYLE)
	offsetFeetStyle := int64(ModelConst.OFFSET_FEET_STYLE)
	a.Icon1 &= ^(lenFeetStyle << offsetFeetStyle)
	a.Icon1 |= (feet & lenFeetStyle) << offsetFeetStyle
	return a
}

// SetHairStyle 设置头发样式
func (a *AttrModule) SetHairStyle(hair int64) *AttrModule {
	lenHairStyle := int64(ModelConst.LEN_HAIR_STYLE)
	offsetHairStyle := int64(ModelConst.OFFSET_HAIR_STYLE)
	a.Icon1 &= ^(lenHairStyle << offsetHairStyle)
	a.Icon1 |= (hair & lenHairStyle) << offsetHairStyle
	return a
}

// SetHairColor 设置头发颜色
func (a *AttrModule) SetHairColor(color int64) *AttrModule {
	lenHairColor := int64(ModelConst.LEN_HAIR_COLOR)
	offsetHairColor := int64(ModelConst.OFFSET_HAIR_COLOR)
	a.Icon1 &= ^(lenHairColor << offsetHairColor)
	a.Icon1 |= (color & lenHairColor) << offsetHairColor
	return a
}

// SetFaceStyle 设置脸样式
func (a *AttrModule) SetFaceStyle(faceStyle int64) *AttrModule {
	lenFaceStyle := int64(ModelConst.LEN_FACE_STYLE)
	offsetFaceStyle := int64(ModelConst.OFFSET_FACE_STYLE)
	a.Icon1 &= ^(lenFaceStyle << offsetFaceStyle)
	a.Icon1 |= (faceStyle & lenFaceStyle) << offsetFaceStyle
	return a
}

// IsStatusBit
/*
 * @description 判断玩家状态是否包含状态
 * @param bit
 */
func (a *AttrModule) IsStatusBit(bit ModelConst.Type) bool {
	return 0 != (a.Status & int64(bit))
}

// SetStatusBit
/*
 * @description 设置玩家状态
 * @param bit
 */
func (a *AttrModule) SetStatusBit(bit ModelConst.Type) {
	a.Status |= int64(bit)
}

// ClearStatusBit
/*
 * @description 清除玩家状态
 * @param bit
 */
func (a *AttrModule) ClearStatusBit(bit ModelConst.Type) {
	a.Status &= ^int64(bit)
}

// GetMaxExp
/*
 * @description 获取当前等级下 普通经验最大值
 * @return int
 */
func (a *AttrModule) GetMaxExp() int {
	bean, _ := cfg.ContainerLevelExp.GetBeanByUnique(a.Level)
	if bean == nil {
		return 0
	}
	return bean.Exp
}

// GetMaxExp2 传奇暂定
func (a *AttrModule) GetMaxExp2() int {
	return a.GetMaxExp() * 2
}

// IsMaxLv 普通等级是不是满了
func (a *AttrModule) IsMaxLv() bool {
	return a.Level >= cfg.LevelMax()
}

// IsCqMaxLv 传奇等级是不是满了
func (a *AttrModule) IsCqMaxLv() bool {
	return a.IsMaxLv() && a.Level2 >= cfg.LevelMax()
}

func (a *AttrModule) AddExp(exp int)   { a.Exp += exp }
func (a *AttrModule) AddExp2(exp int)  { a.Exp2 += exp }
func (a *AttrModule) AddLevel(lv int)  { a.Level += lv }
func (a *AttrModule) AddLevel2(lv int) { a.Level2 += lv }
