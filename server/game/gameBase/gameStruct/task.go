package gameStruct

import (
	"world/base/cfg"
	"world/common/pbGame"
	"world/common/pbGame/ConditionType"

	"github.com/samber/lo"
)

type Task struct {
	Id           int   `bson:"id, omitempty"`           // 任务id
	AcceptedTime int64 `bson:"acceptedTime, omitempty"` // 领取时间
}

// GetCfg 获取配置
func (t *Task) GetCfg() *cfg.Mission[int] {
	bean, _ := cfg.ContainerMission.GetBeanByUnique(t.Id)
	return bean
}

// GetNeedRecordCondition
/*
 * @description 获取任务配置中需要进行单独记录的条件
 * @return []*cfg.ConfigCondition
 */
func (t *Task) GetNeedRecordCondition() []*cfg.ConfigCondition {
	bean := t.GetCfg()
	if bean == nil {
		return nil
	}
	if bean.SubmitCondition == nil || len(bean.SubmitCondition) == 0 {
		return nil
	}

	filter := lo.Filter(bean.SubmitCondition, func(c *cfg.ConfigCondition, i int) bool {
		return isNeedRecord(ConditionType.Type(c.Type))
	})
	if len(filter) == 0 {
		return nil
	}
	return filter
}

// HasNeedRecordCondition
/*
 * @description 任务有没有需要进行单独记录的条件
 * @return []*cfg.ConfigCondition
 */
func (t *Task) HasNeedRecordCondition() bool {
	return t.GetNeedRecordCondition() != nil
}

func (t *Task) ToPb(taskModule *TaskModule) *pbGame.TaskInfo {
	info := &pbGame.TaskInfo{
		Id: int32(t.Id),
	}
	filter := t.GetNeedRecordCondition()
	if len(filter) > 0 {
		list := make([]*pbGame.Condition, 0)
		result := ConfigConditionConvert(filter...)
		for _, condition := range result.All() {
			switch condition.Type {
			// 杀怪任务的记录数据
			case ConditionType.KillMonster:
				val, ok := taskModule.KillRecord[condition.Id]
				condition.Num = lo.If(ok, val).Else(0)
			}
			list = append(list, condition.ToPb())
		}
		info.Cond = list
	}
	return info
}
