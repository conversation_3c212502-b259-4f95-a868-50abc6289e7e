package gameMgr

import (
	"fmt"
	"world/base/cfg"
	"world/common/pbBase/ModelConst"
	"world/common/pbBase/Response"
	"world/common/pbGame/SKILL_TYPE"
	"world/game/gameBase/gameStruct"

	"github.com/samber/lo"
	"github.com/sasha-s/go-deadlock"
)

var skillManagerLock deadlock.Once
var skillManager *SkillManager

// Skill 技能管理
func Skill() *SkillManager {
	skillManagerLock.Do(func() {
		skillManager = &SkillManager{}
	})
	return skillManager
}

type SkillManager struct {
}

// LeanSkillByShopId 在npc商店学习技能
//
// Parameters:
//   - plr *gameStruct.Player
//   - vo gameStruct.GameVo 要学技能的单位
//   - shopId int
//   - skillId int
//   - learnLevel int
//
// Returns:
//   - Response.Code
func (s *SkillManager) LeanSkillByShopId(plr *gameStruct.Player, vo gameStruct.GameVo, shopId, skillId, learnLevel int) Response.Code {
	bean, _ := cfg.ContainerSkillShop.GetBeanByUnique(fmt.Sprintf("%d-%d", shopId, skillId))
	if bean == nil {
		return Response.LearnSkillNotFound
	}
	if learnLevel <= 0 || bean.SkillMaxLevel < learnLevel {
		return Response.LearnSkillCanNotMoreThanMaxLevel
	}

	return s.DoLearnSkill(plr, vo, skillId, learnLevel, false)
}

// DoLearnSkill 学习技能
//
// Parameters:
//   - plr *gameStruct.Player 这个是扣消耗的单位 如果不传入 就不会扣消耗
//   - vo gameStruct.GameVo 这个是学技能的单位(玩家 or 宠物)
//   - skillId int 技能id
//   - learnLevel int 要学习的目标等级
//   - isLearnByBook bool 是否是通过技能书学习 主要是针对宠物
//
// Returns:
//   - Response.Code
func (s *SkillManager) DoLearnSkill(plr *gameStruct.Player, vo gameStruct.GameVo, skillId, learnLevel int, isLearnByBook bool) Response.Code {
	// 超过技能配置最大等级
	if cfg.GetSkillConfigMaxLevel(skillId) < learnLevel {
		return Response.LearnSkillCanNotMoreThanMaxLevel
	}

	skillMod := vo.SkillModule()
	skill := skillMod.GetSkillById(skillId)
	nextLevel := 1
	if skill != nil {
		nextLevel = skill.BaseLevel + 1
	}
	// 技能等级一定是从 1 到 max 的
	if nextLevel != learnLevel {
		return Response.LearnSkillLevelDoesNotMatch
	}
	bean, _ := cfg.ContainerSkill.GetBeanByUnique(fmt.Sprintf("%d-%d", skillId, nextLevel))
	if bean == nil {
		return Response.LearnSkillNotFound
	}
	// 跨职业
	isCrossJob := bean.ReqJob != 0 && vo.AttrModule().GetJob() != int64(bean.ReqJob)
	rate := cfg.ContainerMisc_C.GetObj().Skill.CrossJobRate

	// 计算消耗
	req := cfg.ContainerMisc_C.GetObj().Skill.ReqMoney
	money := req[nextLevel-1]
	typ, val := money.GetMoneyData()
	val = lo.If(isCrossJob, rate*val).Else(val)
	// 货币检查
	if plr != nil && !Player().IsMoneyEnough(plr, typ, val) {
		return Response.MoneyNotEnough
	}
	// 技能点检查
	nsp := nextLevel * 5 * rate
	if isLearnByBook {
		// 宠物用技能书消耗的技能点是额外的配置
		if vo.IsPet() {
			nsp = cfg.ContainerMisc_C.GetObj().Pet.SealSkillLearnSp[nextLevel-1]
		}
	}
	if nsp > 0 && vo.Get(ModelConst.SP) < nsp {
		return Response.LearnSkillSkillPointNotEnough
	}

	r := s.DoSkillLevelAdd(vo, skillId, 1, isLearnByBook)

	if r == Response.NoError {
		// 扣除消耗
		if plr != nil {
			Player().AddValue(plr, typ, -val)
		}
		Player().AddValue(vo, ModelConst.SP, -nsp)
		// 更新hp mp
		gameStruct.CheckHPMP(vo)
	}

	return r
}

// DoSkillLevelAdd 增加技能等级，不存在就创建一个新的技能
//
// Parameters:
//   - vo gameStruct.GameVo 这个是学技能的单位(玩家 or 宠物)
//   - skillId int 技能id
//   - level int 技能等级
//   - isLearnByBook bool 是否是通过技能书学习
//
// Returns:
//   - Response.Code
func (s *SkillManager) DoSkillLevelAdd(vo gameStruct.GameVo, skillId, level int, isLearnByBook bool) Response.Code {
	skillMod := vo.SkillModule()
	skill := skillMod.GetSkillById(skillId)
	if skill == nil {
		if isLearnByBook && skillMod.GetFreeSlot() <= 0 {
			return Response.LearnSkillSlotNotEnough
		}

		skill = &gameStruct.Skill{
			Id:            skillId,
			IsLearnByBook: isLearnByBook,
		}
		skillMod.List[skillId] = skill
	}

	skill.BaseLevel += level
	skill.IsLearnByBook = isLearnByBook

	return Response.NoError
}

// SetSkillAuto 设置自动释放的技能
//
// Parameters:
//   - vo gameStruct.Player 玩家
//   - isPet bool 是否是设置出战宠物
//   - isActive bool 是否是设置主动技能
//   - skillId int 技能id
//
// Returns:
//   - Response.Code
func (s *SkillManager) SetSkillAuto(vo *gameStruct.Player, isPet, isActive bool, skillId int) Response.Code {

	operationSkill := vo.SkillModule()
	if isPet {
		pet := vo.GetPet(vo.PetId)
		if pet == nil {
			return Response.E3515
		}
		operationSkill = pet.SkillModule()
	}

	skill := operationSkill.GetSkillById(skillId)
	if skill == nil {
		return Response.E3533
	}
	skillType := skill.GetJson().Type

	switch true {
	case isActive:
		if skillType != SKILL_TYPE.ACTIVE {
			return Response.E3534
		}
		operationSkill.ActiveAutoSkillID = lo.If(skill.Id == operationSkill.ActiveAutoSkillID, -1).Else(skill.Id)
	case !isActive:
		if skillType != SKILL_TYPE.ROUND {
			return Response.E3535
		}
		if !operationSkill.SetAutoSkillID(skill.Id) {
			return Response.E3536
		}
	}
	return Response.NoError
}
