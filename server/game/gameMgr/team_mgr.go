package gameMgr

import (
	"context"
	"world/base/enum/Define"
	"world/common/net_helper"
	"world/common/pbBase/Response"
	"world/common/pbCross"
	"world/common/pb_helper"
	"world/common/router"
	"world/db"
	"world/game/gameBase/gameStruct"
	ut "world/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	cmap "github.com/orcaman/concurrent-map/v2"
	"github.com/sasha-s/go-deadlock"
)

var teamManagerLock deadlock.Once
var teamManager *TeamManager

func Team() *TeamManager {
	teamManagerLock.Do(func() {
		teamManager = &TeamManager{}
		teamManager.dataMap = cmap.NewWithCustomShardingFunction[int, cmap.ConcurrentMap[int, *teamData]](func(key int) uint32 { return uint32(key) % 100 })
	})
	return teamManager
}

type teamData struct {
	Members []*mapUnit // 队员
	Leader  *mapUnit   // 队长
	MapId   int        // 所在地图id
}

type TeamManager struct {
	app     module.RPCModule
	dataMap cmap.ConcurrentMap[int, cmap.ConcurrentMap[int, *teamData]]
}

func (t *TeamManager) SetApp(app module.RPCModule) { t.app = app }
func (t *TeamManager) addMapTeam(mapId int) {
	t.dataMap.Set(mapId, cmap.NewWithCustomShardingFunction[int, *teamData](func(key int) uint32 { return uint32(key) % 100 }))
}

// 邀请加入队伍
func (t *TeamManager) DoTeamInvite(plr *gameStruct.Player, otherId int) Response.Code {
	if plr.GameId == otherId {
		return Response.E6001
	}
	// todo 先判断自己能创建队伍 ??
	mapTeamData, _ := t.dataMap.Get(plr.MapId)

	// 获取作为队长的已有队伍
	tmpTeamData, exists := mapTeamData.Get(plr.GameId)
	if exists {
		if len(tmpTeamData.Members) >= 4 {
			return Response.E6000
		}
	}

	// 拿对方所在节点
	r := db.GetRedis().Get(context.TODO(), db.RedisKeySessionNodeByGameId(otherId))
	// 拿不到节点信息 说明对方不在线了
	if r.Val() == "" {
		return Response.E6001
	}
	// 给对方发消息
	bytes, err := net_helper.Invoke(t.app, r.Val(), router.S2RNotifyTeamInviteMessage, &pbCross.S2R_NotifyTeamInviteMessage{
		Id:      int32(plr.GameId),
		Inviter: plr.ToCrossSimplePb(t.app),
	})
	if err != nil {
		if net_helper.IsNodeNotFoundError(err) {
			return Response.E6001
		}
		log.Debug("DoTeamInvite 通知对方失败:%v", err)
	}
	msg := &pbCross.R2S_SimpleResponseMessage{}
	err = pb_helper.ProtoUnMarshal(bytes, msg)
	if err != nil {
		log.Debug("DoTeamInvite 解析消息失败:%v", err)
	}
	return msg.Code
}

func (t *TeamManager) ProcessTeamInvite(id int, inviter *pbCross.CrossSimplePlayer) Response.Code {
	// 邀请入队的消息发过来了
	plr, _ := Player().TryGetPlayerByGameId(id)
	// 拿不到玩家数据 说明离线了
	if plr == nil {
		return Response.E6001
	}
	if plr.Leader != 0 {
		return Response.E6002
	}
	lock := Player().LockByUid(plr.Id)
	defer ut.Unlock(lock)

	plr.AddPlayerEvent(&pbCross.PlayerEvent{
		EventType:  int32(Define.PLAYER_EVENT_TEAM_INVITE),
		Message:    "",
		ExtraInfo:  "",
		ExpireTime: 60*ut.TIME_SECOND + ut.Now(), // 组队邀请 60秒后过期
		Player:     inviter,
	})
	return Response.NoError
}
