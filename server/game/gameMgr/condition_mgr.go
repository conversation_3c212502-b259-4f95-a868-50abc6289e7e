package gameMgr

import (
	"world/base/cfg"
	"world/common/pbGame/ConditionType"
	"world/game/gameBase/event"
	"world/game/gameBase/gameStruct"

	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
)

var conditionManagerLock deadlock.Once
var conditionManager *ConditionManager

// Condition 任务管理
func Condition() *ConditionManager {
	conditionManagerLock.Do(func() {
		conditionManager = &ConditionManager{}
		event.Require(conditionManager)
	})
	return conditionManager
}

type ConditionManager struct {
}

// Check
/*
 * @description 检查条件是否满足，全部满足返回true，否则返回false
 * @param plr
 * @param condition 可以是多个条件
 * @return bool 全部满足返回true，否则返回false
 */
func (c *ConditionManager) Check(plr *gameStruct.Player, condition ...*gameStruct.Condition) bool {
	cnt := len(condition)
	if plr == nil {
		return false
	}
	for _, con := range condition {
		if c.CheckOne(plr, con) {
			cnt--
		}
	}
	return cnt == 0
}

// CheckByConfig
/*
 * @description 使用配置条件检查条件是否满足，全部满足返回true，否则返回false
 * @param plr
 * @param cc 配置条件数据列表
 * @return bool
 */
func (c *ConditionManager) CheckByConfig(plr *gameStruct.Player, cc ...*cfg.ConfigCondition) bool {
	return c.Check(plr, gameStruct.ConfigConditionConvert(cc...).All()...)
}

// CheckAryByConfig
/*
 * @description 使用配置条件检查条件是否满足，返回不满足的列表
 * @param plr
 * @param cc 配置条件数据列表
 * @return []*gameStruct.Condition
 */
func (c *ConditionManager) CheckAryByConfig(plr *gameStruct.Player, cc ...*cfg.ConfigCondition) []*gameStruct.Condition {
	return c.CheckAry(plr, gameStruct.ConfigConditionConvert(cc...).All()...)
}

// CheckAry
/*
 * @description 检查条件是否满足,返回不满足的列表
 * @param plr
 * @param condition
 * @return []*gameStruct.Condition
 */
func (c *ConditionManager) CheckAry(plr *gameStruct.Player, condition ...*gameStruct.Condition) []*gameStruct.Condition {
	cnt := len(condition)
	failed := make([]*gameStruct.Condition, 0)
	if plr == nil || cnt == 0 {
		return failed
	}
	for _, con := range condition {
		if !c.CheckOne(plr, con) {
			failed = append(failed, con)
		}
	}
	return failed
}

// CheckOne
/*
 * @description 检查单个条件是否满足
 * @param plr 玩家
 * @param condition 条件
 * @return bool
 */
func (c *ConditionManager) CheckOne(plr *gameStruct.Player, condition *gameStruct.Condition) bool {
	if plr == nil {
		return false
	}
	if condition == nil {
		return true
	}
	// 需求条件的id
	needId := condition.Id
	// 需求数量
	needNum := condition.Num
	// 扩展参数  取决于类型
	// extra := condition.Extra
	// 当前数量
	curNum := 0
	switch condition.Type {
	case ConditionType.Level:
		curNum = plr.Attr.Level
	case ConditionType.KillMonster:
		mod := plr.Task
		if mod != nil {
			val, ok := mod.KillRecord[needId]
			if ok {
				curNum = val
			}
		}
	case ConditionType.Money1:
		curNum = plr.Bag.GetMoney1()
	case ConditionType.Money2:
		curNum = plr.Bag.GetMoney3()
	case ConditionType.Money3:
		curNum = plr.Bag.GetMoney3()
	case ConditionType.HaveItem:
		curNum = plr.Bag.GetItemNumById(needId)
	case ConditionType.MissionDone:
		return plr.Task.IsTaskFinished(needId)
	case ConditionType.MissionDoing:
		return plr.Task.GetTask(needId) != nil
	default:
		log.Error("未处理的条件类型: %v", condition.Type)
	}
	return curNum >= needNum
}
