package gameMgr

import (
	"context"
	"errors"
	"world/common/net_helper"
	"world/common/pbBase/Response"
	"world/common/pbCross"
	"world/common/pbLogin"
	"world/common/pb_helper"
	"world/common/router"
	"world/db"
	"world/game/gameBase/gameStruct"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	"github.com/sasha-s/go-deadlock"
	"google.golang.org/protobuf/reflect/protoreflect"
)

var senderManagerLock deadlock.Once
var senderManager *SenderManager

// Sender 消息发送
func Sender() *SenderManager {
	senderManagerLock.Do(func() {
		senderManager = &SenderManager{}
	})
	return senderManager
}

type SenderManager struct {
	app module.RPCModule
}

func (s *SenderManager) SetApp(app module.RPCModule) { s.app = app }

func (s *SenderManager) SendWithBytes(session gate.Session, topic string, msg []byte, sync bool) bool {
	if session == nil {
		return false
	}
	fun := session.SendNR
	if sync {
		fun = session.Send
	}
	err := fun(topic, msg)
	if err == "" {
		return true
	}
	log.Error("发送给客户端消息时出错:%s", err)
	return false
}

// Send 发送消息给客户端
func (s *SenderManager) Send(session gate.Session, topic string, msg protoreflect.ProtoMessage, sync bool) bool {
	return s.SendWithBytes(session, topic, pb_helper.ProtoMarshalForce(msg), sync)
}

// SendErr 发送错误消息
func (s *SenderManager) SendErr(session gate.Session, code Response.Code) bool {
	msg := &pbLogin.S2C_ErrorMessage{
		Code: code,
	}
	return s.Send(session, router.S2CErrorMessage, msg, true)
}

// SendTo
/*
 * @description 发送消息给玩家
 * @param player
 * @param topic
 * @param msg
 * @param sync 是否同步发送
 */
func (s *SenderManager) SendTo(player *gameStruct.Player, topic string, msg protoreflect.ProtoMessage, sync bool) bool {
	if player == nil {
		log.Error("发送消息给玩家时出错:player为空.")
		return false
	}
	return s.Send(player.Session, topic, msg, sync)
}

func (s *SenderManager) ForwardMsgWithBytes(id string, gameId int, route string, msg []byte, limitCnt int) (bytes []byte, err error) {
	nodeId := ""
	if id != "" {
		r := db.GetRedis().Get(context.TODO(), db.RedisKeySessionNodeId(id))
		nodeId = r.Val()
	}
	if nodeId == "" && gameId > 0 {
		r := db.GetRedis().Get(context.TODO(), db.RedisKeySessionNodeByGameId(gameId))
		nodeId = r.Val()
	}
	if nodeId == "" {
		return nil, errors.New(net_helper.NOT_FOUND)
	}
	return net_helper.Invoke(s.app, nodeId, router.S2RForwardMessage, &pbCross.S2R_ForwardMessage{
		Router: route,
		Msg:    msg,
		Id:     id,
		GameId: int32(gameId),
		Count:  int32(limitCnt),
	})
}

func (s *SenderManager) ForwardMsg(id string, gameId int, route string, msg protoreflect.ProtoMessage, limitCnt int) (bytes []byte, err error) {
	return s.ForwardMsgWithBytes(id, gameId, route, pb_helper.ProtoMarshalForce(msg), limitCnt)
}

// 通过玩家id  转发一个消息给该玩家 失败不重试 如需重试请使用ForwardMsg
func (s *SenderManager) ForwardMsgById(id string, router string, msg protoreflect.ProtoMessage) (bytes []byte, err error) {
	return s.ForwardMsg(id, 0, router, msg, 3)
}

// 通过玩家gameId  转发一个消息给该玩家 失败不重试 如需重试请使用ForwardMsg
func (s *SenderManager) ForwardMsgByGameId(id int, router string, msg protoreflect.ProtoMessage) (bytes []byte, err error) {
	return s.ForwardMsg("", id, router, msg, 3)
}
