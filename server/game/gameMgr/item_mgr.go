package gameMgr

import (
	"sort"
	"world/base/cfg"
	"world/base/structs"
	"world/common/pbBase/ModelConst"
	"world/common/pbBase/Response"
	"world/common/pbGame"
	"world/common/pbGame/Bag"
	"world/common/pbGame/BagReset"
	"world/common/pbGame/ConditionType"
	"world/common/pbGame/ITEM_CLASS"
	"world/common/pbGame/ITEM_ID"
	"world/common/pbGame/ITEM_TYPE"
	ITEM "world/common/pbGame/Item"
	"world/common/pbGame/MyDefine"
	"world/game/gameBase/event"
	"world/game/gameBase/gameStruct"
	"world/game/gameBase/types/PlayerBag"
	ut "world/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/sasha-s/go-deadlock"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/proto"
)

var itemOrder = map[ITEM_CLASS.Type]int{
	ITEM_CLASS.WEAPON:             0,
	ITEM_CLASS.ARMOR:              1,
	ITEM_CLASS.QUEST:              2,
	ITEM_CLASS.USE_ITEM:           3,
	ITEM_CLASS.PET_ADD_LIFE:       4,
	ITEM_CLASS.BOX_CHOOSE_ONE:     5,
	ITEM_CLASS.SEAL:               6,
	ITEM_CLASS.PET_EQUIP_EXP_BOOK: 7,
	ITEM_CLASS.PET_EQUIP:          8,
	ITEM_CLASS.PET:                9,
	ITEM_CLASS.GEM:                10,
	ITEM_CLASS.OTHER:              11,
	ITEM_CLASS.LN_STONE:           12,
}

var itemManagerLock deadlock.Once
var itemManager *ItemManager

// Item 背包物品管理
func Item() *ItemManager {
	itemManagerLock.Do(func() {
		itemManager = &ItemManager{}
		event.Require(itemManager)
	})
	return itemManager
}

type ItemManager struct {
}

// CreateItemById
/*
 * @description 创建物品
 * @param id 物品配置id
 * @param num 数量
 * @return []*gameStruct.Item
 * @return error
 */
func (i *ItemManager) CreateItemById(id, num int) []*gameStruct.Item {
	itemCfg, _ := cfg.ContainerItem.GetBeanByUnique(id)
	return i.CreateItemByItemCfg(itemCfg, num)
}

// CreateItemByRewardItem 创建物品
//
// Parameters:
//   - rewardItem *cfg.RewardItem
//
// Returns:
//   - []*gameStruct.Item
func (i *ItemManager) CreateItemByRewardItem(rewardItem *cfg.RewardItem) []*gameStruct.Item {
	return i.CreateItemById(rewardItem.Id, rewardItem.Quantity)
}

// CreateItemByItemCfg
/*
 * @description 创建物品
 * @param itemCfg 物品配置
 * @param num 数量
 * @return []*gameStruct.Item
 * @return error
 */
func (i *ItemManager) CreateItemByItemCfg(itemCfg *cfg.Item[int], num int) []*gameStruct.Item {
	ary := make([]*gameStruct.Item, 0)
	if num <= 0 || itemCfg == nil {
		return ary
	}
	for {
		item := &gameStruct.Item{}
		item.Id = itemCfg.Id
		ary = append(ary, item)
		// 装备属性
		if item.IsEquipClass() {
			item.Durability = itemCfg.DurMax
			item.Power1 = itemCfg.ParsePower1()
			item.Power2 = itemCfg.ParsePower2()
			item.BindPower1 = itemCfg.ParseBindPower1()
			item.BindPower2 = itemCfg.ParseBindPower2()
			item.Power3 = itemCfg.ParsePower3()
			if item.GetPower1().Type == MyDefine.POWER_IDENTIFY ||
				item.GetPower2().Type == MyDefine.POWER_IDENTIFY ||
				item.GetBindPower1().Type == MyDefine.POWER_IDENTIFY ||
				item.GetBindPower2().Type == MyDefine.POWER_IDENTIFY ||
				item.GetPower3().Type == MyDefine.POWER_COMPOSITE {
				item.SetIsIdentifyItem()
			}
		}
		// 时限
		if itemCfg.OwnTime > 0 {
			expTime := ut.Now() + int64(itemCfg.OwnTime)*ut.TIME_MINUTE
			item.SetExpireTime(expTime)
			item.SetIsTimeTime()
		}
		stackNum := itemCfg.StackNum
		// 时效物品 不可叠加
		if item.IsTimeItem() {
			stackNum = 1
		}
		item.Quantity = ut.Min(num, stackNum)
		num -= stackNum
		if num <= 0 {
			break
		}
	}
	return ary
}

// AddItem
/*
 * @description 添加物品到背包
 * @param plr
 * @param itemId
 * @param num
 * @return int
 */
func (i *ItemManager) AddItem(plr *gameStruct.Player, itemId, num int) int {
	items := i.CreateItemById(itemId, num)
	for _, item := range items {
		plr.Bag.AddItem1(item, false)
		// todo 检查能不能添加 然后 发邮件
	}
	return 0
}

// SortItems
/*
 * @description 物品排序
 * @param items
 * @param offset 重新设置slotPos时需要用到
 */
func (i *ItemManager) SortItems(items []*gameStruct.Item, offset int) {
	// 先合并能够合并的物品
	for j := len(items) - 1; j >= 0; j-- {
		item := items[j]
		// 不可操作，或者不可堆叠
		if item == nil || item.IsNotOperate() || !item.IsStackAble() {
			continue
		}
		total := item.Quantity
		for k := 0; k < j; k++ {
			compare := items[k]
			if compare == nil || item.Id != compare.Id || compare.IsNotOperate() || !compare.IsStackAble() {
				continue
			}
			if compare.Quantity < compare.Config().StackNum {
				diff := ut.Min(compare.Config().StackNum-compare.Quantity, total)
				compare.Quantity += diff
				total -= diff
			}
			if total <= 0 {
				items[j] = nil
				item = nil
				break
			}
		}
		if item != nil {
			item.Quantity = total
		}
	}
	// 开始排序
	sort.SliceStable(items, func(i, j int) bool {
		itemA := items[i]
		itemB := items[j]
		// 让nil排到后面
		if itemA == nil {
			return false
		}
		if itemB == nil {
			return true
		}
		// 获取排序顺序
		orderA, existsA := itemOrder[itemA.GetItemClass()]
		orderB, existsB := itemOrder[itemB.GetItemClass()]
		// 如果类型在映射中不存在，则默认为最大
		if !existsA {
			orderA = len(itemOrder) + 1
		}
		if !existsB {
			orderB = len(itemOrder) + 1
		}
		if orderA != orderB {
			return orderA < orderB
		}
		// 按品质排序
		if itemA.Config().Grade != itemB.Config().Grade {
			return itemA.Config().ReqLv > itemB.Config().ReqLv
		}
		// 按等级排序
		return itemA.Config().ReqLv > itemB.Config().ReqLv
	})

	for j := 0; j < len(items); j++ {
		item := items[j]
		if item != nil {
			item.SlotPos = j + offset
		}
	}
}

// BagRest
/*
 * @description 整理背包
 * @param plr
 * @param typ
 */
func (i *ItemManager) BagRest(plr *gameStruct.Player, typ BagReset.Type) Response.Code {
	bag := plr.Bag
	// 防止快速整理背包
	if !ut.IsTimeOut(bag.LastResetTime, ut.TIME_SECOND) {
		return Response.ErrBagResetSoFast
	}
	start, end := -1, -1
	offset := -1
	switch typ {
	case BagReset.TypeBag:
		start, end = bag.GetStartEndPos(PlayerBag.BAG)
		offset = PlayerBag.BAG_START
	case BagReset.TypeStore:
		start, end = bag.GetStartEndPos(PlayerBag.STORAGE)
		offset = PlayerBag.STORE_START
	case BagReset.TypeVipStore:
		start, end = bag.GetStartEndPos(PlayerBag.VIP_STORAGE)
		offset = PlayerBag.VIPSTORE_START
	default:
		return Response.ErrBagResetPanic
	}
	if start > -1 && end > start && offset != -1 {
		items := bag.Store[start : end+1]
		i.SortItems(items, offset)
	}
	bag.LastResetTime = ut.Now()
	return Response.NoError
}

// DoItemSell
/*
 * @description 物品出售 或者 丢弃
 * @param plr
 * @param typ 1出售 2丢弃
 * @param list 要操作的物品位置和对应的数量
 * @return pbBase.Code
 */
func (i *ItemManager) DoItemSell(plr *gameStruct.Player, typ int, list []*pbGame.ItemData) Response.Code {
	if list == nil || len(list) == 0 {
		return Response.ErrBagSellNoItem
	}
	bag := plr.Bag
	total := 0
	sell := make(map[int]int)
	for _, _data := range list {
		id := cast.ToInt(_data.Id)
		pos := cast.ToInt(_data.SlotPos)
		num := cast.ToInt(_data.Quantity)
		// 不能选择装备栏
		if pos < PlayerBag.EQUIP_POS_END || !bag.IsValidPos(pos) {
			return Response.ErrBagSellErrItem
		}
		item := bag.GetItem(pos)
		if item == nil {
			continue
		}
		if item.IsNotOperate() || item.Id != id {
			return Response.ErrBagSellErrItemState
		}
		if num <= 0 || num > item.Quantity {
			return Response.ErrBagSellErrItemNum
		}
		// 出售可以获得货币
		if typ == 1 {
			total += item.Config().Price * num
		}
		sell[pos] = num
	}
	for pos, num := range sell {
		bag.RemoveBagItemByPos(pos, num)
	}
	i.GrantReward(plr, gameStruct.NewCondition(ConditionType.Money3, -1, total))
	return Response.NoError
}

func (i *ItemManager) changeCostByCond(plr *gameStruct.Player, con *gameStruct.Condition, change int) {
	if con == nil {
		return
	}

	num := con.Num * change
	log.Info("[%d]changeCostByCond, type:%d, id:%d, num: %d", plr.GameId, con.Type, con.Id, num)
	switch con.Type {
	case ConditionType.Money1:
		plr.Bag.AddMoney1(num)
	case ConditionType.Money2:
		plr.Bag.AddMoney2(num)
	case ConditionType.Money3:
		plr.Bag.AddMoney3(num)
	case ConditionType.HaveItem:
		if num > 0 {
			i.AddItem(plr, con.Id, num)
		} else {
			// todo
		}
	}

}

// GrantReward
/*
 * @description 给予奖励
 * @param plr
 * @param con
 */
func (i *ItemManager) GrantReward(plr *gameStruct.Player, con *gameStruct.Condition) {
	i.changeCostByCond(plr, con, 1)
}

// GrantRewards 发奖
//
// Parameters:
//   - plr *gameStruct.Player
//   - con []*gameStruct.Condition
func (i *ItemManager) GrantRewards(plr *gameStruct.Player, con []*gameStruct.Condition) {
	lo.ForEach(con, func(condition *gameStruct.Condition, idx int) {
		i.GrantReward(plr, condition)
	})
}

func (i *ItemManager) DeductCost(plr *gameStruct.Player, con *gameStruct.Condition) {
	i.changeCostByCond(plr, con, -1)
}

func (i *ItemManager) DeductCosts(plr *gameStruct.Player, con []*gameStruct.Condition) {
	lo.ForEach(con, func(condition *gameStruct.Condition, idx int) {
		i.DeductCost(plr, condition)
	})
}

// DoEquipWear 装备穿戴
//
// Parameters:
//   - plr *gameStruct.Player
//   - itemId int 物品id
//   - pos int 装备位置
//
// Returns:
//   - Response.Code
func (i *ItemManager) DoEquipWear(plr *gameStruct.Player, itemId, pos int) Response.Code {
	bag := plr.Bag
	// 穿戴装备一定是操作背包中的道具
	if !bag.IsValidBagPos(pos) {
		return Response.ItemSlotPosError
	}
	item := bag.GetBagItem(pos)
	if item == nil {
		return Response.ItemCanNotEquip
	}
	// 仅宠物和装备能被穿戴
	if item.GetClass() != ITEM_CLASS.PET && item.GetClass() != ITEM_CLASS.WEAPON && item.GetClass() != ITEM_CLASS.ARMOR {
		return Response.ItemCanNotEquip
	}
	// 宠物上阵逻辑
	if item.GetClass() == ITEM_CLASS.PET {
		pet := item.PetItem
		if pet == nil {
			return Response.ItemCanNotEquip
		}
		if pet.GetAge() <= 0 {
			return Response.E3519
		}
		if plr.Get(ModelConst.LEVEL) < pet.Get(ModelConst.LEVEL) {
			return Response.ErrEquipRequireNotMetOfLevel
		}
		code := bag.SwapItem(PlayerBag.PET_POS, item.SlotPos)
		if code == Response.NoError {
			plr.PetId = item.PetId
		}
		return code
	}

	// 检查穿戴条件
	code := i.IsValidEquipRequire(plr, item)
	if code != Response.NoError {
		return code
	}
	// 物品是否过期
	if item.IsTimeItem() {
		if item.IsTimeItemTimeOut() || item.IsVipItemTimeOut() {
			return Response.ErrItemIsTimeout
		}
	}
	// 寻找适合的穿戴位置
	validPos := i.GetEquipPosByType(item.Config().Type)
	if len(validPos) == 0 {
		return Response.ErrItemCanNotFindWearPos
	}
	// 装备了单手武器，并且要穿戴的也是单手武器
	isOneHand := false
	if is, _ := i.IsHaveEquipOneHandHand(plr); is && item.Config().IsOneHandWeaponType() {
		isOneHand = true
	}
	tmp := validPos[0]
	// 如果是单手武器
	if !isOneHand && tmp == PlayerBag.WEAPON_LEFT_POS || tmp == PlayerBag.WEAPON_RIGHT_POS {
		if i.IsSameWeaponType(plr, item) || item.Config().IsTwoHandWeaponType() {
			return i.doEquipWeapon(plr, item)
		}
	}
	// 这里主要是处理 单手武器盒双持的一些逻辑
	for _, pos := range validPos {
		if !bag.IsValidPos(pos) {
			return Response.ItemSlotPosError
		}
		already := bag.GetItem(pos)
		// 有空位置 直接交换完成穿戴
		if already != nil {
			return bag.SwapItem(pos, item.SlotPos)
		}
		// 没有空位置 但是要装备的是单手武器并且已经装备的也是单手武器，直接交换
		if item.Config().Type == ITEM_TYPE.WEAPON_ONEHAND_HAND && already.Config().Type == ITEM_TYPE.WEAPON_ONEHAND_HAND {
			return bag.SwapItem(pos, item.SlotPos)
		}
		if isOneHand && already.Config().IsOneHandWeaponType() {
			return bag.SwapItem(pos, item.SlotPos)
		}
	}

	if item.Config().Type == ITEM_TYPE.WEAPON_ONEHAND_HAND || isOneHand {
		return Response.EquipWearUnknownError
	}

	// 交换
	return bag.SwapItem(validPos[0], item.SlotPos)
}

// doEquipWeapon 穿戴武器主要逻辑
//
// Parameters:
//   - plr *gameStruct.Player
//   - item *gameStruct.Item
//
// Returns:
//   - Response.Code
func (i *ItemManager) doEquipWeapon(plr *gameStruct.Player, item *gameStruct.Item) Response.Code {
	bag := plr.Bag
	pos := []int{PlayerBag.WEAPON_LEFT_POS, PlayerBag.WEAPON_RIGHT_POS}
	slot := 0
	for _, p := range pos {
		tmp := bag.GetBagItem(p)
		if tmp != nil {
			slot++
		}
	}

	if slot == 2 {
		// 保证至少有一个空位
		// 要特殊处理原本是两把单手  现在用一把双持武器去顶，那就存在卸下操作
		if bag.CountFreePos() < slot-1 {
			return Response.ErrBagIsFull
		}
		// 直接将左边的武器和目标武器交换
		code := bag.SwapItem(PlayerBag.WEAPON_LEFT_POS, item.SlotPos)
		if code != Response.NoError {
			return code
		}
		// 获取背包中的空位
		nextFreePos := bag.NextFreePosForType(PlayerBag.BAG)
		// 将右边的武器卸下来，放到空位置
		code = bag.SwapItem(PlayerBag.WEAPON_RIGHT_POS, nextFreePos)
		if code != Response.NoError {
			// 卸载失败，将左边的武器和目标武器再交换回来
			bag.SwapItem(item.SlotPos, PlayerBag.WEAPON_LEFT_POS)
			return code
		}
		return Response.NoError
	}
	if slot == 1 {
		if tmp := bag.GetItem(PlayerBag.WEAPON_RIGHT_POS); tmp != nil {
			code := bag.SwapItem(PlayerBag.WEAPON_LEFT_POS, PlayerBag.WEAPON_RIGHT_POS)
			if code != Response.NoError {
				return code
			}
		}
		code := bag.SwapItem(PlayerBag.WEAPON_LEFT_POS, item.SlotPos)
		if code != Response.NoError {
			return code
		}
		return Response.NoError
	}
	if slot == 0 {
		code := bag.SwapItem(PlayerBag.WEAPON_LEFT_POS, item.SlotPos)
		if code != Response.NoError {
			return code
		}
	}
	return Response.NoError
}

func (i *ItemManager) DoEquipTakeOff(plr *gameStruct.Player, itemId, pos int) Response.Code {
	bag := plr.Bag
	if !bag.IsValidEquipPos(pos) {
		return Response.ItemSlotPosError
	}
	item := bag.GetItem(pos)
	if item == nil || item.IsNotOperate() {
		return Response.ItemCanNotOperate
	}
	// 找空位
	freePos := bag.NextFreePosForType(PlayerBag.BAG)
	if freePos == -1 {
		return Response.ErrBagIsFull
	}
	if item.GetClass() == ITEM_CLASS.PET {
		plr.PetId = 0
		return bag.SwapItem(pos, freePos)
	}

	// 处理单手副武器情况
	oneHand := false
	isHaveEquipOneHandHand, wPos := i.IsHaveEquipOneHandHand(plr)
	if isHaveEquipOneHandHand && item.Config().Type == ITEM_TYPE.WEAPON_ONEHAND_HAND {
		oneHand = true
	}
	// 单手装备取下 有2个空位
	if oneHand && bag.CountFreePos() < 2 {
		return Response.ErrBagIsFull
	}
	bag.SwapItem(pos, freePos)
	if oneHand {
		freePos = bag.NextFreePosForType(PlayerBag.BAG)
		if bag.IsValidBagPos(freePos) {
			bag.SwapItem(wPos, freePos)
		}
	}
	return Response.NoError
}

// IsValidEquipRequire 验证装备穿戴需求
//
// Parameters:
//   - plr *gameStruct.Player
//   - item *gameStruct.Item
//
// Returns:
//   - Response.Code 不为 Response.NoError 则不满足
func (i *ItemManager) IsValidEquipRequire(plr *gameStruct.Player, item *gameStruct.Item) Response.Code {
	code := Response.NoError
	check := func(current, require int, err Response.Code) {
		if code != Response.NoError {
			return
		}
		if current < require {
			code = err
		}
	}
	check(plr.Get(ModelConst.STR), item.Config().ReqStr, Response.ErrEquipRequireNotMetOfStr)
	check(plr.Get(ModelConst.CON), item.Config().ReqCon, Response.ErrEquipRequireNotMetOfCon)
	check(plr.Get(ModelConst.AGI), item.Config().ReqAgi, Response.ErrEquipRequireNotMetOfAgi)
	check(plr.Get(ModelConst.ILT), item.Config().ReqIlt, Response.ErrEquipRequireNotMetOfIlt)
	check(plr.Get(ModelConst.WIS), item.Config().ReqWis, Response.ErrEquipRequireNotMetOfWis)
	check(plr.Get(ModelConst.LEVEL), item.Config().ReqLv, Response.ErrEquipRequireNotMetOfLevel)
	return code
}

// GetEquipPosByType 使用类型获取装备可以被穿戴的位置
//
// Parameters:
//   - itemType ITEM_TYPE.Type
//
// Returns:
//   - out []int
func (i *ItemManager) GetEquipPosByType(itemType ITEM_TYPE.Type) (out []int) {
	switch itemType {
	case ITEM_TYPE.ARMOR_HEAD:
		out = append(out, PlayerBag.ARMOR_HEAD_POS)
	case ITEM_TYPE.ARMOR_CLOTHES:
		out = append(out, PlayerBag.ARMOR_CLOTHES_POS)
	case ITEM_TYPE.ARMOR_TROUSERS:
		out = append(out, PlayerBag.ARMOR_TROUSERS_POS)
	case ITEM_TYPE.ARMOR_SHOULDER:
		out = append(out, PlayerBag.ARMOR_SHOULDER_POS)
	case ITEM_TYPE.ARMOR_WAIST:
		out = append(out, PlayerBag.ARMOR_WAIST_POS)
	case ITEM_TYPE.ARMOR_BACK:
		out = append(out, PlayerBag.ARMOR_BACK_POS)
	case ITEM_TYPE.ARMOR_SHOES:
		out = append(out, PlayerBag.ARMOR_SHOES_POS)
	case ITEM_TYPE.ARMOR_HAND:
		out = append(out, PlayerBag.ARMOR_HAND_POS)
	case ITEM_TYPE.ARMOR_NECKLACE:
		out = append(out, PlayerBag.ARMOR_NECKLACE_POS)
	case ITEM_TYPE.ARMOR_RING:
		out = append(out, PlayerBag.ARMOR_RING_LEFT_POS, PlayerBag.ARMOR_RING_RIGHT_POS)
	case ITEM_TYPE.ARMOR_AMULET:
		out = append(out, PlayerBag.ARMOR_AMULET_POS)
	case ITEM_TYPE.ARMOR_TRANSPORT:
		out = append(out, PlayerBag.ARMOR_TRANSPORT_POS)
	case ITEM_TYPE.ARMOR_FASHION:
		out = append(out, PlayerBag.ARMOR_FASHION_POS)
	case ITEM_TYPE.WEAPON_ONEHAND_SWORD:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_SWORD:
		fallthrough
	case ITEM_TYPE.WEAPON_ONEHAND_BLADE:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_BLADE:
		fallthrough
	case ITEM_TYPE.WEAPON_ONEHAND_HEAVY:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_HEAVY:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_STAFF:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_LANCE:
		fallthrough
	case ITEM_TYPE.WEAPON_ONEHAND_CROSSBOW:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_CROSSBOW:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_BOW:
		fallthrough
	case ITEM_TYPE.WEAPON_ONEHAND_HAND:
		fallthrough
	case ITEM_TYPE.WEAPON_BALL:
		fallthrough
	case ITEM_TYPE.WEAPON_ONEHAND_GUN:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_GUN:
		fallthrough
	case ITEM_TYPE.WEAPON_ONEHAND_HAMMER:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_HAMMER:
		fallthrough
	case ITEM_TYPE.WEAPON_TWOHAND_FAN:
		out = append(out, PlayerBag.WEAPON_LEFT_POS, PlayerBag.WEAPON_RIGHT_POS)
	case ITEM_TYPE.PET:
		out = append(out, PlayerBag.PET_POS)
	case ITEM_TYPE.BLOOD_BOTTLE:
		out = append(out, PlayerBag.BLOOD_BOTTLE_POS)
	}
	return
}

// IsHaveEquipOneHandHand 是不是装备了副手装备
//
// Parameters:
//   - plr *gameStruct.Player
//
// Returns:
//   - is bool 是 否
//   - pos int 如果是 返回位置
func (i *ItemManager) IsHaveEquipOneHandHand(plr *gameStruct.Player) (is bool, pos int) {
	bag := plr.Bag
	item := bag.GetItem(PlayerBag.WEAPON_LEFT_POS)
	if item != nil && item.Config().Type == ITEM_TYPE.WEAPON_ONEHAND_HAND {
		return true, PlayerBag.WEAPON_LEFT_POS
	}

	item = bag.GetItem(PlayerBag.WEAPON_RIGHT_POS)
	if item != nil && item.Config().Type == ITEM_TYPE.WEAPON_ONEHAND_HAND {
		return true, PlayerBag.WEAPON_RIGHT_POS
	}
	return false, 0
}

func (i *ItemManager) IsSameWeaponType(plr *gameStruct.Player, item *gameStruct.Item) bool {
	bag := plr.Bag
	if bag == nil {
		return true
	}
	typ := item.Config().Type
	if tmp := bag.GetItem(PlayerBag.WEAPON_LEFT_POS); tmp != nil && tmp.Config().Type != typ {
		return true
	}
	if tmp := bag.GetItem(PlayerBag.WEAPON_RIGHT_POS); tmp != nil && tmp.Config().Type != typ {
		return true
	}
	if tmp := bag.GetItem(PlayerBag.WEAPON_LEFT_POS); tmp != nil && tmp.Config().Type != typ {
		return true
	}

	if bag.GetItem(PlayerBag.WEAPON_LEFT_POS) == nil || bag.GetItem(PlayerBag.WEAPON_RIGHT_POS) == nil {
		return false
	}

	if typ == ITEM_TYPE.WEAPON_ONEHAND_HAND {
		leftWeaponType := ITEM_TYPE.SPECIAL
		leftIsWeaponOneHand := false
		if tmp := bag.GetItem(PlayerBag.WEAPON_LEFT_POS); tmp != nil {
			leftWeaponType = tmp.Config().Type
			leftIsWeaponOneHand = !tmp.Config().IsOneHandWeaponType()
		}
		rightWeaponType := ITEM_TYPE.SPECIAL
		rightIsWeaponOneHand := false
		if tmp := bag.GetItem(PlayerBag.WEAPON_RIGHT_POS); tmp != nil {
			rightWeaponType = tmp.Config().Type
			rightIsWeaponOneHand = !tmp.Config().IsOneHandWeaponType()
		}

		// 右手有穿戴武器并且不是单手武器
		rightIsNotOneHandWeapon := rightWeaponType != ITEM_TYPE.SPECIAL && rightWeaponType != ITEM_TYPE.WEAPON_ONEHAND_HAND
		// 左手有穿戴武器并且不是单手武器
		leftIsNotOneHandWeapon := leftWeaponType != ITEM_TYPE.SPECIAL && leftWeaponType != ITEM_TYPE.WEAPON_ONEHAND_HAND

		if leftIsWeaponOneHand || rightIsNotOneHandWeapon {
			if rightIsWeaponOneHand || leftIsNotOneHandWeapon {
				return true
			}
		}
	}

	return false
}

// CreatePetItem 创建宠物物品(宠物也是物品)
//
// Parameters:
//   - plr *gameStruct.Player
//   - petId int
//
// Returns:
//   - *gameStruct.Item
//   - *gameStruct.Pet
//   - bool 是否执行添加操作
//   - Response.Code
func (i *ItemManager) CreatePetItem(plr *gameStruct.Player, petId int, add bool) (*gameStruct.Item, *gameStruct.Pet, Response.Code) {
	pet, code := Pet().CreatePet(plr, petId)
	if pet == nil {
		log.Error("创建宠物失败？？, petId:%d, code:%d", petId, code)
		return nil, nil, code
	}
	// 宠物物品占用24号id
	itemCfg, _ := cfg.ContainerItem.GetBeanByUnique(int(ITEM_ID.PET))
	item := i.CreateItemByItemCfg(itemCfg, 1)[0]
	item.PetItem = pet
	item.PetId = structs.GetNextPetId()
	if add {
		// 检查
		r := plr.Bag.AddItem1(item, true)
		if r < 0 {
			return nil, nil, Response.ErrBagIsFull
		}
		plr.Bag.AddItem1(item, false)
	}
	return item, pet, Response.NoError
}

// AfterEquipChanged 穿戴的装备发生改变事件
//
// Parameters:
//   - plr *gameStruct.Player
func (i *ItemManager) AfterEquipChanged(plr *gameStruct.Player, itemId int) {
	log.Debug("装备发生改变: %d", plr.GameId)
	plr.UpdateIcon()
	plr.UpdateItemSetData()
	// 更新hp mp
	gameStruct.CheckHPMP(plr)
}

func (i *ItemManager) DoWorldUseItemAction(plr *gameStruct.Player, baseType Bag.ItemUseType, itemSlotPos, itemId, useNum, extraId int) (code Response.Code, response proto.Message) {
	bag := plr.Bag
	item := bag.GetBagItem(itemSlotPos)

	if item == nil {
		return Response.E3512, nil
	}
	if item.IsNotOperate() {
		return Response.ItemCanNotOperate, nil
	}
	// 是否移除物品
	removeItem := true
	// 操作附带的的物品
	var targetItem *gameStruct.Item
	if extraId != -1 {
		if extraId < PlayerBag.BAG_START {
			targetItem = bag.GetItem(extraId)
			if targetItem != nil && targetItem.GetClass() != ITEM_CLASS.PET {
				return Response.E3516, nil
			}
		} else {
			targetItem = bag.GetBagItem(extraId)
		}
		if targetItem == nil {
			return Response.E3512, nil
		}
	}
	/* 下面是丢弃物品 */
	if baseType == Bag.Lose {
		if bag.IsValidEquipPos(itemSlotPos) {
			return lo.If(item.IsPet(), Response.E3522).Else(Response.E3523), nil
		}
		// todo 判断宠物装备

		// 丢弃就是全部移除
		useNum = item.Quantity
	}

	/* 下面是使用物品 */
	if baseType == Bag.Use || baseType == Bag.UseByOneKey {
		// 判断物品能不能在世界使用
		if !item.IsCanUse(ITEM.CAN_USE_WORLD) {
			return Response.E3513, nil
		}
		// 改名卡不能直接使用
		if item.IsChangeNameItem() {
			return Response.ItemCanNotOperate, nil
		}

		// 宠物相关
		var pet *gameStruct.Pet = nil
		if targetItem != nil {
			pet = targetItem.PetItem
		}
		sendPetChange := false
		orgGrowExp := 0
		orgGrowLevel := 0
		if pet != nil {
			orgGrowExp = pet.GrowExp
			orgGrowLevel = pet.GrowLevel
		}
		debugText := ""

		switch true {
		case item.IsChangeJobItem():
			debugText = "使用转职书"
		case item.IsPetEgg():
			debugText = "使用宠物蛋"
		case item.IsChestItem():
			debugText = "开启宝箱"
		case item.IsCountryBook():
			debugText = "使用指令书"
		case item.IsOpenStoreItem():
			debugText = "开通仓库格子"
		case item.IsPetAddSkill():
			if pet.AttrModule().Level < 40 {
				return Response.E3517, nil
			}
			old := pet.GetLearnSkill(true)
			skills := Pet().RandomPetPotencySkill(pet)
			// 首次使用潜能石 技能直接加入
			if len(old) == 0 {
				for _, skill := range skills {
					pet.SkillModule().List[skill.Id] = skill
				}
			} else {
				pet.PotencySkill = skills
			}
			response = &pbGame.ItemUseResultPetAddSkillItem{
				Old: lo.Map(old, func(skill *gameStruct.Skill, _ int) int32 { return int32(skill.Id) }),
				New: lo.Map(skills, func(skill *gameStruct.Skill, _ int) *pbGame.SkillInfo { return skill.ToPb() }),
			}
		case item.IsPetAgeItem():
			debugText = "使用宠物返老还童石"
			if pet.GetAge() >= 99 {
				return Response.E3518, nil
			}
			pet.Age = ut.Now() + ut.TIME_DAY*100
		case item.IsPetResetItem():
			if pet == nil {
				return Response.E3515, nil
			}
			sendPetChange = true
			if item.Id == int(ITEM_ID.PET_RESET) {
				debugText = "使用宠物洗髓石"
				pet.Create(plr)
			}
			if item.Id == int(ITEM_ID.PET_RESET_2) {
				debugText = "使用宠物重生石"
				pet.Rebirth()
			}
		case item.IsPetExpItem():
			if pet == nil {
				return Response.E3515, nil
			}
			debugText = "使用宠物经验书"
			Player().AddPlayerExp(int(item.GetPower1().Value), pet)
		case item.IsPlayerExpItem():
			debugText = "使用人物经验书"
			Player().AddPlayerExp(int(item.GetPower1().Value), plr)
		case item.IsRepairItem():
			debugText = "使用野外修理卷"
		case item.IsTitleItem():
			debugText = "使用称号道具"
		case item.IsChangeSexItem():
			debugText = "使用变性卷轴"
		case item.IsCpPointAddItem():
			debugText = "使用属性点增加道具"
		case item.IsSpPointAddItem():
			debugText = "使用技能点增加道具"
		case item.IsProsperityDegreePointAddItem():
			debugText = "使用繁荣度增加道具"
		case item.IsSkillPlayerItem():
			debugText = "使用人物技能槽"
		case item.IsSkillPetItem():
			debugText = "使用宠物技能槽"
		case item.IsAddBagSizeItem():
			debugText = "使用背包格子增加道具"
		case item.IsTimeItem():
			debugText = "使用限时道具"
		case item.IsVipItem():
			debugText = "使用VIP道具"
		case item.IsPetAddLife():
			debugText = "使用增加宠物寿命道具"
			if pet.GetAge() >= 99 {
				return Response.E3518, nil
			}
			pet.Age = ut.Now() + ut.TIME_DAY*int64(item.Config().PowerValueBlood1)
		default:
			debugText = "使用未知道具"
			removeItem = false
		}
		if pet != nil {
			// 判断宠物有没有改变
			if sendPetChange || pet.GrowExp != orgGrowExp || pet.GrowLevel != orgGrowLevel {
				response = pet.ToPb()
			}
		}
		if debugText != "" {
			log.Debug("%d: %s", plr.GameId, debugText)
		}
	}
	if removeItem {
		bag.RemoveBagItemByPos(itemSlotPos, useNum)
	}

	return Response.NoError, response
}

// DoItemBind 物品绑定
//
// Parameters:
//   - plr *gameStruct.Player
//   - id int 物品id
//   - slotPos int 物品位置
//
// Returns:
//   - Response.Code
func (i *ItemManager) DoItemBind(plr *gameStruct.Player, id, slotPos int) Response.Code {
	bag := plr.BagModule()
	item := bag.GetBagItem(slotPos)
	if item == nil {
		return Response.E3512
	}
	// 操作的物品id不一致 保险起见
	if item.Id != id {
		return Response.E3537
	}
	if item.IsBinded() {
		return Response.E3537
	}
	if item.IsNotOperate() {
		return Response.E3538
	}
	item.SetBind()
	return Response.NoError
}

// DoItemIdentify 物品鉴定
//
// Parameters:
//   - plr *gameStruct.Player
//   - id int
//   - slotPos int
//
// Returns:
//   - Response.Code
func (i *ItemManager) DoItemIdentify(plr *gameStruct.Player, id, slotPos int, isUpgrade bool) (Response.Code, *gameStruct.Item) {
	bag := plr.BagModule()
	item := bag.GetBagItem(slotPos)
	if item == nil {
		return Response.E3512, nil
	}

	if !item.IsEquipClass() || !item.IsIdentifyItem() {
		return Response.E3539, nil
	}
	// 操作的物品id不一致 保险起见
	if item.Id != id {
		return Response.E3537, nil
	}
	if item.IsNotOperate() {
		return Response.E3538, nil
	}
	if item.IsEquited() {
		return Response.E3516, nil
	}
	if isUpgrade && !item.IsUpgrade() {
		return Response.E3540, nil
	}
	// 鉴定配置
	config, exists := cfg.ContainerIdentity.GetBeanByUnique(item.Id)
	if !exists {
		return Response.E3541, nil
	}

	clone := item.Clone()
	toBase := make([]*pbGame.PowerData, 0)
	toClone := make([]*pbGame.PowerData, 0)
	from := make([][]*cfg.IdentifyPower, 0)
	cost := make([]int32, 0)
	switch true {
	case isUpgrade:
		if bag.GetUpgradeIdentifyScrollNum() < 1 {
			return Response.E3544, nil
		}
		if config.Power4 != nil {
			from = append(from, config.Power4)
			toBase = append(toBase, item.Power4)
			toClone = append(toClone, clone.Power4)
		}
		if config.Power5 != nil {
			from = append(from, config.Power5)
			toBase = append(toBase, item.Power5)
			toClone = append(toClone, clone.Power5)
		}
		if config.Power6 != nil {
			from = append(from, config.Power6)
			toBase = append(toBase, item.Power6)
			toClone = append(toClone, clone.Power6)
		}
		if config.Power7 != nil {
			from = append(from, config.Power7)
			toBase = append(toBase, item.Power7)
			toClone = append(toClone, clone.Power7)
		}
		cost = append(cost, int32(ITEM_ID.UPGRADE_IDENTIFY_SCROLL_BIND), int32(ITEM_ID.UPGRADE_IDENTIFY_SCROLL))
	default:
		if config.Power1 != nil {
			from = append(from, config.Power1)
			toBase = append(toBase, item.Power1)
			toClone = append(toClone, clone.Power1)
		}
		if config.Power2 != nil {
			from = append(from, config.Power2)
			toBase = append(toBase, item.Power2)
			toClone = append(toClone, clone.Power2)
		}
		if config.BindPower1 != nil {
			from = append(from, config.BindPower1)
			toBase = append(toBase, item.BindPower1)
			toClone = append(toClone, clone.BindPower1)
		}
		if config.BindPower2 != nil {
			from = append(from, config.BindPower2)
			toBase = append(toBase, item.BindPower2)
			toClone = append(toClone, clone.BindPower2)
		}
		if clone.Config().ReqLv > 65 {
			if bag.GetHighIdentifyScrollNum() < 1 {
				return Response.E3543, nil
			}
			cost = append(cost, int32(ITEM_ID.HIGH_IDENTIFY_SCROLL_BIND), int32(ITEM_ID.HIGH_IDENTIFY_SCROLL))
		} else {
			if bag.GetIdentifyScrollNum() < 1 {
				return Response.E3542, nil
			}
			cost = append(cost, int32(ITEM_ID.IDENTIFY_SCROLL_BIND), int32(ITEM_ID.IDENTIFY_SCROLL))
		}
	}

	isNew := lo.ContainsBy(toClone, func(power *pbGame.PowerData) bool {
		return power.Type == MyDefine.POWER_IDENTIFY
	})
	bag.LastIdentifyResult = make([]*pbGame.PowerData, 0)
	for i, v := range from {
		if v == nil {
			continue
		}
		it := toClone[i]
		// 先暂时从数组中随机出一个
		rangeDta, _ := ut.RandomIn(v)
		powerType := rangeDta.PowerType
		powerValue := ut.Random(rangeDta.Min, rangeDta.Max)
		it.Type = powerType
		it.Value = int32(powerValue)
		// 第一次鉴定 直接替换
		if isNew {
			toBase[i].Type = powerType
			toBase[i].Value = int32(powerValue)
			continue
		}
		bag.LastIdentifyItemId = id
		bag.LastIdentifyItemSlotPos = slotPos
		bag.LastIdentifyIsUpgrade = isUpgrade
		// 鉴定结果
		bag.LastIdentifyResult = append(bag.LastIdentifyResult, &pbGame.PowerData{
			Type:  powerType,
			Value: int32(powerValue),
		})
	}

	deduct := false
	for _, v := range cost {
		deduct = bag.RemoveBagItemByNum(int(v), 1)
		if deduct {
			break
		}
	}
	// 未扣除成功？
	if !deduct {
		bag.LastIdentifyResult = nil
		return Response.E3545, nil
	}

	return Response.NoError, clone
}

func (i *ItemManager) DoItemIdentifyAnswer(plr *gameStruct.Player, id, slotPos int) Response.Code {
	bag := plr.BagModule()

	if len(bag.LastIdentifyResult) == 0 || bag.LastIdentifyItemId != id || bag.LastIdentifyItemSlotPos != slotPos {
		return Response.E3537
	}

	item := bag.GetBagItem(bag.LastIdentifyItemSlotPos)
	if item == nil {
		return Response.E3512
	}
	// 鉴定配置
	config, exists := cfg.ContainerIdentity.GetBeanByUnique(item.Id)
	if !exists {
		return Response.E3541
	}
	to := make([]*pbGame.PowerData, 0)
	switch true {
	case bag.LastIdentifyIsUpgrade:
		if config.Power4 != nil {
			to = append(to, item.Power4)
		}
		if config.Power5 != nil {
			to = append(to, item.Power5)
		}
		if config.Power6 != nil {
			to = append(to, item.Power6)
		}
		if config.Power7 != nil {
			to = append(to, item.Power7)
		}
	default:
		if config.Power1 != nil {
			to = append(to, item.Power1)
		}
		if config.Power2 != nil {
			to = append(to, item.Power2)
		}
		if config.BindPower1 != nil {
			to = append(to, item.BindPower1)
		}
		if config.BindPower2 != nil {
			to = append(to, item.BindPower2)
		}
	}

	if !item.IsEquipClass() || !item.IsIdentifyItem() {
		return Response.E3539
	}
	if item.Id != id {
		return Response.E3537
	}
	if item.IsNotOperate() {
		return Response.E3538
	}
	if item.IsEquited() {
		return Response.E3516
	}
	if bag.LastIdentifyIsUpgrade && !item.IsUpgrade() {
		return Response.E3540
	}

	for i, v := range bag.LastIdentifyResult {
		if v == nil {
			continue
		}
		to[i].Type = v.Type
		to[i].Value = v.Value
	}

	bag.LastIdentifyIsUpgrade = false
	bag.LastIdentifyItemId = -1
	bag.LastIdentifyItemSlotPos = -1
	bag.LastIdentifyResult = nil
	return Response.NoError
}

func (i *ItemManager) DoItemStar(plr *gameStruct.Player, id, slotPos int, isUpgrade bool) (Response.Code, bool) {
	bag := plr.BagModule()
	item := bag.GetBagItem(slotPos)
	if item == nil {
		return Response.E3512, false
	}
	if !item.IsBinded() {
		return Response.E3548, false
	}

	if !item.IsEquipClass() {
		return Response.E3539, false
	}
	// 操作的物品id不一致 保险起见
	if item.Id != id {
		return Response.E3537, false
	}
	if item.IsNotOperate() {
		return Response.E3538, false
	}
	if isUpgrade && !item.IsUpgrade() {
		return Response.E3540, false
	}
	// 升星上限暂时是等级上限减去装备等级要求
	starMax := item.Config().ReqLv
	starMax = cfg.LevelMax() - starMax

	cost := make([]int32, 0)
	var handle func()
	switch true {
	case isUpgrade:
		if item.UpgradeStar >= starMax {
			return Response.E3547, false
		}
		cost = append(cost, int32(ITEM_ID.UPGRADE_INTENSIFY_SCROLL_BIND), int32(ITEM_ID.UPGRADE_INTENSIFY_SCROLL))
		handle = func() { item.UpgradeStar++ }
	default:
		if item.Star >= starMax {
			return Response.E3547, false
		}
		if bag.GetStarScrollNum() < 1 {
			return Response.E3546, false
		}
		cost = append(cost, int32(ITEM_ID.STAR_SCROLL))
		handle = func() { item.Star++ }
	}

	deduct := false
	for _, v := range cost {
		deduct = bag.RemoveBagItemByNum(int(v), 1)
		if deduct {
			break
		}
	}
	if !deduct {
		return Response.E3545, false
	}
	if ut.Chance(50) {

		handle()
		return Response.NoError, true
	}
	return Response.NoError, false
}

// DoItemEnchase 宝石镶嵌
//
// Parameters:
//   - plr *gameStruct.Player
//   - itemSlotPos int 物品位置
//   - gemSlotPos int 宝石位置
//
// Returns:
//   - Response.Code
//   - bool false代表成功镶嵌，true代表失败破损
func (i *ItemManager) DoItemEnchase(plr *gameStruct.Player, itemSlotPos, gemSlotPos int) (Response.Code, bool) {
	bag := plr.BagModule()
	item := bag.GetBagItem(itemSlotPos)
	gem := bag.GetBagItem(gemSlotPos)
	if item == nil || gem == nil {
		return Response.E3512, false
	}

	if !item.IsEquipClass() {
		return Response.E3539, false
	}
	if item.IsNotOperate() {
		return Response.E3538, false
	}
	if !item.IsCanAttach() {
		return Response.E3550, false
	}
	if gem.Config().Type != ITEM_TYPE.GEM {
		return Response.E3549, false
	}
	if item.AttachDone >= item.Config().AttachCount {
		return Response.E3551, false
	}

	rate := 100
	if !item.IsAttachBroken() {
		if item.AttachPower != nil && item.AttachPower.Type != MyDefine.POWER_NONE {
			if item.AttachPower.Type != gem.Config().ParsePower1().Type {
				return Response.E3552, false
			}
		}
		if item.AttachDone >= 3 {
			rate = 50
		}
	}

	// 移除宝石
	bag.RemoveBagItemByPos(gemSlotPos, 1)
	if !ut.Chance(rate) {
		item.SetAttachBroken(true)
		return Response.NoError, true
	}
	if item.IsAttachBroken() {
		item.AttachDone = 0
		item.AttachPower = nil
		item.SetAttachBroken(false)
	}

	if item.AttachPower == nil {
		item.AttachPower = &pbGame.PowerData{
			Type:  gem.Config().ParsePower1().Type,
			Value: 0,
		}
	}
	item.AttachPower.Value += gem.Config().ParsePower1().Value
	item.AttachDone++

	plr.UpdateIcon()
	gameStruct.CheckHPMP(plr)

	return Response.NoError, false
}

// DoItemGemReplace 宝石替换
//
// Parameters:
//   - plr *gameStruct.Player
//   - itemSlotPos int
//   - gemSlotPos int
//
// Returns:
//   - Response.Code
func (i *ItemManager) DoItemGemReplace(plr *gameStruct.Player, itemSlotPos, gemSlotPos int) Response.Code {
	bag := plr.BagModule()
	item := bag.GetBagItem(itemSlotPos)
	gem := bag.GetBagItem(gemSlotPos)
	if item == nil || gem == nil {
		return Response.E3512
	}

	if item.AttachPower != nil && item.AttachPower.Value < 20 {
		return Response.E3553
	}
	if !item.IsReplaceInlayGem() {
		return Response.E3556
	}

	needCnt := item.AttachDone * 2
	hasCnt := gem.Quantity
	cost := make([]*gameStruct.Item, 0)
	cost = append(cost, gem)

	if hasCnt < needCnt {
		gemAry := bag.GetItemBy(func(tItem *gameStruct.Item) bool {
			if tItem.Config().Type != ITEM_TYPE.GEM {
				return false
			}
			tPower1 := tItem.Config().ParsePower1()
			if tPower1 == nil {
				return false
			}
			gPower1 := gem.Config().ParsePower1()
			return tPower1.Type == gPower1.Type && tPower1.Value == gPower1.Value
		})
		cost = cost[:0]
		cost = append(cost, gemAry...)
	}
	hasCnt = lo.SumBy(cost, func(item *gameStruct.Item) int { return item.Quantity })
	if hasCnt < needCnt {
		return Response.E3554
	}

	costMap := map[int]int{}
	for _, v := range cost {
		if needCnt <= 0 {
			break
		}
		num := ut.Min(needCnt, v.Quantity)
		needCnt -= num
		costMap[v.SlotPos] = num
	}
	if needCnt > 0 {
		return Response.E3554
	}

	if gem.Config().ParsePower1().Type == item.AttachPower.Type {
		if gem.Config().ParsePower1().Value*int32(item.AttachDone) <= item.AttachPower.Value {
			return Response.E3555
		}
	}

	for pos, num := range costMap {
		bag.RemoveBagItemByPos(pos, num)
	}
	item.AttachPower.Value = gem.Config().ParsePower1().Value * int32(item.AttachDone)
	item.AttachPower.Type = gem.Config().ParsePower1().Type

	plr.UpdateIcon()
	gameStruct.CheckHPMP(plr)
	return Response.NoError
}
