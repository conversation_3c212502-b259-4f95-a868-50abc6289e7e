package middle

import (
	mqrpc "github.com/huyangv/vmqant/rpc"
	"world/base/enum/NodeState"
	"world/base/enum/key"
	"world/net"

	"github.com/huyangv/vmqant/conf"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	basemodule "github.com/huyangv/vmqant/module/base"
	"github.com/huyangv/vmqant/server"
)

var Module = func() module.Module {
	return new(Middle)
}

type Middle struct {
	basemodule.BaseModule
	middleware *net.Middleware
}

func (mid *Middle) GetType() string {
	return "mid" //很关键,需要与配置文件中的Module配置对应
}

func (mid *Middle) Version() string {
	return "1.0.0" //可以在监控时了解代码版本
}

// GetMinClientVersion 最低支持的客户端版本
func (mid *Middle) GetMinClientVersion() string {
	return "0.0.0"
}

// OnAppConfigurationLoaded 当应用配置加载完成时调用
func (mid *Middle) OnAppConfigurationLoaded(app module.App) {
	mid.BaseModule.OnAppConfigurationLoaded(app)
}

func (mid *Middle) OnInit(app module.App, settings *conf.ModuleSettings) {
	mid.BaseModule.OnInit(mid, app, settings, func(op *server.Options) {
		op.Metadata = map[string]string{
			key.MinClientVersion: mid.GetMinClientVersion(),
		}
	})
	mid.middleware = net.Create(mid)
}

func (mid *Middle) Run(closeSig chan bool) {
	//run
	<-closeSig
	log.Info("%v模块已停止 正在保存信息...", mid.GetType())
	mid.Offline()
	//stop
}

func (mid *Middle) OnDestroy() {
	mid.BaseModule.OnDestroy()
}

func (mid *Middle) GetModuleServer() server.Server {
	return mid.GetServer()
}
func (mid *Middle) GetRpcServer() mqrpc.RPCServer {
	return mid.GetServer().GetRpcServer()
}

func (mid *Middle) Offline() {
	mid.setMeta(key.NodeState, NodeState.Offline)
}

func (mid *Middle) setMeta(key string, value string) {
	mid.GetServer().Options().Metadata["state"] = NodeState.Offline
	mid.GetServer().ServiceRegister()
}
