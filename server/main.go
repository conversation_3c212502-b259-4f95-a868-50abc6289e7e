package main

import (
	"flag"
	_ "net/http/pprof"
	"time"
	"world/base/cfg"
	"world/base/enum/ServerType"
	"world/base/env"
	"world/base/mgr"
	"world/base/script"
	"world/base/structs"
	"world/common/net_helper"
	"world/db"
	"world/game"
	"world/game/gameMgr"
	mgate "world/gate"
	"world/http"
	"world/login"
	"world/middle"
	"world/test"

	"github.com/bamzi/jobrunner"
	mqant "github.com/huyangv/vmqant"
	"github.com/huyangv/vmqant/log"
	"github.com/huyangv/vmqant/module"
	"github.com/huyangv/vmqant/registry"
	"github.com/huyangv/vmqant/registry/consul"
	"github.com/nats-io/nats.go"
	"github.com/samber/lo"
)

var App module.App

func init() {
	flag.StringVar(&script.ServerTypeStr, "type", "dev", "当前进程需要启动的服务器类型,需要和配置文件的ProcessID匹配才能被启动（loin|game|gate|http|cross|dev）.")
	flag.IntVar(&script.Sid, "sid", 1, "如果type的类型是game,需要sid来启动对应的区服.")
}

func main() {
	script.CallBeforeApplicationLaunchedSync()

	//go func() {
	//	// 性能分析模块
	//	err := http3.ListenAndServe(fmt.Sprintf("127.0.0.1:%d", 6060), nil)
	//	if err != nil {
	//		log.Error("性能分析模块 错误 %v", err)
	//		return
	//	}
	//}()

	rs := consul.NewRegistry(func(options *registry.Options) {
		options.Addrs = []string{env.GetConsulUrl()}
	})
	nc, err := nats.Connect(env.GetNatsUrl(), nats.MaxReconnects(10000))
	if err != nil {
		log.Error("nats error %v", err)
		return
	}

	app := mqant.CreateApp(
		module.ProcessID(script.ServerTypeStr),
		module.Debug(env.IsDebug()),            //只有是在调试模式下才会在控制台打印日志, 非调试模式下只在日志文件中输出日志
		module.Nats(nc),                        //指定nats rpc
		module.Registry(rs),                    //指定服务发现
		module.RegisterTTL(10*time.Second),     //TTL指定在发现之后注册的信息存在多长时间 然后过期并被删除
		module.RegisterInterval(5*time.Second), //时间间隔是服务应该重新注册的时间，以保留其在服务发现中的注册信息
		module.RPCExpired(time.Duration(lo.If(env.IsDebug(), 60).Else(3))*time.Second), // RPC调用超时时间
	)
	App = app

	//重写返回客户端时的协议
	app.SetProtocolMarshal(func(Trace string, Result interface{}, Error string) (module.ProtocolMarshal, string) {
		// 假设返回客户端的必然是protobuf 此处直接断言处理
		data, _ := Result.([]byte)
		if Error != "" {
			log.Error("Trace:%s,返回客户端消息时出错:%s", Trace, Error)
			//data = pb.ProtoMarshalForce(&pb.S2C_ErrorResultMessage{
			//	Code: -500,
			//})
		}
		return app.NewProtocolMarshal(data), ""
	})

	// 配置解析完成
	_ = app.OnConfigurationLoaded(func(app module.App) {
		script.SetLogger()
		script.LaunchAllOrNotAtDev(app)
		// 非gate服启动逻辑
		if script.Mark&ServerType.MarkGame > 0 || script.Mark&ServerType.MarkLogin > 0 || script.Mark&ServerType.MarkHttp > 0 || script.Mark&ServerType.MarkMid > 0 {
			// 加载配置表
			cfg.ConfigLoad()

			url := app.GetSettings().Settings["MongodbURL"].(string)
			dbname := app.GetSettings().Settings["MongodbDB"].(string)
			// 初始化db
			db.InitMongoDB(url, dbname)
			// 初始化redis
			url = app.GetSettings().Settings["RedisUrl"].(string)
			password := app.GetSettings().Settings["RedisPassword"].(string)
			db.InitRedis(url, password)
		}
	})
	// 启动任务服务
	jobrunner.Start()
	// 应用启动完成
	app.OnStartup(func(app module.App) {
		//log.LogBeego().SetFormatFunc(logs.DefineErrorLogFunc(app.GetProcessID(), 4))
		script.AddRPCSerializeOrNot(app)
		net_helper.Init(app)
		InitGlobalMgr()
		// 启动区服
		if script.Mark&ServerType.MarkGame > 0 {
			// 初始化数据
			InitGameMgr()
			// 启动区服
			mgr.Area().Launch(script.Sid, true)

			if env.IsDebug() {
				test.Run()
			}
		}
	})
	// 按需启动模块
	runMods := make([]module.Module, 0)
	// game模块
	if script.Mark&ServerType.MarkGame > 0 {
		runMods = append(runMods, game.Module())
	}
	// login模块
	if script.Mark&ServerType.MarkLogin > 0 {
		runMods = append(runMods, login.Module())
	}
	// gate模块
	if script.Mark&ServerType.MarkGate > 0 {
		runMods = append(runMods, mgate.Module())
	}
	// http模块
	if script.Mark&ServerType.MarkHttp > 0 {
		runMods = append(runMods, http.Module())
	}
	// mid模块
	if script.Mark&ServerType.MarkMid > 0 {
		runMods = append(runMods, middle.Module())
	}

	err = app.Run(runMods...)
	if err != nil {
		log.Error(err.Error())
	}
}

// InitGlobalMgr 有些mgr创建时需要init才能有值，尤其是需要加载数据库数据的
func InitGlobalMgr() {

}
func InitGameMgr() {
	structs.InitDefaultPlayerId()
	structs.InitDefaultPetId()
	gameMgr.Battle()
	gameMgr.Task()
	gameMgr.Condition()
	gameMgr.Item()
}
