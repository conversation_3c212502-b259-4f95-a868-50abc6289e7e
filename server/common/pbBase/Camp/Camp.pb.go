// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbBase/Camp/Camp.proto

package Camp

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 阵营类型
type Type int32

const (
	West Type = 0 //东方
	East Type = 1 //西方
)

// Enum value maps for Type.
var (
	Type_name = map[int32]string{
		0: "West",
		1: "East",
	}
	Type_value = map[string]int32{
		"West": 0,
		"East": 1,
	}
)

func (x Type) Enum() *Type {
	p := new(Type)
	*p = x
	return p
}

func (x Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_Camp_Camp_proto_enumTypes[0].Descriptor()
}

func (Type) Type() protoreflect.EnumType {
	return &file_pbBase_Camp_Camp_proto_enumTypes[0]
}

func (x Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type.Descriptor instead.
func (Type) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_Camp_Camp_proto_rawDescGZIP(), []int{0}
}

var File_pbBase_Camp_Camp_proto protoreflect.FileDescriptor

var file_pbBase_Camp_Camp_proto_rawDesc = []byte{
	0x0a, 0x16, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x43, 0x61, 0x6d, 0x70, 0x2f, 0x43, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x61, 0x6d, 0x70, 0x2a, 0x1a, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04,
	0x57, 0x65, 0x73, 0x74, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x45, 0x61, 0x73, 0x74, 0x10, 0x01,
	0x42, 0x1f, 0x5a, 0x1d, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x43, 0x61, 0x6d, 0x70, 0x3b, 0x43, 0x61, 0x6d,
	0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbBase_Camp_Camp_proto_rawDescOnce sync.Once
	file_pbBase_Camp_Camp_proto_rawDescData = file_pbBase_Camp_Camp_proto_rawDesc
)

func file_pbBase_Camp_Camp_proto_rawDescGZIP() []byte {
	file_pbBase_Camp_Camp_proto_rawDescOnce.Do(func() {
		file_pbBase_Camp_Camp_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbBase_Camp_Camp_proto_rawDescData)
	})
	return file_pbBase_Camp_Camp_proto_rawDescData
}

var file_pbBase_Camp_Camp_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbBase_Camp_Camp_proto_goTypes = []interface{}{
	(Type)(0), // 0: proto.Camp.Type
}
var file_pbBase_Camp_Camp_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbBase_Camp_Camp_proto_init() }
func file_pbBase_Camp_Camp_proto_init() {
	if File_pbBase_Camp_Camp_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbBase_Camp_Camp_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbBase_Camp_Camp_proto_goTypes,
		DependencyIndexes: file_pbBase_Camp_Camp_proto_depIdxs,
		EnumInfos:         file_pbBase_Camp_Camp_proto_enumTypes,
	}.Build()
	File_pbBase_Camp_Camp_proto = out.File
	file_pbBase_Camp_Camp_proto_rawDesc = nil
	file_pbBase_Camp_Camp_proto_goTypes = nil
	file_pbBase_Camp_Camp_proto_depIdxs = nil
}
