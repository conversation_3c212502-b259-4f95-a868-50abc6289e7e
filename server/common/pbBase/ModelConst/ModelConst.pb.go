// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbBase/ModelConst/ModelConst.proto

package ModelConst

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 玩家常量
type Type int32

const (
	EXP                        Type = 0           //当前经验
	EXPMAX                     Type = 1           //最大经验值
	HP                         Type = 2           //血
	MP                         Type = 3           //蓝
	CP                         Type = 4           //属性点
	SP                         Type = 5           //技能点
	STR                        Type = 6           //力量
	CON                        Type = 7           //体质
	AGI                        Type = 8           //敏捷
	ILT                        Type = 9           //智力
	WIS                        Type = 10          //感知
	MONEY1                     Type = 11          //黄金
	MONEY2                     Type = 12          //金叶
	MONEY3                     Type = 13          //铜币
	NUM_BAG                    Type = 14          //背包格子数量
	NUM_STROE                  Type = 15          //仓库格子数量
	COUNTRY_HONOR              Type = 16          //国家贡献
	CITY_ID                    Type = 17          //个人城市id
	KILL_COUNT                 Type = 18          //杀怪数
	PK_WIN_COUNT               Type = 19          //pk胜场
	PK_LOSE_COUNT              Type = 20          //pk输场
	TOTAL_ONLINE               Type = 21          //在线时长
	PARTNER_ID                 Type = 22          //伴侣id
	MASTER_ID                  Type = 23          //师傅
	APPRENTICE1                Type = 24          //徒弟1
	APPRENTICE2                Type = 25          //徒弟2
	APPRENTICE3                Type = 26          //徒弟3
	APPRENTICE4                Type = 27          //徒弟4
	APPRENTICE5                Type = 28          //徒弟5
	HPMAX                      Type = 29          //最大血量
	MPMAX                      Type = 30          //最大蓝量
	SPEED                      Type = 31          //出手速度
	ATK_TYPE                   Type = 32          //攻击方式
	ATK_MIN                    Type = 33          //最小武伤
	ATK_MAX                    Type = 34          //最大武伤
	ATK_TIME                   Type = 35          //攻击次数
	ATK_STR                    Type = 36          //劈砍攻击力
	ATK_AGI                    Type = 37          //穿刺攻击力
	ATK_MAGIC                  Type = 38          //魔法攻击力
	DEF_STR                    Type = 39          //劈砍防御力
	DEF_AGI                    Type = 40          //穿刺防御力
	DEF_MAGIC                  Type = 41          //魔法防御力
	DODGE                      Type = 42          //闪避
	HIT_RATE                   Type = 43          //物理命中
	HIT_MAGIC                  Type = 44          //魔法命中
	CRITICAL                   Type = 45          //致命点（暴击）
	FORCE_HIT                  Type = 46          //强命
	EXP_UP                     Type = 47          //经验加成
	WIL                        Type = 48          //状态抵抗
	TOUGH                      Type = 49          //伤害减免
	BLOCK                      Type = 50          //格挡
	BRK_ARMOR                  Type = 51          //破甲
	LONG_TROUSERS_IN_TRANSPORT Type = 52          //
	INSIGHT                    Type = 53          //洞察
	DEF_FIELD                  Type = 54          //防御场
	BACK                       Type = 55          //物理反伤
	MAGIC_BACK                 Type = 56          //魔法反伤
	LIFE_ABSORPTION            Type = 57          //生命吸收
	MANA_ABSORPTION            Type = 58          //魔法吸收
	MAGIC_PENETRATION          Type = 59          //魔法穿透
	HEAL_RECOVERY              Type = 60          //治疗恢复
	MANA_RECOVERY              Type = 61          //魔法恢复
	RECOVERY                   Type = 62          //恢复
	ARGO                       Type = 63          //仇恨
	BACK_MAX                   Type = 64          //最大反伤
	MASTER_FLAG                Type = 65          //师傅标记
	HELP_COUNTRY               Type = 66          //帮助国家
	LOVE_PLAYER                Type = 67          //喜爱玩家
	INTEGRAL                   Type = 68          //积分
	PARTNER_NAME               Type = 69          //伴侣名字
	CRITICAL_DAMAGE            Type = 70          //暴击伤害
	LEFT_WEAPON_TYPE           Type = 71          //左手武器类型
	RIGHT_WEAPON_TYPE          Type = 72          //右手武器类型
	LEFT_ATK_MIN               Type = 73          //左手最小攻击
	LEFT_ATK_MAX               Type = 74          //左手最大攻击
	RIGHT_ATK_MIN              Type = 75          //右手最小攻击
	RIGHT_ATK_MAX              Type = 76          //右手最大攻击
	LEFT_ATK_TIME              Type = 77          //左手攻击次数
	RIGHT_ATK_TIME             Type = 78          //右手攻击次数
	PET_COLOR                  Type = 81          //宠物颜色
	PET_GRADE                  Type = 82          //宠物品质
	PET_GROW_LEVEL             Type = 83          //宠物成长等级
	BUFFER_REMOVE_STATUS       Type = 90          //移除状态
	HP_DISPLAY                 Type = 91          //血量显示
	MP_DISPLAY                 Type = 92          //魔法显示
	UID                        Type = 101         //唯一ID
	ID                         Type = 102         //ID
	SEX                        Type = 103         //性别
	RACE                       Type = 104         //种族
	JOB                        Type = 105         //职业
	LEVEL                      Type = 106         //等级
	ICON                       Type = 107         //图标
	SETTING                    Type = 108         //设置
	STATUS                     Type = 109         //状态
	COUNTRY_ID                 Type = 110         //国家ID
	COUNTRY_RANK               Type = 111         //国家等级
	COUNTRY_NAME               Type = 112         //国家名称
	PX                         Type = 113         //位置X
	PY                         Type = 114         //位置Y
	COUNTRY_RANK_SET           Type = 115         //国家等级设置
	COUNTRY_ID_SET             Type = 116         //国家ID设置
	STATUS_SET                 Type = 117         //状态设置
	LEVEL2                     Type = 118         //二级等级
	EXP2                       Type = 119         //二级经验
	EXPMAX2                    Type = 120         //二级最大经验
	ENCHANTVALUE               Type = 121         //附魔值
	COMBATPOINT                Type = 122         //战斗力
	VIP_LEVEL_2                Type = 123         //VIP等级2
	NPC_MSS_NONE               Type = 0           //NPC无任务状态
	NPC_MSS_SUBMIT             Type = 1           //NPC可提交任务
	NPC_MSS_NOT_SUBMIT         Type = 2           //NPC不可提交任务
	NPC_MSS_ACCEPT             Type = 3           //NPC可接任务
	NPC_MSS_NOT_ACCEPT         Type = 4           //NPC不可接任务
	MAX_MERCENARY_MEMBER       Type = 2           //最大佣兵成员数
	SET_EXP                    Type = 5001        //设置经验
	SET_EXPMAX                 Type = 5002        //设置最大经验
	SET_EXP2                   Type = 5003        //设置二级经验
	SET_EXPMAX2                Type = 5004        //设置二级最大经验
	MAX_MOSTER_HP              Type = 5000000     //怪物最大血量
	MAX_BASE_ATTRIBUTE         Type = 2147483647  //基础属性最大值
	MAX_OTHER_ATTRIBUTE        Type = 1000000     //其他属性最大值
	MAX_DEF                    Type = 2147483647  //最大防御
	MAX_PROBABILITY            Type = 100         //最大概率
	IGNORE_BACK                Type = 200         //忽视物理反伤
	IGNORE_MAGIC_BACK          Type = 201         //忽视魔法反伤
	IGNORE_BLOCK               Type = 202         //忽视格挡
	IGNORE_INSIGHT             Type = 203         //忽视洞察
	IGNORE_WIL                 Type = 204         //忽视意志
	IGNORE_TOUCH               Type = 205         //无视伤害减免
	IGNORE_CRITICAL            Type = 206         //忽视暴击
	DEF_STR_NEARBY             Type = 220         //近战劈砍防御
	DEF_STR_RANGE              Type = 221         //远程劈砍防御力
	DEF_AGI_RANGE              Type = 222         //远程穿刺防御
	DEF_AGI_NEARBY             Type = 223         //近战穿刺防御
	ATK_STR_RANGE              Type = 230         //远程劈砍攻击
	ATK_STR_NEARBY             Type = 231         //近战劈砍攻击
	ATK_AGI_RANGE              Type = 232         //远程穿刺攻击
	ATK_AGI_NEARBY             Type = 233         //近战穿刺攻击
	MIN_HIT_RATE               Type = 30          //最小命中率
	MIN_HIT_MAGIC              Type = 20          //最小魔法命中
	MIN_FORCE_HITRATE          Type = 30          //最小强制命中率
	MAX_FORCE_RATE             Type = 70          //最大强制率
	MIN_HIT_TIME               Type = 1           //最小命中次数
	MAX_HIT_TIME               Type = 99          //最大命中次数
	MAX_ATK                    Type = 99999999    //最大攻击
	MAX_KEEPOUT_ATK_TIME       Type = 100         //最大免伤次数
	MAX_OTHER_PROBABILITY      Type = 1000        //其他最大概率
	MIN_DEF_FIELD              Type = -999999     //最小防御场
	MIN_HEAL_RECOVERY          Type = -999999     //最小治疗恢复
	MIN_MANA_RECOVERY          Type = -999999     //最小魔法恢复
	KEEPOUT_ATK_TIME           Type = 250         //免伤护盾
	NEW_REFLECTION             Type = 251         //新反击
	START_HAIR                 Type = 0           //发型起始ID
	START_FACE                 Type = 1000        //脸型起始ID
	START_HAND                 Type = 2000        //手部起始ID
	START_FEET                 Type = 3000        //脚部起始ID
	START_SHOULDER             Type = 4000        //肩部起始ID
	START_BACK                 Type = 5000        //背部起始ID
	START_CLOTHES              Type = 6000        //衣服起始ID
	START_TROUSERS             Type = 7000        //裤子起始ID
	START_WEAPON               Type = 8000        //武器起始ID
	START_HELMET               Type = 10000       //头盔起始ID
	START_PET                  Type = 12000       //宠物起始ID
	START_TRANSPORT            Type = 14000       //坐骑起始ID
	START_FLASH                Type = 15000       //闪光起始ID
	START_MINI_SPRITE          Type = 16000       //迷你精灵起始ID
	START_WEAPON_ADD1          Type = 30000       //武器附加1起始ID
	START_WEAPON_ADD2          Type = 32000       //武器附加2起始ID
	BIT_1                      Type = 1           //1位掩码
	BIT_2                      Type = 3           //2位掩码
	BIT_3                      Type = 7           //3位掩码
	BIT_4                      Type = 15          //4位掩码
	BIT_5                      Type = 31          //5位掩码
	BIT_6                      Type = 63          //6位掩码
	BIT_7                      Type = 127         //7位掩码
	BIT_8                      Type = 255         //8位掩码
	OFFSET_SEX                 Type = 0           //性别偏移
	OFFSET_RACE                Type = 1           //种族偏移
	OFFSET_JOB                 Type = 3           //职业偏移
	OFFSET_HAIR_STYLE          Type = 7           //发型样式偏移
	OFFSET_HAIR_COLOR          Type = 11          //发色偏移
	OFFSET_FACE_STYLE          Type = 13          //脸型样式偏移
	OFFSET_HAND_STYLE          Type = 17          //手部样式偏移
	OFFSET_HAND_COLOR          Type = 19          //手部颜色偏移
	OFFSET_FEET_STYLE          Type = 21          //脚部样式偏移
	OFFSET_FEET_COLOR          Type = 23          //脚部颜色偏移
	OFFSET_HELMET_STYLE        Type = 25          //头盔样式偏移
	OFFSET_HELMET_COLOR        Type = 30          //头盔颜色偏移
	OFFSET_HAIR_ADD            Type = 0           //发型附加偏移
	OFFSET_FACE_ADD            Type = 3           //脸型附加偏移
	OFFSET_HAND_ADD            Type = 4           //手部附加偏移
	OFFSET_FEET_ADD            Type = 8           //脚部附加偏移
	OFFSET_HELMET_ADD          Type = 12          //头盔附加偏移
	OFFSET_SHOULDER_STYLE      Type = 0           //肩部样式偏移
	OFFSET_SHOULDER_COLOR      Type = 4           //肩部颜色偏移
	OFFSET_LWEAPON_STYLE       Type = 6           //左手武器样式偏移
	OFFSET_LWEAPON_COLOR       Type = 14          //左手武器颜色偏移
	OFFSET_RWEAPON_STYLE       Type = 16          //右手武器样式偏移
	OFFSET_RWEAPON_COLOR       Type = 24          //右手武器颜色偏移
	OFFSET_VIP                 Type = 26          //VIP偏移
	OFFSET_WEAPON_FLASH        Type = 28          //武器闪光偏移
	OFFSET_SHOULDER_ADD        Type = 31          //肩部附加偏移
	OFFSET_LWEAPON_ADD         Type = 0           //左手武器附加偏移
	OFFSET_RWEAPON_ADD         Type = 2           //右手武器附加偏移
	OFFSET_SHOULDER_ADD_2      Type = 4           //肩部附加2偏移
	OFFSET_BACK_STYLE          Type = 0           //背部样式偏移
	OFFSET_BACK_COLOR          Type = 4           //背部颜色偏移
	OFFSET_CLOTHES_STYLE       Type = 6           //衣服样式偏移
	OFFSET_CLOTHES_COLOR       Type = 12          //衣服颜色偏移
	OFFSET_TROUSERS_STYLE      Type = 14          //裤子样式偏移
	OFFSET_TROUSERS_COLOR      Type = 20          //裤子颜色偏移
	OFFSET_TRANSPORT_STYLE     Type = 22          //坐骑样式偏移
	OFFSET_TRANSPORT_COLOR     Type = 26          //坐骑颜色偏移
	OFFSET_BACK_ADD            Type = 28          //背部附加偏移
	OFFSET_TRANSPORT_ADD       Type = 29          //坐骑附加偏移
	OFFSET_FASH_ADD            Type = 30          //时装附加偏移
	OFFSET_TRANSPORT_ADD_2     Type = 31          //坐骑附加2偏移
	OFFSET_BACK_ADD_2          Type = 0           //背部附加2偏移
	OFFSET_CLOTHES_ADD         Type = 2           //衣服附加偏移
	OFFSET_TROUSERS_ADD        Type = 3           //裤子附加偏移
	OFFSET_TRANSPORT_ADD_3     Type = 4           //坐骑附加3偏移
	LEN_SEX                    Type = 1           //性别长度
	LEN_RACE                   Type = 3           //种族长度
	LEN_JOB                    Type = 15          //职业长度
	LEN_HAIR_STYLE             Type = 15          //发型样式长度
	LEN_HAIR_COLOR             Type = 3           //发色长度
	LEN_FACE_STYLE             Type = 15          //脸型样式长度
	LEN_HAND_STYLE             Type = 3           //手部样式长度
	LEN_HAND_COLOR             Type = 3           //手部颜色长度
	LEN_FEET_STYLE             Type = 3           //脚部样式长度
	LEN_FEET_COLOR             Type = 3           //脚部颜色长度
	LEN_HELMET_STYLE           Type = 31          //头盔样式长度
	LEN_HELMET_COLOR           Type = 3           //头盔颜色长度
	LEN_HAIR_ADD               Type = 7           //发型附加长度
	LEN_FACE_ADD               Type = 1           //脸型附加长度
	LEN_HAND_ADD               Type = 15          //手部附加长度
	LEN_FEET_ADD               Type = 15          //脚部附加长度
	LEN_HELMET_ADD             Type = 3           //头盔附加长度
	LEN_SHOULDER_STYLE         Type = 15          //肩部样式长度
	LEN_SHOULDER_COLOR         Type = 3           //肩部颜色长度
	LEN_LWEAPON_STYLE          Type = 255         //左手武器样式长度
	LEN_LWEAPON_COLOR          Type = 3           //左手武器颜色长度
	LEN_RWEAPON_STYLE          Type = 255         //右手武器样式长度
	LEN_RWEAPON_COLOR          Type = 3           //右手武器颜色长度
	LEN_VIP                    Type = 3           //VIP长度
	LEN_WEAPON_FLASH           Type = 7           //武器闪光长度
	LEN_SHOULDER_ADD           Type = 1           //肩部附加长度
	LEN_LWEAPON_ADD            Type = 3           //左手武器附加长度
	LEN_RWEAPON_ADD            Type = 3           //右手武器附加长度
	LEN_SHOULDER_ADD_2         Type = 3           //肩部附加2长度
	LEN_BACK_STYLE             Type = 15          //背部样式长度
	LEN_BACK_COLOR             Type = 3           //背部颜色长度
	LEN_CLOTHES_STYLE          Type = 63          //衣服样式长度
	LEN_CLOTHES_COLOR          Type = 3           //衣服颜色长度
	LEN_TROUSERS_STYLE         Type = 63          //裤子样式长度
	LEN_TROUSERS_COLOR         Type = 3           //裤子颜色长度
	LEN_TRANSPORT_STYLE        Type = 15          //坐骑样式长度
	LEN_TRANSPORT_COLOR        Type = 3           //坐骑颜色长度
	LEN_BACK_ADD               Type = 1           //背部附加长度
	LEN_TRANSPORT_ADD          Type = 1           //坐骑附加长度
	LEN_FASH_ADD               Type = 1           //时装附加长度
	LEN_TRANSPORT_ADD_2        Type = 1           //坐骑附加2长度
	LEN_BACK_ADD_2             Type = 3           //背部附加2长度
	LEN_CLOTHES_ADD            Type = 1           //衣服附加长度
	LEN_TROUSERS_ADD           Type = 1           //裤子附加长度
	LEN_TRANSPORT_ADD_3        Type = 1           //坐骑附加3长度
	OFFSET_PET_STYLE           Type = 0           //宠物样式偏移
	OFFSET_PET_COLOR           Type = 6           //宠物颜色偏移
	OFFSET_LVUP_STYLE          Type = 8           //升级样式偏移
	LEN_PET_STYLE              Type = 63          //宠物样式长度
	LEN_PET_COLOR              Type = 3           //宠物颜色长度
	LEN_LVUP_STYLE             Type = 15          //升级样式长度
	MOVE_END_CHECK             Type = 1           //移动结束检查
	LAST_MOVE_FILL             Type = 2           //最后移动填充
	NPC_MISSION_LOAD           Type = 4           //NPC任务加载
	MOVE_NPC_WELCOME           Type = 8           //NPC欢迎
	MERCENARY_INFO             Type = 16          //佣兵信息
	PET_INFO                   Type = 32          //宠物信息
	DEL_STATUS                 Type = 64          //删除状态
	MONSTER_BOOK_INFO          Type = 128         //怪物图鉴信息
	PET_INNER_SHOW             Type = 256         //宠物内部显示
	OFFLINE_DOING              Type = 512         //离线操作
	NPC_MISSION_DISPLAY        Type = 1024        //NPC任务显示
	NPC_MISSION_HIDE           Type = 2048        //NPC任务隐藏
	NPC_COUNTRY_BLOAD          Type = 4096        //NPC国家血统
	MAIL_NEW_NOTICE            Type = 8192        //新邮件通知
	ATTR_NEW_NOTICE            Type = 16384       //新属性通知
	CHAT_NEW_NOTICE            Type = 32768       //新聊天通知
	SPRITE_LOADING_NPC         Type = 65536       //精灵NPC加载
	SELL_PLAYER_INFO_LOAD      Type = 131072      //出售玩家信息加载
	SELL_PLAYER_STORE_LOAD     Type = 262144      //出售玩家商店加载
	HIDE_SPIRTE                Type = 524288      //隐藏精灵
	HIDE_PHOTO_COMMAND         Type = 1048576     //隐藏照片命令
	SELL_PLAYER_STORE_VIP_LOAD Type = 2097152     //VIP玩家商店加载
	BUFFER_DIE_1HP_CHECK       Type = 134217728   //缓冲区死亡1HP检查
	BUFFER_DIE_FULLHP_CHECK    Type = 268435456   //缓冲区死亡满血检查
	BUFFER_DIE_DELAY_CHECK     Type = 536870912   //缓冲区死亡延迟检查
	TAG_IS_KEEP_OUT            Type = 1073741824  //是否保持外部标记
	COUNTRY_APPLY              Type = -2147483648 //国家申请
	STATUS_NONE                Type = 0           //无状态
	STATUS_ONLINE              Type = 1           //在线状态
	STATUS_ALL_DEL             Type = 2           //全部删除状态
	STATUS_TEMP_DEL            Type = 4           //临时删除状态
	STATUS_FROST               Type = 8           //冻结状态
	STATUS_SELL                Type = 16          //出售状态
	STATUS_ENGAGE              Type = 32          //订婚状态
	STATUS_CLOSE               Type = 64          //关闭状态
	STATUS_MOUTH               Type = 128         //嘴部状态
	STATUS_COMMENTS            Type = 256         //评论状态
	STATUS_OFFLINEMISSION      Type = 512         //离线任务状态
	STATUS_VIP                 Type = 1024        //VIP状态
	STATUS_IS_CARD             Type = 2048        //是否为卡片
	STATUS_CAN_MASTER          Type = 4096        //可以成为大师
	STATUS_NEW                 Type = 8192        //新角色
	PHOTO_FLAG_VIP             Type = 1           //VIP照片标记
	PHOTO_IS_VIP_CQ            Type = 2           //是否为VIP CQ照片
	COUNTRY_FFLAG_IS_VIP       Type = 1           //国家VIP标记
	COUNTRY_FFLAG_IS_VIP_CQ    Type = 2           //国家VIP CQ标记
	COUNTRY_MEMBER_IS_VIP      Type = 1           //国家成员是否VIP
	COUNTRY_MEMBER_IS_VIP_CQ   Type = 2           //国家成员是否VIP CQ
	STATUS_IS_SOLDIER          Type = 8192        //是否为士兵
	STATUS_IS_HELP             Type = 16384       //是否为帮助
	STATUS_IS_PHOTO            Type = 32768       //是否为照片
	STATUS_IS_GM               Type = 65536       //是否为GM
	STATUS_IS_CITY             Type = 131072      //是否为城市
	STATUS_IS_TEACHER          Type = 262144      //是否为老师
	STATUS_VIP_TX_CQ           Type = 524288      //VIP TX CQ状态
	STATUS_IS_TOURIST          Type = 1048576     //是否为游客
	STATUS_IS_ROBOT            Type = 2097152     //是否为机器人
	MODE_NORMAL                Type = 0           //普通模式
	MODE_SHOP                  Type = 1           //商店模式
	MODE_BATTLE_LOCAL          Type = 50          //本地战斗模式
	MODE_BATTLE_REMOTE         Type = 51          //远程战斗模式
	MODE_BATTLE_PK             Type = 52          //PK战斗模式
	MODE_BATTLE_OB             Type = 53          //观战模式
	MODE_BATTLE_PET_PK         Type = 54          //宠物PK模式
	NPC                        Type = 1           //NPC
	BATTLE_PLAYER              Type = 2           //战斗玩家
	MAX_STORE_NUM              Type = 60          //最大仓库数量
	MAX_PLAYER_HP              Type = 9999999     //玩家最大血量
	MAX_PLAYER_MP              Type = 9999999     //玩家最大魔法值
)

// Enum value maps for Type.
var (
	Type_name = map[int32]string{
		0:   "EXP",
		1:   "EXPMAX",
		2:   "HP",
		3:   "MP",
		4:   "CP",
		5:   "SP",
		6:   "STR",
		7:   "CON",
		8:   "AGI",
		9:   "ILT",
		10:  "WIS",
		11:  "MONEY1",
		12:  "MONEY2",
		13:  "MONEY3",
		14:  "NUM_BAG",
		15:  "NUM_STROE",
		16:  "COUNTRY_HONOR",
		17:  "CITY_ID",
		18:  "KILL_COUNT",
		19:  "PK_WIN_COUNT",
		20:  "PK_LOSE_COUNT",
		21:  "TOTAL_ONLINE",
		22:  "PARTNER_ID",
		23:  "MASTER_ID",
		24:  "APPRENTICE1",
		25:  "APPRENTICE2",
		26:  "APPRENTICE3",
		27:  "APPRENTICE4",
		28:  "APPRENTICE5",
		29:  "HPMAX",
		30:  "MPMAX",
		31:  "SPEED",
		32:  "ATK_TYPE",
		33:  "ATK_MIN",
		34:  "ATK_MAX",
		35:  "ATK_TIME",
		36:  "ATK_STR",
		37:  "ATK_AGI",
		38:  "ATK_MAGIC",
		39:  "DEF_STR",
		40:  "DEF_AGI",
		41:  "DEF_MAGIC",
		42:  "DODGE",
		43:  "HIT_RATE",
		44:  "HIT_MAGIC",
		45:  "CRITICAL",
		46:  "FORCE_HIT",
		47:  "EXP_UP",
		48:  "WIL",
		49:  "TOUGH",
		50:  "BLOCK",
		51:  "BRK_ARMOR",
		52:  "LONG_TROUSERS_IN_TRANSPORT",
		53:  "INSIGHT",
		54:  "DEF_FIELD",
		55:  "BACK",
		56:  "MAGIC_BACK",
		57:  "LIFE_ABSORPTION",
		58:  "MANA_ABSORPTION",
		59:  "MAGIC_PENETRATION",
		60:  "HEAL_RECOVERY",
		61:  "MANA_RECOVERY",
		62:  "RECOVERY",
		63:  "ARGO",
		64:  "BACK_MAX",
		65:  "MASTER_FLAG",
		66:  "HELP_COUNTRY",
		67:  "LOVE_PLAYER",
		68:  "INTEGRAL",
		69:  "PARTNER_NAME",
		70:  "CRITICAL_DAMAGE",
		71:  "LEFT_WEAPON_TYPE",
		72:  "RIGHT_WEAPON_TYPE",
		73:  "LEFT_ATK_MIN",
		74:  "LEFT_ATK_MAX",
		75:  "RIGHT_ATK_MIN",
		76:  "RIGHT_ATK_MAX",
		77:  "LEFT_ATK_TIME",
		78:  "RIGHT_ATK_TIME",
		81:  "PET_COLOR",
		82:  "PET_GRADE",
		83:  "PET_GROW_LEVEL",
		90:  "BUFFER_REMOVE_STATUS",
		91:  "HP_DISPLAY",
		92:  "MP_DISPLAY",
		101: "UID",
		102: "ID",
		103: "SEX",
		104: "RACE",
		105: "JOB",
		106: "LEVEL",
		107: "ICON",
		108: "SETTING",
		109: "STATUS",
		110: "COUNTRY_ID",
		111: "COUNTRY_RANK",
		112: "COUNTRY_NAME",
		113: "PX",
		114: "PY",
		115: "COUNTRY_RANK_SET",
		116: "COUNTRY_ID_SET",
		117: "STATUS_SET",
		118: "LEVEL2",
		119: "EXP2",
		120: "EXPMAX2",
		121: "ENCHANTVALUE",
		122: "COMBATPOINT",
		123: "VIP_LEVEL_2",
		// Duplicate value: 0: "NPC_MSS_NONE",
		// Duplicate value: 1: "NPC_MSS_SUBMIT",
		// Duplicate value: 2: "NPC_MSS_NOT_SUBMIT",
		// Duplicate value: 3: "NPC_MSS_ACCEPT",
		// Duplicate value: 4: "NPC_MSS_NOT_ACCEPT",
		// Duplicate value: 2: "MAX_MERCENARY_MEMBER",
		5001:       "SET_EXP",
		5002:       "SET_EXPMAX",
		5003:       "SET_EXP2",
		5004:       "SET_EXPMAX2",
		5000000:    "MAX_MOSTER_HP",
		2147483647: "MAX_BASE_ATTRIBUTE",
		1000000:    "MAX_OTHER_ATTRIBUTE",
		// Duplicate value: 2147483647: "MAX_DEF",
		100: "MAX_PROBABILITY",
		200: "IGNORE_BACK",
		201: "IGNORE_MAGIC_BACK",
		202: "IGNORE_BLOCK",
		203: "IGNORE_INSIGHT",
		204: "IGNORE_WIL",
		205: "IGNORE_TOUCH",
		206: "IGNORE_CRITICAL",
		220: "DEF_STR_NEARBY",
		221: "DEF_STR_RANGE",
		222: "DEF_AGI_RANGE",
		223: "DEF_AGI_NEARBY",
		230: "ATK_STR_RANGE",
		231: "ATK_STR_NEARBY",
		232: "ATK_AGI_RANGE",
		233: "ATK_AGI_NEARBY",
		// Duplicate value: 30: "MIN_HIT_RATE",
		// Duplicate value: 20: "MIN_HIT_MAGIC",
		// Duplicate value: 30: "MIN_FORCE_HITRATE",
		// Duplicate value: 70: "MAX_FORCE_RATE",
		// Duplicate value: 1: "MIN_HIT_TIME",
		99:       "MAX_HIT_TIME",
		99999999: "MAX_ATK",
		// Duplicate value: 100: "MAX_KEEPOUT_ATK_TIME",
		1000:    "MAX_OTHER_PROBABILITY",
		-999999: "MIN_DEF_FIELD",
		// Duplicate value: -999999: "MIN_HEAL_RECOVERY",
		// Duplicate value: -999999: "MIN_MANA_RECOVERY",
		250: "KEEPOUT_ATK_TIME",
		251: "NEW_REFLECTION",
		// Duplicate value: 0: "START_HAIR",
		// Duplicate value: 1000: "START_FACE",
		2000:  "START_HAND",
		3000:  "START_FEET",
		4000:  "START_SHOULDER",
		5000:  "START_BACK",
		6000:  "START_CLOTHES",
		7000:  "START_TROUSERS",
		8000:  "START_WEAPON",
		10000: "START_HELMET",
		12000: "START_PET",
		14000: "START_TRANSPORT",
		15000: "START_FLASH",
		16000: "START_MINI_SPRITE",
		30000: "START_WEAPON_ADD1",
		32000: "START_WEAPON_ADD2",
		// Duplicate value: 1: "BIT_1",
		// Duplicate value: 3: "BIT_2",
		// Duplicate value: 7: "BIT_3",
		// Duplicate value: 15: "BIT_4",
		// Duplicate value: 31: "BIT_5",
		// Duplicate value: 63: "BIT_6",
		127: "BIT_7",
		255: "BIT_8",
		// Duplicate value: 0: "OFFSET_SEX",
		// Duplicate value: 1: "OFFSET_RACE",
		// Duplicate value: 3: "OFFSET_JOB",
		// Duplicate value: 7: "OFFSET_HAIR_STYLE",
		// Duplicate value: 11: "OFFSET_HAIR_COLOR",
		// Duplicate value: 13: "OFFSET_FACE_STYLE",
		// Duplicate value: 17: "OFFSET_HAND_STYLE",
		// Duplicate value: 19: "OFFSET_HAND_COLOR",
		// Duplicate value: 21: "OFFSET_FEET_STYLE",
		// Duplicate value: 23: "OFFSET_FEET_COLOR",
		// Duplicate value: 25: "OFFSET_HELMET_STYLE",
		// Duplicate value: 30: "OFFSET_HELMET_COLOR",
		// Duplicate value: 0: "OFFSET_HAIR_ADD",
		// Duplicate value: 3: "OFFSET_FACE_ADD",
		// Duplicate value: 4: "OFFSET_HAND_ADD",
		// Duplicate value: 8: "OFFSET_FEET_ADD",
		// Duplicate value: 12: "OFFSET_HELMET_ADD",
		// Duplicate value: 0: "OFFSET_SHOULDER_STYLE",
		// Duplicate value: 4: "OFFSET_SHOULDER_COLOR",
		// Duplicate value: 6: "OFFSET_LWEAPON_STYLE",
		// Duplicate value: 14: "OFFSET_LWEAPON_COLOR",
		// Duplicate value: 16: "OFFSET_RWEAPON_STYLE",
		// Duplicate value: 24: "OFFSET_RWEAPON_COLOR",
		// Duplicate value: 26: "OFFSET_VIP",
		// Duplicate value: 28: "OFFSET_WEAPON_FLASH",
		// Duplicate value: 31: "OFFSET_SHOULDER_ADD",
		// Duplicate value: 0: "OFFSET_LWEAPON_ADD",
		// Duplicate value: 2: "OFFSET_RWEAPON_ADD",
		// Duplicate value: 4: "OFFSET_SHOULDER_ADD_2",
		// Duplicate value: 0: "OFFSET_BACK_STYLE",
		// Duplicate value: 4: "OFFSET_BACK_COLOR",
		// Duplicate value: 6: "OFFSET_CLOTHES_STYLE",
		// Duplicate value: 12: "OFFSET_CLOTHES_COLOR",
		// Duplicate value: 14: "OFFSET_TROUSERS_STYLE",
		// Duplicate value: 20: "OFFSET_TROUSERS_COLOR",
		// Duplicate value: 22: "OFFSET_TRANSPORT_STYLE",
		// Duplicate value: 26: "OFFSET_TRANSPORT_COLOR",
		// Duplicate value: 28: "OFFSET_BACK_ADD",
		// Duplicate value: 29: "OFFSET_TRANSPORT_ADD",
		// Duplicate value: 30: "OFFSET_FASH_ADD",
		// Duplicate value: 31: "OFFSET_TRANSPORT_ADD_2",
		// Duplicate value: 0: "OFFSET_BACK_ADD_2",
		// Duplicate value: 2: "OFFSET_CLOTHES_ADD",
		// Duplicate value: 3: "OFFSET_TROUSERS_ADD",
		// Duplicate value: 4: "OFFSET_TRANSPORT_ADD_3",
		// Duplicate value: 1: "LEN_SEX",
		// Duplicate value: 3: "LEN_RACE",
		// Duplicate value: 15: "LEN_JOB",
		// Duplicate value: 15: "LEN_HAIR_STYLE",
		// Duplicate value: 3: "LEN_HAIR_COLOR",
		// Duplicate value: 15: "LEN_FACE_STYLE",
		// Duplicate value: 3: "LEN_HAND_STYLE",
		// Duplicate value: 3: "LEN_HAND_COLOR",
		// Duplicate value: 3: "LEN_FEET_STYLE",
		// Duplicate value: 3: "LEN_FEET_COLOR",
		// Duplicate value: 31: "LEN_HELMET_STYLE",
		// Duplicate value: 3: "LEN_HELMET_COLOR",
		// Duplicate value: 7: "LEN_HAIR_ADD",
		// Duplicate value: 1: "LEN_FACE_ADD",
		// Duplicate value: 15: "LEN_HAND_ADD",
		// Duplicate value: 15: "LEN_FEET_ADD",
		// Duplicate value: 3: "LEN_HELMET_ADD",
		// Duplicate value: 15: "LEN_SHOULDER_STYLE",
		// Duplicate value: 3: "LEN_SHOULDER_COLOR",
		// Duplicate value: 255: "LEN_LWEAPON_STYLE",
		// Duplicate value: 3: "LEN_LWEAPON_COLOR",
		// Duplicate value: 255: "LEN_RWEAPON_STYLE",
		// Duplicate value: 3: "LEN_RWEAPON_COLOR",
		// Duplicate value: 3: "LEN_VIP",
		// Duplicate value: 7: "LEN_WEAPON_FLASH",
		// Duplicate value: 1: "LEN_SHOULDER_ADD",
		// Duplicate value: 3: "LEN_LWEAPON_ADD",
		// Duplicate value: 3: "LEN_RWEAPON_ADD",
		// Duplicate value: 3: "LEN_SHOULDER_ADD_2",
		// Duplicate value: 15: "LEN_BACK_STYLE",
		// Duplicate value: 3: "LEN_BACK_COLOR",
		// Duplicate value: 63: "LEN_CLOTHES_STYLE",
		// Duplicate value: 3: "LEN_CLOTHES_COLOR",
		// Duplicate value: 63: "LEN_TROUSERS_STYLE",
		// Duplicate value: 3: "LEN_TROUSERS_COLOR",
		// Duplicate value: 15: "LEN_TRANSPORT_STYLE",
		// Duplicate value: 3: "LEN_TRANSPORT_COLOR",
		// Duplicate value: 1: "LEN_BACK_ADD",
		// Duplicate value: 1: "LEN_TRANSPORT_ADD",
		// Duplicate value: 1: "LEN_FASH_ADD",
		// Duplicate value: 1: "LEN_TRANSPORT_ADD_2",
		// Duplicate value: 3: "LEN_BACK_ADD_2",
		// Duplicate value: 1: "LEN_CLOTHES_ADD",
		// Duplicate value: 1: "LEN_TROUSERS_ADD",
		// Duplicate value: 1: "LEN_TRANSPORT_ADD_3",
		// Duplicate value: 0: "OFFSET_PET_STYLE",
		// Duplicate value: 6: "OFFSET_PET_COLOR",
		// Duplicate value: 8: "OFFSET_LVUP_STYLE",
		// Duplicate value: 63: "LEN_PET_STYLE",
		// Duplicate value: 3: "LEN_PET_COLOR",
		// Duplicate value: 15: "LEN_LVUP_STYLE",
		// Duplicate value: 1: "MOVE_END_CHECK",
		// Duplicate value: 2: "LAST_MOVE_FILL",
		// Duplicate value: 4: "NPC_MISSION_LOAD",
		// Duplicate value: 8: "MOVE_NPC_WELCOME",
		// Duplicate value: 16: "MERCENARY_INFO",
		// Duplicate value: 32: "PET_INFO",
		// Duplicate value: 64: "DEL_STATUS",
		128:         "MONSTER_BOOK_INFO",
		256:         "PET_INNER_SHOW",
		512:         "OFFLINE_DOING",
		1024:        "NPC_MISSION_DISPLAY",
		2048:        "NPC_MISSION_HIDE",
		4096:        "NPC_COUNTRY_BLOAD",
		8192:        "MAIL_NEW_NOTICE",
		16384:       "ATTR_NEW_NOTICE",
		32768:       "CHAT_NEW_NOTICE",
		65536:       "SPRITE_LOADING_NPC",
		131072:      "SELL_PLAYER_INFO_LOAD",
		262144:      "SELL_PLAYER_STORE_LOAD",
		524288:      "HIDE_SPIRTE",
		1048576:     "HIDE_PHOTO_COMMAND",
		2097152:     "SELL_PLAYER_STORE_VIP_LOAD",
		134217728:   "BUFFER_DIE_1HP_CHECK",
		268435456:   "BUFFER_DIE_FULLHP_CHECK",
		536870912:   "BUFFER_DIE_DELAY_CHECK",
		1073741824:  "TAG_IS_KEEP_OUT",
		-2147483648: "COUNTRY_APPLY",
		// Duplicate value: 0: "STATUS_NONE",
		// Duplicate value: 1: "STATUS_ONLINE",
		// Duplicate value: 2: "STATUS_ALL_DEL",
		// Duplicate value: 4: "STATUS_TEMP_DEL",
		// Duplicate value: 8: "STATUS_FROST",
		// Duplicate value: 16: "STATUS_SELL",
		// Duplicate value: 32: "STATUS_ENGAGE",
		// Duplicate value: 64: "STATUS_CLOSE",
		// Duplicate value: 128: "STATUS_MOUTH",
		// Duplicate value: 256: "STATUS_COMMENTS",
		// Duplicate value: 512: "STATUS_OFFLINEMISSION",
		// Duplicate value: 1024: "STATUS_VIP",
		// Duplicate value: 2048: "STATUS_IS_CARD",
		// Duplicate value: 4096: "STATUS_CAN_MASTER",
		// Duplicate value: 8192: "STATUS_NEW",
		// Duplicate value: 1: "PHOTO_FLAG_VIP",
		// Duplicate value: 2: "PHOTO_IS_VIP_CQ",
		// Duplicate value: 1: "COUNTRY_FFLAG_IS_VIP",
		// Duplicate value: 2: "COUNTRY_FFLAG_IS_VIP_CQ",
		// Duplicate value: 1: "COUNTRY_MEMBER_IS_VIP",
		// Duplicate value: 2: "COUNTRY_MEMBER_IS_VIP_CQ",
		// Duplicate value: 8192: "STATUS_IS_SOLDIER",
		// Duplicate value: 16384: "STATUS_IS_HELP",
		// Duplicate value: 32768: "STATUS_IS_PHOTO",
		// Duplicate value: 65536: "STATUS_IS_GM",
		// Duplicate value: 131072: "STATUS_IS_CITY",
		// Duplicate value: 262144: "STATUS_IS_TEACHER",
		// Duplicate value: 524288: "STATUS_VIP_TX_CQ",
		// Duplicate value: 1048576: "STATUS_IS_TOURIST",
		// Duplicate value: 2097152: "STATUS_IS_ROBOT",
		// Duplicate value: 0: "MODE_NORMAL",
		// Duplicate value: 1: "MODE_SHOP",
		// Duplicate value: 50: "MODE_BATTLE_LOCAL",
		// Duplicate value: 51: "MODE_BATTLE_REMOTE",
		// Duplicate value: 52: "MODE_BATTLE_PK",
		// Duplicate value: 53: "MODE_BATTLE_OB",
		// Duplicate value: 54: "MODE_BATTLE_PET_PK",
		// Duplicate value: 1: "NPC",
		// Duplicate value: 2: "BATTLE_PLAYER",
		// Duplicate value: 60: "MAX_STORE_NUM",
		9999999: "MAX_PLAYER_HP",
		// Duplicate value: 9999999: "MAX_PLAYER_MP",
	}
	Type_value = map[string]int32{
		"EXP":                        0,
		"EXPMAX":                     1,
		"HP":                         2,
		"MP":                         3,
		"CP":                         4,
		"SP":                         5,
		"STR":                        6,
		"CON":                        7,
		"AGI":                        8,
		"ILT":                        9,
		"WIS":                        10,
		"MONEY1":                     11,
		"MONEY2":                     12,
		"MONEY3":                     13,
		"NUM_BAG":                    14,
		"NUM_STROE":                  15,
		"COUNTRY_HONOR":              16,
		"CITY_ID":                    17,
		"KILL_COUNT":                 18,
		"PK_WIN_COUNT":               19,
		"PK_LOSE_COUNT":              20,
		"TOTAL_ONLINE":               21,
		"PARTNER_ID":                 22,
		"MASTER_ID":                  23,
		"APPRENTICE1":                24,
		"APPRENTICE2":                25,
		"APPRENTICE3":                26,
		"APPRENTICE4":                27,
		"APPRENTICE5":                28,
		"HPMAX":                      29,
		"MPMAX":                      30,
		"SPEED":                      31,
		"ATK_TYPE":                   32,
		"ATK_MIN":                    33,
		"ATK_MAX":                    34,
		"ATK_TIME":                   35,
		"ATK_STR":                    36,
		"ATK_AGI":                    37,
		"ATK_MAGIC":                  38,
		"DEF_STR":                    39,
		"DEF_AGI":                    40,
		"DEF_MAGIC":                  41,
		"DODGE":                      42,
		"HIT_RATE":                   43,
		"HIT_MAGIC":                  44,
		"CRITICAL":                   45,
		"FORCE_HIT":                  46,
		"EXP_UP":                     47,
		"WIL":                        48,
		"TOUGH":                      49,
		"BLOCK":                      50,
		"BRK_ARMOR":                  51,
		"LONG_TROUSERS_IN_TRANSPORT": 52,
		"INSIGHT":                    53,
		"DEF_FIELD":                  54,
		"BACK":                       55,
		"MAGIC_BACK":                 56,
		"LIFE_ABSORPTION":            57,
		"MANA_ABSORPTION":            58,
		"MAGIC_PENETRATION":          59,
		"HEAL_RECOVERY":              60,
		"MANA_RECOVERY":              61,
		"RECOVERY":                   62,
		"ARGO":                       63,
		"BACK_MAX":                   64,
		"MASTER_FLAG":                65,
		"HELP_COUNTRY":               66,
		"LOVE_PLAYER":                67,
		"INTEGRAL":                   68,
		"PARTNER_NAME":               69,
		"CRITICAL_DAMAGE":            70,
		"LEFT_WEAPON_TYPE":           71,
		"RIGHT_WEAPON_TYPE":          72,
		"LEFT_ATK_MIN":               73,
		"LEFT_ATK_MAX":               74,
		"RIGHT_ATK_MIN":              75,
		"RIGHT_ATK_MAX":              76,
		"LEFT_ATK_TIME":              77,
		"RIGHT_ATK_TIME":             78,
		"PET_COLOR":                  81,
		"PET_GRADE":                  82,
		"PET_GROW_LEVEL":             83,
		"BUFFER_REMOVE_STATUS":       90,
		"HP_DISPLAY":                 91,
		"MP_DISPLAY":                 92,
		"UID":                        101,
		"ID":                         102,
		"SEX":                        103,
		"RACE":                       104,
		"JOB":                        105,
		"LEVEL":                      106,
		"ICON":                       107,
		"SETTING":                    108,
		"STATUS":                     109,
		"COUNTRY_ID":                 110,
		"COUNTRY_RANK":               111,
		"COUNTRY_NAME":               112,
		"PX":                         113,
		"PY":                         114,
		"COUNTRY_RANK_SET":           115,
		"COUNTRY_ID_SET":             116,
		"STATUS_SET":                 117,
		"LEVEL2":                     118,
		"EXP2":                       119,
		"EXPMAX2":                    120,
		"ENCHANTVALUE":               121,
		"COMBATPOINT":                122,
		"VIP_LEVEL_2":                123,
		"NPC_MSS_NONE":               0,
		"NPC_MSS_SUBMIT":             1,
		"NPC_MSS_NOT_SUBMIT":         2,
		"NPC_MSS_ACCEPT":             3,
		"NPC_MSS_NOT_ACCEPT":         4,
		"MAX_MERCENARY_MEMBER":       2,
		"SET_EXP":                    5001,
		"SET_EXPMAX":                 5002,
		"SET_EXP2":                   5003,
		"SET_EXPMAX2":                5004,
		"MAX_MOSTER_HP":              5000000,
		"MAX_BASE_ATTRIBUTE":         2147483647,
		"MAX_OTHER_ATTRIBUTE":        1000000,
		"MAX_DEF":                    2147483647,
		"MAX_PROBABILITY":            100,
		"IGNORE_BACK":                200,
		"IGNORE_MAGIC_BACK":          201,
		"IGNORE_BLOCK":               202,
		"IGNORE_INSIGHT":             203,
		"IGNORE_WIL":                 204,
		"IGNORE_TOUCH":               205,
		"IGNORE_CRITICAL":            206,
		"DEF_STR_NEARBY":             220,
		"DEF_STR_RANGE":              221,
		"DEF_AGI_RANGE":              222,
		"DEF_AGI_NEARBY":             223,
		"ATK_STR_RANGE":              230,
		"ATK_STR_NEARBY":             231,
		"ATK_AGI_RANGE":              232,
		"ATK_AGI_NEARBY":             233,
		"MIN_HIT_RATE":               30,
		"MIN_HIT_MAGIC":              20,
		"MIN_FORCE_HITRATE":          30,
		"MAX_FORCE_RATE":             70,
		"MIN_HIT_TIME":               1,
		"MAX_HIT_TIME":               99,
		"MAX_ATK":                    99999999,
		"MAX_KEEPOUT_ATK_TIME":       100,
		"MAX_OTHER_PROBABILITY":      1000,
		"MIN_DEF_FIELD":              -999999,
		"MIN_HEAL_RECOVERY":          -999999,
		"MIN_MANA_RECOVERY":          -999999,
		"KEEPOUT_ATK_TIME":           250,
		"NEW_REFLECTION":             251,
		"START_HAIR":                 0,
		"START_FACE":                 1000,
		"START_HAND":                 2000,
		"START_FEET":                 3000,
		"START_SHOULDER":             4000,
		"START_BACK":                 5000,
		"START_CLOTHES":              6000,
		"START_TROUSERS":             7000,
		"START_WEAPON":               8000,
		"START_HELMET":               10000,
		"START_PET":                  12000,
		"START_TRANSPORT":            14000,
		"START_FLASH":                15000,
		"START_MINI_SPRITE":          16000,
		"START_WEAPON_ADD1":          30000,
		"START_WEAPON_ADD2":          32000,
		"BIT_1":                      1,
		"BIT_2":                      3,
		"BIT_3":                      7,
		"BIT_4":                      15,
		"BIT_5":                      31,
		"BIT_6":                      63,
		"BIT_7":                      127,
		"BIT_8":                      255,
		"OFFSET_SEX":                 0,
		"OFFSET_RACE":                1,
		"OFFSET_JOB":                 3,
		"OFFSET_HAIR_STYLE":          7,
		"OFFSET_HAIR_COLOR":          11,
		"OFFSET_FACE_STYLE":          13,
		"OFFSET_HAND_STYLE":          17,
		"OFFSET_HAND_COLOR":          19,
		"OFFSET_FEET_STYLE":          21,
		"OFFSET_FEET_COLOR":          23,
		"OFFSET_HELMET_STYLE":        25,
		"OFFSET_HELMET_COLOR":        30,
		"OFFSET_HAIR_ADD":            0,
		"OFFSET_FACE_ADD":            3,
		"OFFSET_HAND_ADD":            4,
		"OFFSET_FEET_ADD":            8,
		"OFFSET_HELMET_ADD":          12,
		"OFFSET_SHOULDER_STYLE":      0,
		"OFFSET_SHOULDER_COLOR":      4,
		"OFFSET_LWEAPON_STYLE":       6,
		"OFFSET_LWEAPON_COLOR":       14,
		"OFFSET_RWEAPON_STYLE":       16,
		"OFFSET_RWEAPON_COLOR":       24,
		"OFFSET_VIP":                 26,
		"OFFSET_WEAPON_FLASH":        28,
		"OFFSET_SHOULDER_ADD":        31,
		"OFFSET_LWEAPON_ADD":         0,
		"OFFSET_RWEAPON_ADD":         2,
		"OFFSET_SHOULDER_ADD_2":      4,
		"OFFSET_BACK_STYLE":          0,
		"OFFSET_BACK_COLOR":          4,
		"OFFSET_CLOTHES_STYLE":       6,
		"OFFSET_CLOTHES_COLOR":       12,
		"OFFSET_TROUSERS_STYLE":      14,
		"OFFSET_TROUSERS_COLOR":      20,
		"OFFSET_TRANSPORT_STYLE":     22,
		"OFFSET_TRANSPORT_COLOR":     26,
		"OFFSET_BACK_ADD":            28,
		"OFFSET_TRANSPORT_ADD":       29,
		"OFFSET_FASH_ADD":            30,
		"OFFSET_TRANSPORT_ADD_2":     31,
		"OFFSET_BACK_ADD_2":          0,
		"OFFSET_CLOTHES_ADD":         2,
		"OFFSET_TROUSERS_ADD":        3,
		"OFFSET_TRANSPORT_ADD_3":     4,
		"LEN_SEX":                    1,
		"LEN_RACE":                   3,
		"LEN_JOB":                    15,
		"LEN_HAIR_STYLE":             15,
		"LEN_HAIR_COLOR":             3,
		"LEN_FACE_STYLE":             15,
		"LEN_HAND_STYLE":             3,
		"LEN_HAND_COLOR":             3,
		"LEN_FEET_STYLE":             3,
		"LEN_FEET_COLOR":             3,
		"LEN_HELMET_STYLE":           31,
		"LEN_HELMET_COLOR":           3,
		"LEN_HAIR_ADD":               7,
		"LEN_FACE_ADD":               1,
		"LEN_HAND_ADD":               15,
		"LEN_FEET_ADD":               15,
		"LEN_HELMET_ADD":             3,
		"LEN_SHOULDER_STYLE":         15,
		"LEN_SHOULDER_COLOR":         3,
		"LEN_LWEAPON_STYLE":          255,
		"LEN_LWEAPON_COLOR":          3,
		"LEN_RWEAPON_STYLE":          255,
		"LEN_RWEAPON_COLOR":          3,
		"LEN_VIP":                    3,
		"LEN_WEAPON_FLASH":           7,
		"LEN_SHOULDER_ADD":           1,
		"LEN_LWEAPON_ADD":            3,
		"LEN_RWEAPON_ADD":            3,
		"LEN_SHOULDER_ADD_2":         3,
		"LEN_BACK_STYLE":             15,
		"LEN_BACK_COLOR":             3,
		"LEN_CLOTHES_STYLE":          63,
		"LEN_CLOTHES_COLOR":          3,
		"LEN_TROUSERS_STYLE":         63,
		"LEN_TROUSERS_COLOR":         3,
		"LEN_TRANSPORT_STYLE":        15,
		"LEN_TRANSPORT_COLOR":        3,
		"LEN_BACK_ADD":               1,
		"LEN_TRANSPORT_ADD":          1,
		"LEN_FASH_ADD":               1,
		"LEN_TRANSPORT_ADD_2":        1,
		"LEN_BACK_ADD_2":             3,
		"LEN_CLOTHES_ADD":            1,
		"LEN_TROUSERS_ADD":           1,
		"LEN_TRANSPORT_ADD_3":        1,
		"OFFSET_PET_STYLE":           0,
		"OFFSET_PET_COLOR":           6,
		"OFFSET_LVUP_STYLE":          8,
		"LEN_PET_STYLE":              63,
		"LEN_PET_COLOR":              3,
		"LEN_LVUP_STYLE":             15,
		"MOVE_END_CHECK":             1,
		"LAST_MOVE_FILL":             2,
		"NPC_MISSION_LOAD":           4,
		"MOVE_NPC_WELCOME":           8,
		"MERCENARY_INFO":             16,
		"PET_INFO":                   32,
		"DEL_STATUS":                 64,
		"MONSTER_BOOK_INFO":          128,
		"PET_INNER_SHOW":             256,
		"OFFLINE_DOING":              512,
		"NPC_MISSION_DISPLAY":        1024,
		"NPC_MISSION_HIDE":           2048,
		"NPC_COUNTRY_BLOAD":          4096,
		"MAIL_NEW_NOTICE":            8192,
		"ATTR_NEW_NOTICE":            16384,
		"CHAT_NEW_NOTICE":            32768,
		"SPRITE_LOADING_NPC":         65536,
		"SELL_PLAYER_INFO_LOAD":      131072,
		"SELL_PLAYER_STORE_LOAD":     262144,
		"HIDE_SPIRTE":                524288,
		"HIDE_PHOTO_COMMAND":         1048576,
		"SELL_PLAYER_STORE_VIP_LOAD": 2097152,
		"BUFFER_DIE_1HP_CHECK":       134217728,
		"BUFFER_DIE_FULLHP_CHECK":    268435456,
		"BUFFER_DIE_DELAY_CHECK":     536870912,
		"TAG_IS_KEEP_OUT":            1073741824,
		"COUNTRY_APPLY":              -2147483648,
		"STATUS_NONE":                0,
		"STATUS_ONLINE":              1,
		"STATUS_ALL_DEL":             2,
		"STATUS_TEMP_DEL":            4,
		"STATUS_FROST":               8,
		"STATUS_SELL":                16,
		"STATUS_ENGAGE":              32,
		"STATUS_CLOSE":               64,
		"STATUS_MOUTH":               128,
		"STATUS_COMMENTS":            256,
		"STATUS_OFFLINEMISSION":      512,
		"STATUS_VIP":                 1024,
		"STATUS_IS_CARD":             2048,
		"STATUS_CAN_MASTER":          4096,
		"STATUS_NEW":                 8192,
		"PHOTO_FLAG_VIP":             1,
		"PHOTO_IS_VIP_CQ":            2,
		"COUNTRY_FFLAG_IS_VIP":       1,
		"COUNTRY_FFLAG_IS_VIP_CQ":    2,
		"COUNTRY_MEMBER_IS_VIP":      1,
		"COUNTRY_MEMBER_IS_VIP_CQ":   2,
		"STATUS_IS_SOLDIER":          8192,
		"STATUS_IS_HELP":             16384,
		"STATUS_IS_PHOTO":            32768,
		"STATUS_IS_GM":               65536,
		"STATUS_IS_CITY":             131072,
		"STATUS_IS_TEACHER":          262144,
		"STATUS_VIP_TX_CQ":           524288,
		"STATUS_IS_TOURIST":          1048576,
		"STATUS_IS_ROBOT":            2097152,
		"MODE_NORMAL":                0,
		"MODE_SHOP":                  1,
		"MODE_BATTLE_LOCAL":          50,
		"MODE_BATTLE_REMOTE":         51,
		"MODE_BATTLE_PK":             52,
		"MODE_BATTLE_OB":             53,
		"MODE_BATTLE_PET_PK":         54,
		"NPC":                        1,
		"BATTLE_PLAYER":              2,
		"MAX_STORE_NUM":              60,
		"MAX_PLAYER_HP":              9999999,
		"MAX_PLAYER_MP":              9999999,
	}
)

func (x Type) Enum() *Type {
	p := new(Type)
	*p = x
	return p
}

func (x Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pbBase_ModelConst_ModelConst_proto_enumTypes[0].Descriptor()
}

func (Type) Type() protoreflect.EnumType {
	return &file_pbBase_ModelConst_ModelConst_proto_enumTypes[0]
}

func (x Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type.Descriptor instead.
func (Type) EnumDescriptor() ([]byte, []int) {
	return file_pbBase_ModelConst_ModelConst_proto_rawDescGZIP(), []int{0}
}

var File_pbBase_ModelConst_ModelConst_proto protoreflect.FileDescriptor

var file_pbBase_ModelConst_ModelConst_proto_rawDesc = []byte{
	0x0a, 0x22, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f,
	0x6e, 0x73, 0x74, 0x2f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x2a, 0xce, 0x33, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x07, 0x0a, 0x03, 0x45, 0x58, 0x50, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x58, 0x50, 0x4d,
	0x41, 0x58, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x48, 0x50, 0x10, 0x02, 0x12, 0x06, 0x0a, 0x02,
	0x4d, 0x50, 0x10, 0x03, 0x12, 0x06, 0x0a, 0x02, 0x43, 0x50, 0x10, 0x04, 0x12, 0x06, 0x0a, 0x02,
	0x53, 0x50, 0x10, 0x05, 0x12, 0x07, 0x0a, 0x03, 0x53, 0x54, 0x52, 0x10, 0x06, 0x12, 0x07, 0x0a,
	0x03, 0x43, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x47, 0x49, 0x10, 0x08, 0x12,
	0x07, 0x0a, 0x03, 0x49, 0x4c, 0x54, 0x10, 0x09, 0x12, 0x07, 0x0a, 0x03, 0x57, 0x49, 0x53, 0x10,
	0x0a, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x31, 0x10, 0x0b, 0x12, 0x0a, 0x0a,
	0x06, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x32, 0x10, 0x0c, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x4f, 0x4e,
	0x45, 0x59, 0x33, 0x10, 0x0d, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x55, 0x4d, 0x5f, 0x42, 0x41, 0x47,
	0x10, 0x0e, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x55, 0x4d, 0x5f, 0x53, 0x54, 0x52, 0x4f, 0x45, 0x10,
	0x0f, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x48, 0x4f, 0x4e,
	0x4f, 0x52, 0x10, 0x10, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x49, 0x54, 0x59, 0x5f, 0x49, 0x44, 0x10,
	0x11, 0x12, 0x0e, 0x0a, 0x0a, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10,
	0x12, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x4b, 0x5f, 0x57, 0x49, 0x4e, 0x5f, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x10, 0x13, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4b, 0x5f, 0x4c, 0x4f, 0x53, 0x45, 0x5f, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x10, 0x14, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x5f,
	0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x15, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x41, 0x52, 0x54,
	0x4e, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x16, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x41, 0x53, 0x54,
	0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x17, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50, 0x52, 0x45,
	0x4e, 0x54, 0x49, 0x43, 0x45, 0x31, 0x10, 0x18, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50, 0x52,
	0x45, 0x4e, 0x54, 0x49, 0x43, 0x45, 0x32, 0x10, 0x19, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50, 0x50,
	0x52, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x45, 0x33, 0x10, 0x1a, 0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x50,
	0x50, 0x52, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x45, 0x34, 0x10, 0x1b, 0x12, 0x0f, 0x0a, 0x0b, 0x41,
	0x50, 0x50, 0x52, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x45, 0x35, 0x10, 0x1c, 0x12, 0x09, 0x0a, 0x05,
	0x48, 0x50, 0x4d, 0x41, 0x58, 0x10, 0x1d, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x50, 0x4d, 0x41, 0x58,
	0x10, 0x1e, 0x12, 0x09, 0x0a, 0x05, 0x53, 0x50, 0x45, 0x45, 0x44, 0x10, 0x1f, 0x12, 0x0c, 0x0a,
	0x08, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x20, 0x12, 0x0b, 0x0a, 0x07, 0x41,
	0x54, 0x4b, 0x5f, 0x4d, 0x49, 0x4e, 0x10, 0x21, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x54, 0x4b, 0x5f,
	0x4d, 0x41, 0x58, 0x10, 0x22, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d,
	0x45, 0x10, 0x23, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x54, 0x4b, 0x5f, 0x53, 0x54, 0x52, 0x10, 0x24,
	0x12, 0x0b, 0x0a, 0x07, 0x41, 0x54, 0x4b, 0x5f, 0x41, 0x47, 0x49, 0x10, 0x25, 0x12, 0x0d, 0x0a,
	0x09, 0x41, 0x54, 0x4b, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x10, 0x26, 0x12, 0x0b, 0x0a, 0x07,
	0x44, 0x45, 0x46, 0x5f, 0x53, 0x54, 0x52, 0x10, 0x27, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46,
	0x5f, 0x41, 0x47, 0x49, 0x10, 0x28, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x45, 0x46, 0x5f, 0x4d, 0x41,
	0x47, 0x49, 0x43, 0x10, 0x29, 0x12, 0x09, 0x0a, 0x05, 0x44, 0x4f, 0x44, 0x47, 0x45, 0x10, 0x2a,
	0x12, 0x0c, 0x0a, 0x08, 0x48, 0x49, 0x54, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x10, 0x2b, 0x12, 0x0d,
	0x0a, 0x09, 0x48, 0x49, 0x54, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x10, 0x2c, 0x12, 0x0c, 0x0a,
	0x08, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x2d, 0x12, 0x0d, 0x0a, 0x09, 0x46,
	0x4f, 0x52, 0x43, 0x45, 0x5f, 0x48, 0x49, 0x54, 0x10, 0x2e, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x58,
	0x50, 0x5f, 0x55, 0x50, 0x10, 0x2f, 0x12, 0x07, 0x0a, 0x03, 0x57, 0x49, 0x4c, 0x10, 0x30, 0x12,
	0x09, 0x0a, 0x05, 0x54, 0x4f, 0x55, 0x47, 0x48, 0x10, 0x31, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x4c,
	0x4f, 0x43, 0x4b, 0x10, 0x32, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x52, 0x4b, 0x5f, 0x41, 0x52, 0x4d,
	0x4f, 0x52, 0x10, 0x33, 0x12, 0x1e, 0x0a, 0x1a, 0x4c, 0x4f, 0x4e, 0x47, 0x5f, 0x54, 0x52, 0x4f,
	0x55, 0x53, 0x45, 0x52, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f,
	0x52, 0x54, 0x10, 0x34, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x10,
	0x35, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x45, 0x46, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x10, 0x36,
	0x12, 0x08, 0x0a, 0x04, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x37, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x41,
	0x47, 0x49, 0x43, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x38, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x49,
	0x46, 0x45, 0x5f, 0x41, 0x42, 0x53, 0x4f, 0x52, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x39, 0x12,
	0x13, 0x0a, 0x0f, 0x4d, 0x41, 0x4e, 0x41, 0x5f, 0x41, 0x42, 0x53, 0x4f, 0x52, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x3a, 0x12, 0x15, 0x0a, 0x11, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x50, 0x45,
	0x4e, 0x45, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x3b, 0x12, 0x11, 0x0a, 0x0d, 0x48,
	0x45, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x59, 0x10, 0x3c, 0x12, 0x11,
	0x0a, 0x0d, 0x4d, 0x41, 0x4e, 0x41, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x59, 0x10,
	0x3d, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x59, 0x10, 0x3e, 0x12,
	0x08, 0x0a, 0x04, 0x41, 0x52, 0x47, 0x4f, 0x10, 0x3f, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x4d, 0x41, 0x58, 0x10, 0x40, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x41, 0x53, 0x54, 0x45,
	0x52, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x10, 0x41, 0x12, 0x10, 0x0a, 0x0c, 0x48, 0x45, 0x4c, 0x50,
	0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x10, 0x42, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x4f,
	0x56, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x10, 0x43, 0x12, 0x0c, 0x0a, 0x08, 0x49,
	0x4e, 0x54, 0x45, 0x47, 0x52, 0x41, 0x4c, 0x10, 0x44, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x41, 0x52,
	0x54, 0x4e, 0x45, 0x52, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x45, 0x12, 0x13, 0x0a, 0x0f, 0x43,
	0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x44, 0x41, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x46,
	0x12, 0x14, 0x0a, 0x10, 0x4c, 0x45, 0x46, 0x54, 0x5f, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x10, 0x47, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x49, 0x47, 0x48, 0x54, 0x5f,
	0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x48, 0x12, 0x10, 0x0a,
	0x0c, 0x4c, 0x45, 0x46, 0x54, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x4d, 0x49, 0x4e, 0x10, 0x49, 0x12,
	0x10, 0x0a, 0x0c, 0x4c, 0x45, 0x46, 0x54, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x4d, 0x41, 0x58, 0x10,
	0x4a, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x4d,
	0x49, 0x4e, 0x10, 0x4b, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x41, 0x54,
	0x4b, 0x5f, 0x4d, 0x41, 0x58, 0x10, 0x4c, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x45, 0x46, 0x54, 0x5f,
	0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x4d, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x49,
	0x47, 0x48, 0x54, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x4e, 0x12, 0x0d,
	0x0a, 0x09, 0x50, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x51, 0x12, 0x0d, 0x0a,
	0x09, 0x50, 0x45, 0x54, 0x5f, 0x47, 0x52, 0x41, 0x44, 0x45, 0x10, 0x52, 0x12, 0x12, 0x0a, 0x0e,
	0x50, 0x45, 0x54, 0x5f, 0x47, 0x52, 0x4f, 0x57, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x10, 0x53,
	0x12, 0x18, 0x0a, 0x14, 0x42, 0x55, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x4d, 0x4f, 0x56,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x5a, 0x12, 0x0e, 0x0a, 0x0a, 0x48, 0x50,
	0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x10, 0x5b, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x50,
	0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x10, 0x5c, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x49,
	0x44, 0x10, 0x65, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x44, 0x10, 0x66, 0x12, 0x07, 0x0a, 0x03, 0x53,
	0x45, 0x58, 0x10, 0x67, 0x12, 0x08, 0x0a, 0x04, 0x52, 0x41, 0x43, 0x45, 0x10, 0x68, 0x12, 0x07,
	0x0a, 0x03, 0x4a, 0x4f, 0x42, 0x10, 0x69, 0x12, 0x09, 0x0a, 0x05, 0x4c, 0x45, 0x56, 0x45, 0x4c,
	0x10, 0x6a, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x43, 0x4f, 0x4e, 0x10, 0x6b, 0x12, 0x0b, 0x0a, 0x07,
	0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x6c, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x10, 0x6d, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59,
	0x5f, 0x49, 0x44, 0x10, 0x6e, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59,
	0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x10, 0x6f, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x52, 0x59, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x70, 0x12, 0x06, 0x0a, 0x02, 0x50, 0x58, 0x10,
	0x71, 0x12, 0x06, 0x0a, 0x02, 0x50, 0x59, 0x10, 0x72, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x52, 0x59, 0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x73, 0x12,
	0x12, 0x0a, 0x0e, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x49, 0x44, 0x5f, 0x53, 0x45,
	0x54, 0x10, 0x74, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x45,
	0x54, 0x10, 0x75, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x32, 0x10, 0x76, 0x12,
	0x08, 0x0a, 0x04, 0x45, 0x58, 0x50, 0x32, 0x10, 0x77, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50,
	0x4d, 0x41, 0x58, 0x32, 0x10, 0x78, 0x12, 0x10, 0x0a, 0x0c, 0x45, 0x4e, 0x43, 0x48, 0x41, 0x4e,
	0x54, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x10, 0x79, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x4f, 0x4d, 0x42,
	0x41, 0x54, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x10, 0x7a, 0x12, 0x0f, 0x0a, 0x0b, 0x56, 0x49, 0x50,
	0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x5f, 0x32, 0x10, 0x7b, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x50,
	0x43, 0x5f, 0x4d, 0x53, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e,
	0x4e, 0x50, 0x43, 0x5f, 0x4d, 0x53, 0x53, 0x5f, 0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x10, 0x01,
	0x12, 0x16, 0x0a, 0x12, 0x4e, 0x50, 0x43, 0x5f, 0x4d, 0x53, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x53, 0x55, 0x42, 0x4d, 0x49, 0x54, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x50, 0x43, 0x5f,
	0x4d, 0x53, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12,
	0x4e, 0x50, 0x43, 0x5f, 0x4d, 0x53, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x45,
	0x50, 0x54, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x41, 0x58, 0x5f, 0x4d, 0x45, 0x52, 0x43,
	0x45, 0x4e, 0x41, 0x52, 0x59, 0x5f, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x02, 0x12, 0x0c,
	0x0a, 0x07, 0x53, 0x45, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x10, 0x89, 0x27, 0x12, 0x0f, 0x0a, 0x0a,
	0x53, 0x45, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x4d, 0x41, 0x58, 0x10, 0x8a, 0x27, 0x12, 0x0d, 0x0a,
	0x08, 0x53, 0x45, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x32, 0x10, 0x8b, 0x27, 0x12, 0x10, 0x0a, 0x0b,
	0x53, 0x45, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x4d, 0x41, 0x58, 0x32, 0x10, 0x8c, 0x27, 0x12, 0x14,
	0x0a, 0x0d, 0x4d, 0x41, 0x58, 0x5f, 0x4d, 0x4f, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x48, 0x50, 0x10,
	0xc0, 0x96, 0xb1, 0x02, 0x12, 0x1a, 0x0a, 0x12, 0x4d, 0x41, 0x58, 0x5f, 0x42, 0x41, 0x53, 0x45,
	0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x10, 0xff, 0xff, 0xff, 0xff, 0x07,
	0x12, 0x19, 0x0a, 0x13, 0x4d, 0x41, 0x58, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x41, 0x54,
	0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x10, 0xc0, 0x84, 0x3d, 0x12, 0x0f, 0x0a, 0x07, 0x4d,
	0x41, 0x58, 0x5f, 0x44, 0x45, 0x46, 0x10, 0xff, 0xff, 0xff, 0xff, 0x07, 0x12, 0x13, 0x0a, 0x0f,
	0x4d, 0x41, 0x58, 0x5f, 0x50, 0x52, 0x4f, 0x42, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10,
	0x64, 0x12, 0x10, 0x0a, 0x0b, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x41, 0x43, 0x4b,
	0x10, 0xc8, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x4d, 0x41,
	0x47, 0x49, 0x43, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0xc9, 0x01, 0x12, 0x11, 0x0a, 0x0c, 0x49,
	0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0xca, 0x01, 0x12, 0x13,
	0x0a, 0x0e, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x49, 0x4e, 0x53, 0x49, 0x47, 0x48, 0x54,
	0x10, 0xcb, 0x01, 0x12, 0x0f, 0x0a, 0x0a, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x57, 0x49,
	0x4c, 0x10, 0xcc, 0x01, 0x12, 0x11, 0x0a, 0x0c, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x54,
	0x4f, 0x55, 0x43, 0x48, 0x10, 0xcd, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x49, 0x47, 0x4e, 0x4f, 0x52,
	0x45, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0xce, 0x01, 0x12, 0x13, 0x0a,
	0x0e, 0x44, 0x45, 0x46, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x4e, 0x45, 0x41, 0x52, 0x42, 0x59, 0x10,
	0xdc, 0x01, 0x12, 0x12, 0x0a, 0x0d, 0x44, 0x45, 0x46, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x52, 0x41,
	0x4e, 0x47, 0x45, 0x10, 0xdd, 0x01, 0x12, 0x12, 0x0a, 0x0d, 0x44, 0x45, 0x46, 0x5f, 0x41, 0x47,
	0x49, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x10, 0xde, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x44, 0x45,
	0x46, 0x5f, 0x41, 0x47, 0x49, 0x5f, 0x4e, 0x45, 0x41, 0x52, 0x42, 0x59, 0x10, 0xdf, 0x01, 0x12,
	0x12, 0x0a, 0x0d, 0x41, 0x54, 0x4b, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45,
	0x10, 0xe6, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x41, 0x54, 0x4b, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x4e,
	0x45, 0x41, 0x52, 0x42, 0x59, 0x10, 0xe7, 0x01, 0x12, 0x12, 0x0a, 0x0d, 0x41, 0x54, 0x4b, 0x5f,
	0x41, 0x47, 0x49, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x10, 0xe8, 0x01, 0x12, 0x13, 0x0a, 0x0e,
	0x41, 0x54, 0x4b, 0x5f, 0x41, 0x47, 0x49, 0x5f, 0x4e, 0x45, 0x41, 0x52, 0x42, 0x59, 0x10, 0xe9,
	0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x49, 0x4e, 0x5f, 0x48, 0x49, 0x54, 0x5f, 0x52, 0x41, 0x54,
	0x45, 0x10, 0x1e, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x49, 0x4e, 0x5f, 0x48, 0x49, 0x54, 0x5f, 0x4d,
	0x41, 0x47, 0x49, 0x43, 0x10, 0x14, 0x12, 0x15, 0x0a, 0x11, 0x4d, 0x49, 0x4e, 0x5f, 0x46, 0x4f,
	0x52, 0x43, 0x45, 0x5f, 0x48, 0x49, 0x54, 0x52, 0x41, 0x54, 0x45, 0x10, 0x1e, 0x12, 0x12, 0x0a,
	0x0e, 0x4d, 0x41, 0x58, 0x5f, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x10,
	0x46, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x49, 0x4e, 0x5f, 0x48, 0x49, 0x54, 0x5f, 0x54, 0x49, 0x4d,
	0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x41, 0x58, 0x5f, 0x48, 0x49, 0x54, 0x5f, 0x54,
	0x49, 0x4d, 0x45, 0x10, 0x63, 0x12, 0x0e, 0x0a, 0x07, 0x4d, 0x41, 0x58, 0x5f, 0x41, 0x54, 0x4b,
	0x10, 0xff, 0xc1, 0xd7, 0x2f, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x41, 0x58, 0x5f, 0x4b, 0x45, 0x45,
	0x50, 0x4f, 0x55, 0x54, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x64, 0x12,
	0x1a, 0x0a, 0x15, 0x4d, 0x41, 0x58, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x4f,
	0x42, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0xe8, 0x07, 0x12, 0x1a, 0x0a, 0x0d, 0x4d,
	0x49, 0x4e, 0x5f, 0x44, 0x45, 0x46, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x10, 0xc1, 0xfb, 0xc2,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x1e, 0x0a, 0x11, 0x4d, 0x49, 0x4e, 0x5f, 0x48,
	0x45, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x59, 0x10, 0xc1, 0xfb, 0xc2,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x1e, 0x0a, 0x11, 0x4d, 0x49, 0x4e, 0x5f, 0x4d,
	0x41, 0x4e, 0x41, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x59, 0x10, 0xc1, 0xfb, 0xc2,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x15, 0x0a, 0x10, 0x4b, 0x45, 0x45, 0x50, 0x4f,
	0x55, 0x54, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0xfa, 0x01, 0x12, 0x13,
	0x0a, 0x0e, 0x4e, 0x45, 0x57, 0x5f, 0x52, 0x45, 0x46, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0xfb, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x48, 0x41, 0x49,
	0x52, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0a, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x46, 0x41, 0x43,
	0x45, 0x10, 0xe8, 0x07, 0x12, 0x0f, 0x0a, 0x0a, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x48, 0x41,
	0x4e, 0x44, 0x10, 0xd0, 0x0f, 0x12, 0x0f, 0x0a, 0x0a, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x46,
	0x45, 0x45, 0x54, 0x10, 0xb8, 0x17, 0x12, 0x13, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f,
	0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x45, 0x52, 0x10, 0xa0, 0x1f, 0x12, 0x0f, 0x0a, 0x0a, 0x53,
	0x54, 0x41, 0x52, 0x54, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x88, 0x27, 0x12, 0x12, 0x0a, 0x0d,
	0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x45, 0x53, 0x10, 0xf0, 0x2e,
	0x12, 0x13, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x54, 0x52, 0x4f, 0x55, 0x53, 0x45,
	0x52, 0x53, 0x10, 0xd8, 0x36, 0x12, 0x11, 0x0a, 0x0c, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x57,
	0x45, 0x41, 0x50, 0x4f, 0x4e, 0x10, 0xc0, 0x3e, 0x12, 0x11, 0x0a, 0x0c, 0x53, 0x54, 0x41, 0x52,
	0x54, 0x5f, 0x48, 0x45, 0x4c, 0x4d, 0x45, 0x54, 0x10, 0x90, 0x4e, 0x12, 0x0e, 0x0a, 0x09, 0x53,
	0x54, 0x41, 0x52, 0x54, 0x5f, 0x50, 0x45, 0x54, 0x10, 0xe0, 0x5d, 0x12, 0x14, 0x0a, 0x0f, 0x53,
	0x54, 0x41, 0x52, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x10, 0xb0,
	0x6d, 0x12, 0x10, 0x0a, 0x0b, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x46, 0x4c, 0x41, 0x53, 0x48,
	0x10, 0x98, 0x75, 0x12, 0x16, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x4d, 0x49, 0x4e,
	0x49, 0x5f, 0x53, 0x50, 0x52, 0x49, 0x54, 0x45, 0x10, 0x80, 0x7d, 0x12, 0x17, 0x0a, 0x11, 0x53,
	0x54, 0x41, 0x52, 0x54, 0x5f, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x31,
	0x10, 0xb0, 0xea, 0x01, 0x12, 0x17, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x57, 0x45,
	0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x32, 0x10, 0x80, 0xfa, 0x01, 0x12, 0x09, 0x0a,
	0x05, 0x42, 0x49, 0x54, 0x5f, 0x31, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x49, 0x54, 0x5f,
	0x32, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x49, 0x54, 0x5f, 0x33, 0x10, 0x07, 0x12, 0x09,
	0x0a, 0x05, 0x42, 0x49, 0x54, 0x5f, 0x34, 0x10, 0x0f, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x49, 0x54,
	0x5f, 0x35, 0x10, 0x1f, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x49, 0x54, 0x5f, 0x36, 0x10, 0x3f, 0x12,
	0x09, 0x0a, 0x05, 0x42, 0x49, 0x54, 0x5f, 0x37, 0x10, 0x7f, 0x12, 0x0a, 0x0a, 0x05, 0x42, 0x49,
	0x54, 0x5f, 0x38, 0x10, 0xff, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54,
	0x5f, 0x53, 0x45, 0x58, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54,
	0x5f, 0x52, 0x41, 0x43, 0x45, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x46, 0x46, 0x53, 0x45,
	0x54, 0x5f, 0x4a, 0x4f, 0x42, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x46, 0x46, 0x53, 0x45,
	0x54, 0x5f, 0x48, 0x41, 0x49, 0x52, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x07, 0x12, 0x15,
	0x0a, 0x11, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x48, 0x41, 0x49, 0x52, 0x5f, 0x43, 0x4f,
	0x4c, 0x4f, 0x52, 0x10, 0x0b, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f,
	0x46, 0x41, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x0d, 0x12, 0x15, 0x0a, 0x11,
	0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x59, 0x4c,
	0x45, 0x10, 0x11, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x48, 0x41,
	0x4e, 0x44, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x13, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x46,
	0x46, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x45, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10,
	0x15, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x45, 0x45, 0x54,
	0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x17, 0x12, 0x17, 0x0a, 0x13, 0x4f, 0x46, 0x46, 0x53,
	0x45, 0x54, 0x5f, 0x48, 0x45, 0x4c, 0x4d, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10,
	0x19, 0x12, 0x17, 0x0a, 0x13, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x48, 0x45, 0x4c, 0x4d,
	0x45, 0x54, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x1e, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x46,
	0x46, 0x53, 0x45, 0x54, 0x5f, 0x48, 0x41, 0x49, 0x52, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x00, 0x12,
	0x13, 0x0a, 0x0f, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x41, 0x43, 0x45, 0x5f, 0x41,
	0x44, 0x44, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x48,
	0x41, 0x4e, 0x44, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x46, 0x46,
	0x53, 0x45, 0x54, 0x5f, 0x46, 0x45, 0x45, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x08, 0x12, 0x15,
	0x0a, 0x11, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x48, 0x45, 0x4c, 0x4d, 0x45, 0x54, 0x5f,
	0x41, 0x44, 0x44, 0x10, 0x0c, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f,
	0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x00,
	0x12, 0x19, 0x0a, 0x15, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c,
	0x44, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x4f,
	0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x4c, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x53, 0x54,
	0x59, 0x4c, 0x45, 0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f,
	0x4c, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x0e, 0x12,
	0x18, 0x0a, 0x14, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x52, 0x57, 0x45, 0x41, 0x50, 0x4f,
	0x4e, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x10, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x46, 0x46,
	0x53, 0x45, 0x54, 0x5f, 0x52, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4c, 0x4f,
	0x52, 0x10, 0x18, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x56, 0x49,
	0x50, 0x10, 0x1a, 0x12, 0x17, 0x0a, 0x13, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x57, 0x45,
	0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x41, 0x53, 0x48, 0x10, 0x1c, 0x12, 0x17, 0x0a, 0x13,
	0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x45, 0x52, 0x5f,
	0x41, 0x44, 0x44, 0x10, 0x1f, 0x12, 0x16, 0x0a, 0x12, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f,
	0x4c, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a,
	0x12, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x52, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f,
	0x41, 0x44, 0x44, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f,
	0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x45, 0x52, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x32, 0x10, 0x04,
	0x12, 0x15, 0x0a, 0x11, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x46, 0x46, 0x53, 0x45,
	0x54, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x04, 0x12, 0x18,
	0x0a, 0x14, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x45, 0x53,
	0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x46, 0x46, 0x53,
	0x45, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x45, 0x53, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52,
	0x10, 0x0c, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x52, 0x4f,
	0x55, 0x53, 0x45, 0x52, 0x53, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x0e, 0x12, 0x19, 0x0a,
	0x15, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x52, 0x4f, 0x55, 0x53, 0x45, 0x52, 0x53,
	0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x14, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x46, 0x46, 0x53,
	0x45, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x59,
	0x4c, 0x45, 0x10, 0x16, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x1a,
	0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x41, 0x44, 0x44, 0x10, 0x1c, 0x12, 0x18, 0x0a, 0x14, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x1d, 0x12,
	0x13, 0x0a, 0x0f, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x41, 0x53, 0x48, 0x5f, 0x41,
	0x44, 0x44, 0x10, 0x1e, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x32, 0x10, 0x1f,
	0x12, 0x15, 0x0a, 0x11, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f,
	0x41, 0x44, 0x44, 0x5f, 0x32, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x4f, 0x46, 0x46, 0x53, 0x45,
	0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x45, 0x53, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x02, 0x12,
	0x17, 0x0a, 0x13, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x52, 0x4f, 0x55, 0x53, 0x45,
	0x52, 0x53, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x46, 0x46, 0x53,
	0x45, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x41, 0x44, 0x44,
	0x5f, 0x33, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x45, 0x4e, 0x5f, 0x53, 0x45, 0x58, 0x10,
	0x01, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x45, 0x4e, 0x5f, 0x52, 0x41, 0x43, 0x45, 0x10, 0x03, 0x12,
	0x0b, 0x0a, 0x07, 0x4c, 0x45, 0x4e, 0x5f, 0x4a, 0x4f, 0x42, 0x10, 0x0f, 0x12, 0x12, 0x0a, 0x0e,
	0x4c, 0x45, 0x4e, 0x5f, 0x48, 0x41, 0x49, 0x52, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x0f,
	0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x45, 0x4e, 0x5f, 0x48, 0x41, 0x49, 0x52, 0x5f, 0x43, 0x4f, 0x4c,
	0x4f, 0x52, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x45, 0x4e, 0x5f, 0x46, 0x41, 0x43, 0x45,
	0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x0f, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x45, 0x4e, 0x5f,
	0x48, 0x41, 0x4e, 0x44, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e,
	0x4c, 0x45, 0x4e, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x03,
	0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x45, 0x4e, 0x5f, 0x46, 0x45, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x59,
	0x4c, 0x45, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x45, 0x4e, 0x5f, 0x46, 0x45, 0x45, 0x54,
	0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x45, 0x4e, 0x5f,
	0x48, 0x45, 0x4c, 0x4d, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x1f, 0x12, 0x14,
	0x0a, 0x10, 0x4c, 0x45, 0x4e, 0x5f, 0x48, 0x45, 0x4c, 0x4d, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x4c,
	0x4f, 0x52, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x45, 0x4e, 0x5f, 0x48, 0x41, 0x49, 0x52,
	0x5f, 0x41, 0x44, 0x44, 0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x45, 0x4e, 0x5f, 0x46, 0x41,
	0x43, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x45, 0x4e, 0x5f,
	0x48, 0x41, 0x4e, 0x44, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x0f, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x45,
	0x4e, 0x5f, 0x46, 0x45, 0x45, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x0f, 0x12, 0x12, 0x0a, 0x0e,
	0x4c, 0x45, 0x4e, 0x5f, 0x48, 0x45, 0x4c, 0x4d, 0x45, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x03,
	0x12, 0x16, 0x0a, 0x12, 0x4c, 0x45, 0x4e, 0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x45, 0x52,
	0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x0f, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x45, 0x4e, 0x5f,
	0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x03,
	0x12, 0x16, 0x0a, 0x11, 0x4c, 0x45, 0x4e, 0x5f, 0x4c, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0xff, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x45, 0x4e, 0x5f,
	0x4c, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x03, 0x12,
	0x16, 0x0a, 0x11, 0x4c, 0x45, 0x4e, 0x5f, 0x52, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x53,
	0x54, 0x59, 0x4c, 0x45, 0x10, 0xff, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x45, 0x4e, 0x5f, 0x52,
	0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x03, 0x12, 0x0b,
	0x0a, 0x07, 0x4c, 0x45, 0x4e, 0x5f, 0x56, 0x49, 0x50, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x4c,
	0x45, 0x4e, 0x5f, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x46, 0x4c, 0x41, 0x53, 0x48, 0x10,
	0x07, 0x12, 0x14, 0x0a, 0x10, 0x4c, 0x45, 0x4e, 0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x45,
	0x52, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x45, 0x4e, 0x5f, 0x4c,
	0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f,
	0x4c, 0x45, 0x4e, 0x5f, 0x52, 0x57, 0x45, 0x41, 0x50, 0x4f, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x10,
	0x03, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x45, 0x4e, 0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x45,
	0x52, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x32, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x45, 0x4e,
	0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x0f, 0x12, 0x12, 0x0a,
	0x0e, 0x4c, 0x45, 0x4e, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10,
	0x03, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x45, 0x4e, 0x5f, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x45, 0x53,
	0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x3f, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x45, 0x4e, 0x5f,
	0x43, 0x4c, 0x4f, 0x54, 0x48, 0x45, 0x53, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x03, 0x12,
	0x16, 0x0a, 0x12, 0x4c, 0x45, 0x4e, 0x5f, 0x54, 0x52, 0x4f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x5f,
	0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x3f, 0x12, 0x16, 0x0a, 0x12, 0x4c, 0x45, 0x4e, 0x5f, 0x54,
	0x52, 0x4f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x03, 0x12,
	0x17, 0x0a, 0x13, 0x4c, 0x45, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x0f, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x45, 0x4e, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10,
	0x03, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x45, 0x4e, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x41, 0x44,
	0x44, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x4c, 0x45, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x45,
	0x4e, 0x5f, 0x46, 0x41, 0x53, 0x48, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13,
	0x4c, 0x45, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x41, 0x44,
	0x44, 0x5f, 0x32, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x45, 0x4e, 0x5f, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x32, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x45, 0x4e,
	0x5f, 0x43, 0x4c, 0x4f, 0x54, 0x48, 0x45, 0x53, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x14,
	0x0a, 0x10, 0x4c, 0x45, 0x4e, 0x5f, 0x54, 0x52, 0x4f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x5f, 0x41,
	0x44, 0x44, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x45, 0x4e, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x33, 0x10, 0x01, 0x12, 0x14, 0x0a,
	0x10, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x59, 0x4c,
	0x45, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4f, 0x46, 0x46, 0x53, 0x45, 0x54, 0x5f, 0x50, 0x45,
	0x54, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x46, 0x46,
	0x53, 0x45, 0x54, 0x5f, 0x4c, 0x56, 0x55, 0x50, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x08,
	0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x45, 0x4e, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x59, 0x4c,
	0x45, 0x10, 0x3f, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x45, 0x4e, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x43,
	0x4f, 0x4c, 0x4f, 0x52, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x45, 0x4e, 0x5f, 0x4c, 0x56,
	0x55, 0x50, 0x5f, 0x53, 0x54, 0x59, 0x4c, 0x45, 0x10, 0x0f, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x4f,
	0x56, 0x45, 0x5f, 0x45, 0x4e, 0x44, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x12,
	0x0a, 0x0e, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x4d, 0x4f, 0x56, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x4c,
	0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x4e, 0x50, 0x43, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f,
	0x4e, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x4f, 0x56, 0x45,
	0x5f, 0x4e, 0x50, 0x43, 0x5f, 0x57, 0x45, 0x4c, 0x43, 0x4f, 0x4d, 0x45, 0x10, 0x08, 0x12, 0x12,
	0x0a, 0x0e, 0x4d, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x41, 0x52, 0x59, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x10, 0x10, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x45, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x20,
	0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x45, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x40,
	0x12, 0x16, 0x0a, 0x11, 0x4d, 0x4f, 0x4e, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x42, 0x4f, 0x4f, 0x4b,
	0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x80, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x50, 0x45, 0x54, 0x5f,
	0x49, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x53, 0x48, 0x4f, 0x57, 0x10, 0x80, 0x02, 0x12, 0x12, 0x0a,
	0x0d, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x44, 0x4f, 0x49, 0x4e, 0x47, 0x10, 0x80,
	0x04, 0x12, 0x18, 0x0a, 0x13, 0x4e, 0x50, 0x43, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x44, 0x49, 0x53, 0x50, 0x4c, 0x41, 0x59, 0x10, 0x80, 0x08, 0x12, 0x15, 0x0a, 0x10, 0x4e,
	0x50, 0x43, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x48, 0x49, 0x44, 0x45, 0x10,
	0x80, 0x10, 0x12, 0x16, 0x0a, 0x11, 0x4e, 0x50, 0x43, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52,
	0x59, 0x5f, 0x42, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x80, 0x20, 0x12, 0x14, 0x0a, 0x0f, 0x4d, 0x41,
	0x49, 0x4c, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x43, 0x45, 0x10, 0x80, 0x40,
	0x12, 0x15, 0x0a, 0x0f, 0x41, 0x54, 0x54, 0x52, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x4e, 0x4f, 0x54,
	0x49, 0x43, 0x45, 0x10, 0x80, 0x80, 0x01, 0x12, 0x15, 0x0a, 0x0f, 0x43, 0x48, 0x41, 0x54, 0x5f,
	0x4e, 0x45, 0x57, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x43, 0x45, 0x10, 0x80, 0x80, 0x02, 0x12, 0x18,
	0x0a, 0x12, 0x53, 0x50, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x4e, 0x50, 0x43, 0x10, 0x80, 0x80, 0x04, 0x12, 0x1b, 0x0a, 0x15, 0x53, 0x45, 0x4c, 0x4c,
	0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x4c, 0x4f, 0x41,
	0x44, 0x10, 0x80, 0x80, 0x08, 0x12, 0x1c, 0x0a, 0x16, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x50, 0x4c,
	0x41, 0x59, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x10,
	0x80, 0x80, 0x10, 0x12, 0x11, 0x0a, 0x0b, 0x48, 0x49, 0x44, 0x45, 0x5f, 0x53, 0x50, 0x49, 0x52,
	0x54, 0x45, 0x10, 0x80, 0x80, 0x20, 0x12, 0x18, 0x0a, 0x12, 0x48, 0x49, 0x44, 0x45, 0x5f, 0x50,
	0x48, 0x4f, 0x54, 0x4f, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x41, 0x4e, 0x44, 0x10, 0x80, 0x80, 0x40,
	0x12, 0x21, 0x0a, 0x1a, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f,
	0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x56, 0x49, 0x50, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x80,
	0x80, 0x80, 0x01, 0x12, 0x1b, 0x0a, 0x14, 0x42, 0x55, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x44, 0x49,
	0x45, 0x5f, 0x31, 0x48, 0x50, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x80, 0x80, 0x80, 0x40,
	0x12, 0x1f, 0x0a, 0x17, 0x42, 0x55, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x44, 0x49, 0x45, 0x5f, 0x46,
	0x55, 0x4c, 0x4c, 0x48, 0x50, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x80, 0x80, 0x80, 0x80,
	0x01, 0x12, 0x1e, 0x0a, 0x16, 0x42, 0x55, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x44, 0x49, 0x45, 0x5f,
	0x44, 0x45, 0x4c, 0x41, 0x59, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10, 0x80, 0x80, 0x80, 0x80,
	0x02, 0x12, 0x17, 0x0a, 0x0f, 0x54, 0x41, 0x47, 0x5f, 0x49, 0x53, 0x5f, 0x4b, 0x45, 0x45, 0x50,
	0x5f, 0x4f, 0x55, 0x54, 0x10, 0x80, 0x80, 0x80, 0x80, 0x04, 0x12, 0x1a, 0x0a, 0x0d, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x59, 0x10, 0x80, 0x80, 0x80, 0x80,
	0xf8, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x44, 0x45, 0x4c, 0x10, 0x02, 0x12, 0x13,
	0x0a, 0x0f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x5f, 0x44, 0x45,
	0x4c, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x52,
	0x4f, 0x53, 0x54, 0x10, 0x08, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x53, 0x45, 0x4c, 0x4c, 0x10, 0x10, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x45, 0x4e, 0x47, 0x41, 0x47, 0x45, 0x10, 0x20, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0x40, 0x12, 0x11, 0x0a, 0x0c, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4d, 0x4f, 0x55, 0x54, 0x48, 0x10, 0x80, 0x01, 0x12, 0x14,
	0x0a, 0x0f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e, 0x54,
	0x53, 0x10, 0x80, 0x02, 0x12, 0x1a, 0x0a, 0x15, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f,
	0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x80, 0x04,
	0x12, 0x0f, 0x0a, 0x0a, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x49, 0x50, 0x10, 0x80,
	0x08, 0x12, 0x13, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x53, 0x5f, 0x43,
	0x41, 0x52, 0x44, 0x10, 0x80, 0x10, 0x12, 0x16, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x43, 0x41, 0x4e, 0x5f, 0x4d, 0x41, 0x53, 0x54, 0x45, 0x52, 0x10, 0x80, 0x20, 0x12, 0x0f,
	0x0a, 0x0a, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x45, 0x57, 0x10, 0x80, 0x40, 0x12,
	0x12, 0x0a, 0x0e, 0x50, 0x48, 0x4f, 0x54, 0x4f, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x56, 0x49,
	0x50, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x48, 0x4f, 0x54, 0x4f, 0x5f, 0x49, 0x53, 0x5f,
	0x56, 0x49, 0x50, 0x5f, 0x43, 0x51, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x52, 0x59, 0x5f, 0x46, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x49, 0x53, 0x5f, 0x56, 0x49, 0x50,
	0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x46, 0x46,
	0x4c, 0x41, 0x47, 0x5f, 0x49, 0x53, 0x5f, 0x56, 0x49, 0x50, 0x5f, 0x43, 0x51, 0x10, 0x02, 0x12,
	0x19, 0x0a, 0x15, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4d, 0x45, 0x4d, 0x42, 0x45,
	0x52, 0x5f, 0x49, 0x53, 0x5f, 0x56, 0x49, 0x50, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x4d, 0x45, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x49, 0x53, 0x5f,
	0x56, 0x49, 0x50, 0x5f, 0x43, 0x51, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x49, 0x53, 0x5f, 0x53, 0x4f, 0x4c, 0x44, 0x49, 0x45, 0x52, 0x10, 0x80, 0x40,
	0x12, 0x14, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x53, 0x5f, 0x48, 0x45,
	0x4c, 0x50, 0x10, 0x80, 0x80, 0x01, 0x12, 0x15, 0x0a, 0x0f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x49, 0x53, 0x5f, 0x50, 0x48, 0x4f, 0x54, 0x4f, 0x10, 0x80, 0x80, 0x02, 0x12, 0x12, 0x0a,
	0x0c, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x53, 0x5f, 0x47, 0x4d, 0x10, 0x80, 0x80,
	0x04, 0x12, 0x14, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x53, 0x5f, 0x43,
	0x49, 0x54, 0x59, 0x10, 0x80, 0x80, 0x08, 0x12, 0x17, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x49, 0x53, 0x5f, 0x54, 0x45, 0x41, 0x43, 0x48, 0x45, 0x52, 0x10, 0x80, 0x80, 0x10,
	0x12, 0x16, 0x0a, 0x10, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x49, 0x50, 0x5f, 0x54,
	0x58, 0x5f, 0x43, 0x51, 0x10, 0x80, 0x80, 0x20, 0x12, 0x17, 0x0a, 0x11, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x49, 0x53, 0x5f, 0x54, 0x4f, 0x55, 0x52, 0x49, 0x53, 0x54, 0x10, 0x80, 0x80,
	0x40, 0x12, 0x16, 0x0a, 0x0f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x53, 0x5f, 0x52,
	0x4f, 0x42, 0x4f, 0x54, 0x10, 0x80, 0x80, 0x80, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x4f, 0x44,
	0x45, 0x5f, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4f,
	0x44, 0x45, 0x5f, 0x53, 0x48, 0x4f, 0x50, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x4d, 0x4f, 0x44,
	0x45, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x4c, 0x10, 0x32,
	0x12, 0x16, 0x0a, 0x12, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f,
	0x52, 0x45, 0x4d, 0x4f, 0x54, 0x45, 0x10, 0x33, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x4f, 0x44, 0x45,
	0x5f, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x50, 0x4b, 0x10, 0x34, 0x12, 0x12, 0x0a, 0x0e,
	0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x4f, 0x42, 0x10, 0x35,
	0x12, 0x16, 0x0a, 0x12, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f,
	0x50, 0x45, 0x54, 0x5f, 0x50, 0x4b, 0x10, 0x36, 0x12, 0x07, 0x0a, 0x03, 0x4e, 0x50, 0x43, 0x10,
	0x01, 0x12, 0x11, 0x0a, 0x0d, 0x42, 0x41, 0x54, 0x54, 0x4c, 0x45, 0x5f, 0x50, 0x4c, 0x41, 0x59,
	0x45, 0x52, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x41, 0x58, 0x5f, 0x53, 0x54, 0x4f, 0x52,
	0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x10, 0x3c, 0x12, 0x14, 0x0a, 0x0d, 0x4d, 0x41, 0x58, 0x5f, 0x50,
	0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x48, 0x50, 0x10, 0xff, 0xac, 0xe2, 0x04, 0x12, 0x14, 0x0a,
	0x0d, 0x4d, 0x41, 0x58, 0x5f, 0x50, 0x4c, 0x41, 0x59, 0x45, 0x52, 0x5f, 0x4d, 0x50, 0x10, 0xff,
	0xac, 0xe2, 0x04, 0x1a, 0x02, 0x10, 0x01, 0x42, 0x2b, 0x5a, 0x29, 0x77, 0x6f, 0x72, 0x6c, 0x64,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x3b, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbBase_ModelConst_ModelConst_proto_rawDescOnce sync.Once
	file_pbBase_ModelConst_ModelConst_proto_rawDescData = file_pbBase_ModelConst_ModelConst_proto_rawDesc
)

func file_pbBase_ModelConst_ModelConst_proto_rawDescGZIP() []byte {
	file_pbBase_ModelConst_ModelConst_proto_rawDescOnce.Do(func() {
		file_pbBase_ModelConst_ModelConst_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbBase_ModelConst_ModelConst_proto_rawDescData)
	})
	return file_pbBase_ModelConst_ModelConst_proto_rawDescData
}

var file_pbBase_ModelConst_ModelConst_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbBase_ModelConst_ModelConst_proto_goTypes = []interface{}{
	(Type)(0), // 0: proto.ModelConst.Type
}
var file_pbBase_ModelConst_ModelConst_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbBase_ModelConst_ModelConst_proto_init() }
func file_pbBase_ModelConst_ModelConst_proto_init() {
	if File_pbBase_ModelConst_ModelConst_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbBase_ModelConst_ModelConst_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbBase_ModelConst_ModelConst_proto_goTypes,
		DependencyIndexes: file_pbBase_ModelConst_ModelConst_proto_depIdxs,
		EnumInfos:         file_pbBase_ModelConst_ModelConst_proto_enumTypes,
	}.Build()
	File_pbBase_ModelConst_ModelConst_proto = out.File
	file_pbBase_ModelConst_ModelConst_proto_rawDesc = nil
	file_pbBase_ModelConst_ModelConst_proto_goTypes = nil
	file_pbBase_ModelConst_ModelConst_proto_depIdxs = nil
}
