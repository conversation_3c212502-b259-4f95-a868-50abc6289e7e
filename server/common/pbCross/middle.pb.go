// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbCross/middle.proto

package pbCross

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_pbCross_middle_proto protoreflect.FileDescriptor

var file_pbCross_middle_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x62, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x2f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x42, 0x1e, 0x5a,
	0x1c, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62,
	0x43, 0x72, 0x6f, 0x73, 0x73, 0x3b, 0x70, 0x62, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_pbCross_middle_proto_goTypes = []interface{}{}
var file_pbCross_middle_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbCross_middle_proto_init() }
func file_pbCross_middle_proto_init() {
	if File_pbCross_middle_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbCross_middle_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbCross_middle_proto_goTypes,
		DependencyIndexes: file_pbCross_middle_proto_depIdxs,
	}.Build()
	File_pbCross_middle_proto = out.File
	file_pbCross_middle_proto_rawDesc = nil
	file_pbCross_middle_proto_goTypes = nil
	file_pbCross_middle_proto_depIdxs = nil
}
