// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbCross/struct.proto

package pbCross

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are structs.
// 跨节点简要传递玩家数据
type CrossSimplePlayer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	From        string          `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`                //发送方，即玩家当前所在节点
	Id          string          `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`                    //玩家用户id
	GameId      int32           `protobuf:"varint,3,opt,name=gameId,proto3" json:"gameId,omitempty"`           //玩家游戏id
	Name        string          `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                //玩家名称
	MapId       int32           `protobuf:"varint,5,opt,name=mapId,proto3" json:"mapId,omitempty"`             //所处地图id
	X           int32           `protobuf:"varint,6,opt,name=x,proto3" json:"x,omitempty"`                     //所处地图x
	Y           int32           `protobuf:"varint,7,opt,name=y,proto3" json:"y,omitempty"`                     //所处地图y
	Icon1       int64           `protobuf:"varint,8,opt,name=icon1,proto3" json:"icon1,omitempty"`             //icon1
	Icon2       int64           `protobuf:"varint,9,opt,name=icon2,proto3" json:"icon2,omitempty"`             //icon2
	Icon3       int64           `protobuf:"varint,10,opt,name=icon3,proto3" json:"icon3,omitempty"`            //icon3
	Level       int32           `protobuf:"varint,11,opt,name=level,proto3" json:"level,omitempty"`            //等级
	Level2      int32           `protobuf:"varint,12,opt,name=level2,proto3" json:"level2,omitempty"`          //传奇等级
	Title       string          `protobuf:"bytes,13,opt,name=title,proto3" json:"title,omitempty"`             //称号
	Setting     int64           `protobuf:"varint,14,opt,name=setting,proto3" json:"setting,omitempty"`        //设置
	Status      int64           `protobuf:"varint,15,opt,name=status,proto3" json:"status,omitempty"`          //状态
	Mode        int32           `protobuf:"varint,16,opt,name=mode,proto3" json:"mode,omitempty"`              //设置
	ShopName    string          `protobuf:"bytes,17,opt,name=shopName,proto3" json:"shopName,omitempty"`       //摆摊名称
	CountryName string          `protobuf:"bytes,18,opt,name=countryName,proto3" json:"countryName,omitempty"` //国家名称
	VipLv       int32           `protobuf:"varint,19,opt,name=vipLv,proto3" json:"vipLv,omitempty"`            //vip等级
	VipLvMax    int32           `protobuf:"varint,20,opt,name=vipLvMax,proto3" json:"vipLvMax,omitempty"`      //历史最高vip等级
	Pet         *CrossSimplePet `protobuf:"bytes,21,opt,name=pet,proto3" json:"pet,omitempty"`                 //宠物数据
}

func (x *CrossSimplePlayer) Reset() {
	*x = CrossSimplePlayer{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_struct_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrossSimplePlayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrossSimplePlayer) ProtoMessage() {}

func (x *CrossSimplePlayer) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_struct_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrossSimplePlayer.ProtoReflect.Descriptor instead.
func (*CrossSimplePlayer) Descriptor() ([]byte, []int) {
	return file_pbCross_struct_proto_rawDescGZIP(), []int{0}
}

func (x *CrossSimplePlayer) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *CrossSimplePlayer) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CrossSimplePlayer) GetGameId() int32 {
	if x != nil {
		return x.GameId
	}
	return 0
}

func (x *CrossSimplePlayer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CrossSimplePlayer) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *CrossSimplePlayer) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *CrossSimplePlayer) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *CrossSimplePlayer) GetIcon1() int64 {
	if x != nil {
		return x.Icon1
	}
	return 0
}

func (x *CrossSimplePlayer) GetIcon2() int64 {
	if x != nil {
		return x.Icon2
	}
	return 0
}

func (x *CrossSimplePlayer) GetIcon3() int64 {
	if x != nil {
		return x.Icon3
	}
	return 0
}

func (x *CrossSimplePlayer) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *CrossSimplePlayer) GetLevel2() int32 {
	if x != nil {
		return x.Level2
	}
	return 0
}

func (x *CrossSimplePlayer) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CrossSimplePlayer) GetSetting() int64 {
	if x != nil {
		return x.Setting
	}
	return 0
}

func (x *CrossSimplePlayer) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *CrossSimplePlayer) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *CrossSimplePlayer) GetShopName() string {
	if x != nil {
		return x.ShopName
	}
	return ""
}

func (x *CrossSimplePlayer) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *CrossSimplePlayer) GetVipLv() int32 {
	if x != nil {
		return x.VipLv
	}
	return 0
}

func (x *CrossSimplePlayer) GetVipLvMax() int32 {
	if x != nil {
		return x.VipLvMax
	}
	return 0
}

func (x *CrossSimplePlayer) GetPet() *CrossSimplePet {
	if x != nil {
		return x.Pet
	}
	return nil
}

// 跨节点简要传递宠物数据
type CrossSimplePet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CfgId int32  `protobuf:"varint,1,opt,name=cfgId,proto3" json:"cfgId,omitempty"` //宠物配置id
	Id    int64  `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`       //宠物id
	Name  string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`    //自定义名字，可能存在
	Age   int64  `protobuf:"varint,4,opt,name=age,proto3" json:"age,omitempty"`     //寿命
}

func (x *CrossSimplePet) Reset() {
	*x = CrossSimplePet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_struct_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CrossSimplePet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrossSimplePet) ProtoMessage() {}

func (x *CrossSimplePet) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_struct_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrossSimplePet.ProtoReflect.Descriptor instead.
func (*CrossSimplePet) Descriptor() ([]byte, []int) {
	return file_pbCross_struct_proto_rawDescGZIP(), []int{1}
}

func (x *CrossSimplePet) GetCfgId() int32 {
	if x != nil {
		return x.CfgId
	}
	return 0
}

func (x *CrossSimplePet) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CrossSimplePet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CrossSimplePet) GetAge() int64 {
	if x != nil {
		return x.Age
	}
	return 0
}

// 玩家事件数据
type PlayerEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventId    int32              `protobuf:"varint,1,opt,name=eventId,proto3" json:"eventId,omitempty"`       //事件id
	EventType  int32              `protobuf:"varint,2,opt,name=eventType,proto3" json:"eventType,omitempty"`   //事件类型
	Message    string             `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`        //事件消息
	ExtraInfo  string             `protobuf:"bytes,4,opt,name=extraInfo,proto3" json:"extraInfo,omitempty"`    //额外信息
	ExpireTime int64              `protobuf:"varint,5,opt,name=expireTime,proto3" json:"expireTime,omitempty"` //过期时间
	Player     *CrossSimplePlayer `protobuf:"bytes,6,opt,name=player,proto3" json:"player,omitempty"`          //发起者
}

func (x *PlayerEvent) Reset() {
	*x = PlayerEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbCross_struct_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerEvent) ProtoMessage() {}

func (x *PlayerEvent) ProtoReflect() protoreflect.Message {
	mi := &file_pbCross_struct_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerEvent.ProtoReflect.Descriptor instead.
func (*PlayerEvent) Descriptor() ([]byte, []int) {
	return file_pbCross_struct_proto_rawDescGZIP(), []int{2}
}

func (x *PlayerEvent) GetEventId() int32 {
	if x != nil {
		return x.EventId
	}
	return 0
}

func (x *PlayerEvent) GetEventType() int32 {
	if x != nil {
		return x.EventType
	}
	return 0
}

func (x *PlayerEvent) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PlayerEvent) GetExtraInfo() string {
	if x != nil {
		return x.ExtraInfo
	}
	return ""
}

func (x *PlayerEvent) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *PlayerEvent) GetPlayer() *CrossSimplePlayer {
	if x != nil {
		return x.Player
	}
	return nil
}

var File_pbCross_struct_proto protoreflect.FileDescriptor

var file_pbCross_struct_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x62, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfa, 0x03,
	0x0a, 0x11, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x67, 0x61, 0x6d, 0x65, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x31, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x31, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x63, 0x6f, 0x6e, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x63, 0x6f, 0x6e,
	0x32, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x33, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x33, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x16, 0x0a,
	0x06, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x6f, 0x64,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x68, 0x6f, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x68, 0x6f, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x69, 0x70, 0x4c, 0x76, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x76, 0x69, 0x70, 0x4c, 0x76, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x69, 0x70, 0x4c, 0x76, 0x4d, 0x61,
	0x78, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x76, 0x69, 0x70, 0x4c, 0x76, 0x4d, 0x61,
	0x78, 0x12, 0x27, 0x0a, 0x03, 0x70, 0x65, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70,
	0x6c, 0x65, 0x50, 0x65, 0x74, 0x52, 0x03, 0x70, 0x65, 0x74, 0x22, 0x5c, 0x0a, 0x0e, 0x43, 0x72,
	0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x66, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x63, 0x66, 0x67,
	0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x61, 0x67, 0x65, 0x22, 0xcf, 0x01, 0x0a, 0x0b, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x1e, 0x5a, 0x1c, 0x77, 0x6f,
	0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x43, 0x72, 0x6f,
	0x73, 0x73, 0x3b, 0x70, 0x62, 0x43, 0x72, 0x6f, 0x73, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_pbCross_struct_proto_rawDescOnce sync.Once
	file_pbCross_struct_proto_rawDescData = file_pbCross_struct_proto_rawDesc
)

func file_pbCross_struct_proto_rawDescGZIP() []byte {
	file_pbCross_struct_proto_rawDescOnce.Do(func() {
		file_pbCross_struct_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbCross_struct_proto_rawDescData)
	})
	return file_pbCross_struct_proto_rawDescData
}

var file_pbCross_struct_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_pbCross_struct_proto_goTypes = []interface{}{
	(*CrossSimplePlayer)(nil), // 0: proto.CrossSimplePlayer
	(*CrossSimplePet)(nil),    // 1: proto.CrossSimplePet
	(*PlayerEvent)(nil),       // 2: proto.PlayerEvent
}
var file_pbCross_struct_proto_depIdxs = []int32{
	1, // 0: proto.CrossSimplePlayer.pet:type_name -> proto.CrossSimplePet
	0, // 1: proto.PlayerEvent.player:type_name -> proto.CrossSimplePlayer
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pbCross_struct_proto_init() }
func file_pbCross_struct_proto_init() {
	if File_pbCross_struct_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbCross_struct_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrossSimplePlayer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_struct_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CrossSimplePet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbCross_struct_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbCross_struct_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbCross_struct_proto_goTypes,
		DependencyIndexes: file_pbCross_struct_proto_depIdxs,
		MessageInfos:      file_pbCross_struct_proto_msgTypes,
	}.Build()
	File_pbCross_struct_proto = out.File
	file_pbCross_struct_proto_rawDesc = nil
	file_pbCross_struct_proto_goTypes = nil
	file_pbCross_struct_proto_depIdxs = nil
}
