// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbLogin/struct.proto

package pbLogin

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	AreaLineState "world/common/pbBase/AreaLineState"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are structs.
// 登录结果信息
type LoginResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`       //用户id
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"` //会话凭证
}

func (x *LoginResult) Reset() {
	*x = LoginResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_struct_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginResult) ProtoMessage() {}

func (x *LoginResult) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_struct_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginResult.ProtoReflect.Descriptor instead.
func (*LoginResult) Descriptor() ([]byte, []int) {
	return file_pbLogin_struct_proto_rawDescGZIP(), []int{0}
}

func (x *LoginResult) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LoginResult) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 区服信息
type AreaLine struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         int32              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                     //区服id
	Name       string             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                  //区服名称
	OpenTime   int32              `protobuf:"varint,3,opt,name=openTime,proto3" json:"openTime,omitempty"`                         //区服名称
	State      AreaLineState.Type `protobuf:"varint,4,opt,name=state,proto3,enum=proto.AreaLineState.Type" json:"state,omitempty"` //区服状态
	ActorCount int32              `protobuf:"varint,5,opt,name=actorCount,proto3" json:"actorCount,omitempty"`                     //拥有角色数量
}

func (x *AreaLine) Reset() {
	*x = AreaLine{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbLogin_struct_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AreaLine) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AreaLine) ProtoMessage() {}

func (x *AreaLine) ProtoReflect() protoreflect.Message {
	mi := &file_pbLogin_struct_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AreaLine.ProtoReflect.Descriptor instead.
func (*AreaLine) Descriptor() ([]byte, []int) {
	return file_pbLogin_struct_proto_rawDescGZIP(), []int{1}
}

func (x *AreaLine) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AreaLine) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AreaLine) GetOpenTime() int32 {
	if x != nil {
		return x.OpenTime
	}
	return 0
}

func (x *AreaLine) GetState() AreaLineState.Type {
	if x != nil {
		return x.State
	}
	return AreaLineState.Type(0)
}

func (x *AreaLine) GetActorCount() int32 {
	if x != nil {
		return x.ActorCount
	}
	return 0
}

var File_pbLogin_struct_proto protoreflect.FileDescriptor

var file_pbLogin_struct_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x62, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x70,
	0x62, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x2f, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x33, 0x0a, 0x0b, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x9b, 0x01, 0x0a,
	0x08, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x6f, 0x70, 0x65, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x6f, 0x70, 0x65, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x41, 0x72, 0x65, 0x61, 0x4c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x1e, 0x5a, 0x1c, 0x77, 0x6f,
	0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x3b, 0x70, 0x62, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_pbLogin_struct_proto_rawDescOnce sync.Once
	file_pbLogin_struct_proto_rawDescData = file_pbLogin_struct_proto_rawDesc
)

func file_pbLogin_struct_proto_rawDescGZIP() []byte {
	file_pbLogin_struct_proto_rawDescOnce.Do(func() {
		file_pbLogin_struct_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbLogin_struct_proto_rawDescData)
	})
	return file_pbLogin_struct_proto_rawDescData
}

var file_pbLogin_struct_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_pbLogin_struct_proto_goTypes = []interface{}{
	(*LoginResult)(nil),     // 0: proto.LoginResult
	(*AreaLine)(nil),        // 1: proto.AreaLine
	(AreaLineState.Type)(0), // 2: proto.AreaLineState.Type
}
var file_pbLogin_struct_proto_depIdxs = []int32{
	2, // 0: proto.AreaLine.state:type_name -> proto.AreaLineState.Type
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pbLogin_struct_proto_init() }
func file_pbLogin_struct_proto_init() {
	if File_pbLogin_struct_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbLogin_struct_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbLogin_struct_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AreaLine); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbLogin_struct_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbLogin_struct_proto_goTypes,
		DependencyIndexes: file_pbLogin_struct_proto_depIdxs,
		MessageInfos:      file_pbLogin_struct_proto_msgTypes,
	}.Build()
	File_pbLogin_struct_proto = out.File
	file_pbLogin_struct_proto_rawDesc = nil
	file_pbLogin_struct_proto_goTypes = nil
	file_pbLogin_struct_proto_depIdxs = nil
}
