// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbGame/ITEM_GRADE/ItemGrade.proto

package ITEM_GRADE

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 物品grade定义 品质
type Type int32

const (
	Grade_0 Type = 0 //普通
	Grade_1 Type = 1 //精致
	Grade_2 Type = 2 //稀有
	Grade_3 Type = 3 //史诗
	Grade_4 Type = 4 //传说
	Grade_5 Type = 5 //神话
)

// Enum value maps for Type.
var (
	Type_name = map[int32]string{
		0: "Grade_0",
		1: "Grade_1",
		2: "Grade_2",
		3: "Grade_3",
		4: "Grade_4",
		5: "Grade_5",
	}
	Type_value = map[string]int32{
		"Grade_0": 0,
		"Grade_1": 1,
		"Grade_2": 2,
		"Grade_3": 3,
		"Grade_4": 4,
		"Grade_5": 5,
	}
)

func (x Type) Enum() *Type {
	p := new(Type)
	*p = x
	return p
}

func (x Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_ITEM_GRADE_ItemGrade_proto_enumTypes[0].Descriptor()
}

func (Type) Type() protoreflect.EnumType {
	return &file_pbGame_ITEM_GRADE_ItemGrade_proto_enumTypes[0]
}

func (x Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type.Descriptor instead.
func (Type) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_ITEM_GRADE_ItemGrade_proto_rawDescGZIP(), []int{0}
}

var File_pbGame_ITEM_GRADE_ItemGrade_proto protoreflect.FileDescriptor

var file_pbGame_ITEM_GRADE_ItemGrade_proto_rawDesc = []byte{
	0x0a, 0x21, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x47, 0x52,
	0x41, 0x44, 0x45, 0x2f, 0x49, 0x74, 0x65, 0x6d, 0x47, 0x72, 0x61, 0x64, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x10, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x54, 0x45, 0x4d, 0x5f,
	0x47, 0x52, 0x41, 0x44, 0x45, 0x2a, 0x54, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a,
	0x07, 0x47, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x30, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x47, 0x72,
	0x61, 0x64, 0x65, 0x5f, 0x31, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x47, 0x72, 0x61, 0x64, 0x65,
	0x5f, 0x32, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x47, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x33, 0x10,
	0x03, 0x12, 0x0b, 0x0a, 0x07, 0x47, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x34, 0x10, 0x04, 0x12, 0x0b,
	0x0a, 0x07, 0x47, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x35, 0x10, 0x05, 0x42, 0x2b, 0x5a, 0x29, 0x77,
	0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61,
	0x6d, 0x65, 0x2f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x47, 0x52, 0x41, 0x44, 0x45, 0x3b, 0x49, 0x54,
	0x45, 0x4d, 0x5f, 0x47, 0x52, 0x41, 0x44, 0x45, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_ITEM_GRADE_ItemGrade_proto_rawDescOnce sync.Once
	file_pbGame_ITEM_GRADE_ItemGrade_proto_rawDescData = file_pbGame_ITEM_GRADE_ItemGrade_proto_rawDesc
)

func file_pbGame_ITEM_GRADE_ItemGrade_proto_rawDescGZIP() []byte {
	file_pbGame_ITEM_GRADE_ItemGrade_proto_rawDescOnce.Do(func() {
		file_pbGame_ITEM_GRADE_ItemGrade_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_ITEM_GRADE_ItemGrade_proto_rawDescData)
	})
	return file_pbGame_ITEM_GRADE_ItemGrade_proto_rawDescData
}

var file_pbGame_ITEM_GRADE_ItemGrade_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbGame_ITEM_GRADE_ItemGrade_proto_goTypes = []interface{}{
	(Type)(0), // 0: proto.ITEM_GRADE.Type
}
var file_pbGame_ITEM_GRADE_ItemGrade_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbGame_ITEM_GRADE_ItemGrade_proto_init() }
func file_pbGame_ITEM_GRADE_ItemGrade_proto_init() {
	if File_pbGame_ITEM_GRADE_ItemGrade_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_ITEM_GRADE_ItemGrade_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_ITEM_GRADE_ItemGrade_proto_goTypes,
		DependencyIndexes: file_pbGame_ITEM_GRADE_ItemGrade_proto_depIdxs,
		EnumInfos:         file_pbGame_ITEM_GRADE_ItemGrade_proto_enumTypes,
	}.Build()
	File_pbGame_ITEM_GRADE_ItemGrade_proto = out.File
	file_pbGame_ITEM_GRADE_ItemGrade_proto_rawDesc = nil
	file_pbGame_ITEM_GRADE_ItemGrade_proto_goTypes = nil
	file_pbGame_ITEM_GRADE_ItemGrade_proto_depIdxs = nil
}
