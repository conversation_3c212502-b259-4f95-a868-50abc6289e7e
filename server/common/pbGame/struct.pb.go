// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbGame/struct.proto

package pbGame

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	BattleDefine "world/common/pbGame/BattleDefine"
	ConditionType "world/common/pbGame/ConditionType"
	MyDefine "world/common/pbGame/MyDefine"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are structs.
// 简要玩家数据,用于拉取角色列表时使用
type SimplePlayerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int32            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                       //玩家id
	MapId         int32            `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`                 //当前所处地图
	X             int32            `protobuf:"varint,3,opt,name=x,proto3" json:"x,omitempty"`                         //所处地图x
	Y             int32            `protobuf:"varint,4,opt,name=y,proto3" json:"y,omitempty"`                         //所处地图y
	Name          string           `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                    //玩家名称
	Attr          *AttrData        `protobuf:"bytes,6,opt,name=attr,proto3" json:"attr,omitempty"`                    //属性
	DeleteEndTime int64            `protobuf:"varint,7,opt,name=deleteEndTime,proto3" json:"deleteEndTime,omitempty"` //删除计时
	OfflineTask   *OfflineTaskData `protobuf:"bytes,8,opt,name=offlineTask,proto3" json:"offlineTask,omitempty"`      //离线任务数据
}

func (x *SimplePlayerInfo) Reset() {
	*x = SimplePlayerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SimplePlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimplePlayerInfo) ProtoMessage() {}

func (x *SimplePlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimplePlayerInfo.ProtoReflect.Descriptor instead.
func (*SimplePlayerInfo) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{0}
}

func (x *SimplePlayerInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SimplePlayerInfo) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *SimplePlayerInfo) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *SimplePlayerInfo) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *SimplePlayerInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SimplePlayerInfo) GetAttr() *AttrData {
	if x != nil {
		return x.Attr
	}
	return nil
}

func (x *SimplePlayerInfo) GetDeleteEndTime() int64 {
	if x != nil {
		return x.DeleteEndTime
	}
	return 0
}

func (x *SimplePlayerInfo) GetOfflineTask() *OfflineTaskData {
	if x != nil {
		return x.OfflineTask
	}
	return nil
}

// 玩家数据,玩家进入游戏时推送
type PlayerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int32      `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                           //玩家id
	MapId       int32      `protobuf:"varint,2,opt,name=mapId,proto3" json:"mapId,omitempty"`                     //当前所处地图
	X           int32      `protobuf:"varint,3,opt,name=x,proto3" json:"x,omitempty"`                             //所处地图x
	Y           int32      `protobuf:"varint,4,opt,name=y,proto3" json:"y,omitempty"`                             //所处地图y
	Name        string     `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`                        //玩家名称
	Setting     int64      `protobuf:"varint,6,opt,name=setting,proto3" json:"setting,omitempty"`                 //设置
	Mode        int32      `protobuf:"varint,7,opt,name=mode,proto3" json:"mode,omitempty"`                       //设置
	Attr        *AttrData  `protobuf:"bytes,8,opt,name=attr,proto3" json:"attr,omitempty"`                        //属性
	Bag         *BagData   `protobuf:"bytes,9,opt,name=bag,proto3" json:"bag,omitempty"`                          //背包
	Task        *TaskData  `protobuf:"bytes,10,opt,name=task,proto3" json:"task,omitempty"`                       //任务
	ItemSetData []int32    `protobuf:"varint,11,rep,packed,name=itemSetData,proto3" json:"itemSetData,omitempty"` //玩家套装数据
	Skill       *SkillData `protobuf:"bytes,12,opt,name=skill,proto3" json:"skill,omitempty"`                     //技能
	PetId       int64      `protobuf:"varint,13,opt,name=petId,proto3" json:"petId,omitempty"`                    //上阵的宠物
}

func (x *PlayerInfo) Reset() {
	*x = PlayerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerInfo) ProtoMessage() {}

func (x *PlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerInfo.ProtoReflect.Descriptor instead.
func (*PlayerInfo) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{1}
}

func (x *PlayerInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PlayerInfo) GetMapId() int32 {
	if x != nil {
		return x.MapId
	}
	return 0
}

func (x *PlayerInfo) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *PlayerInfo) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *PlayerInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PlayerInfo) GetSetting() int64 {
	if x != nil {
		return x.Setting
	}
	return 0
}

func (x *PlayerInfo) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *PlayerInfo) GetAttr() *AttrData {
	if x != nil {
		return x.Attr
	}
	return nil
}

func (x *PlayerInfo) GetBag() *BagData {
	if x != nil {
		return x.Bag
	}
	return nil
}

func (x *PlayerInfo) GetTask() *TaskData {
	if x != nil {
		return x.Task
	}
	return nil
}

func (x *PlayerInfo) GetItemSetData() []int32 {
	if x != nil {
		return x.ItemSetData
	}
	return nil
}

func (x *PlayerInfo) GetSkill() *SkillData {
	if x != nil {
		return x.Skill
	}
	return nil
}

func (x *PlayerInfo) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

// 属性模块
type AttrData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Icon1    int64 `protobuf:"varint,1,opt,name=icon1,proto3" json:"icon1,omitempty"`        //icon1
	Icon2    int64 `protobuf:"varint,2,opt,name=icon2,proto3" json:"icon2,omitempty"`        //icon2
	Icon3    int64 `protobuf:"varint,3,opt,name=icon3,proto3" json:"icon3,omitempty"`        //icon3
	Status   int64 `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`      //状态
	Level    int32 `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`        //等级
	Level2   int32 `protobuf:"varint,6,opt,name=level2,proto3" json:"level2,omitempty"`      //传奇等级
	Exp      int32 `protobuf:"varint,7,opt,name=exp,proto3" json:"exp,omitempty"`            //普通经验
	Exp2     int32 `protobuf:"varint,8,opt,name=exp2,proto3" json:"exp2,omitempty"`          //传奇经验
	VipLv    int32 `protobuf:"varint,9,opt,name=vipLv,proto3" json:"vipLv,omitempty"`        //vip等级
	VipLvMax int32 `protobuf:"varint,10,opt,name=vipLvMax,proto3" json:"vipLvMax,omitempty"` //历史最高vip等级
	Cp       int32 `protobuf:"varint,11,opt,name=cp,proto3" json:"cp,omitempty"`             //未使用的技能点
	Str      int32 `protobuf:"varint,12,opt,name=str,proto3" json:"str,omitempty"`           //力量
	Agi      int32 `protobuf:"varint,13,opt,name=agi,proto3" json:"agi,omitempty"`           //敏捷
	Con      int32 `protobuf:"varint,14,opt,name=con,proto3" json:"con,omitempty"`           //体质
	Ilt      int32 `protobuf:"varint,15,opt,name=ilt,proto3" json:"ilt,omitempty"`           //智力
	Wis      int32 `protobuf:"varint,16,opt,name=wis,proto3" json:"wis,omitempty"`           //感知
	Hp       int32 `protobuf:"varint,17,opt,name=hp,proto3" json:"hp,omitempty"`             //血
	Mp       int32 `protobuf:"varint,18,opt,name=mp,proto3" json:"mp,omitempty"`             //蓝
}

func (x *AttrData) Reset() {
	*x = AttrData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttrData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttrData) ProtoMessage() {}

func (x *AttrData) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttrData.ProtoReflect.Descriptor instead.
func (*AttrData) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{2}
}

func (x *AttrData) GetIcon1() int64 {
	if x != nil {
		return x.Icon1
	}
	return 0
}

func (x *AttrData) GetIcon2() int64 {
	if x != nil {
		return x.Icon2
	}
	return 0
}

func (x *AttrData) GetIcon3() int64 {
	if x != nil {
		return x.Icon3
	}
	return 0
}

func (x *AttrData) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AttrData) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *AttrData) GetLevel2() int32 {
	if x != nil {
		return x.Level2
	}
	return 0
}

func (x *AttrData) GetExp() int32 {
	if x != nil {
		return x.Exp
	}
	return 0
}

func (x *AttrData) GetExp2() int32 {
	if x != nil {
		return x.Exp2
	}
	return 0
}

func (x *AttrData) GetVipLv() int32 {
	if x != nil {
		return x.VipLv
	}
	return 0
}

func (x *AttrData) GetVipLvMax() int32 {
	if x != nil {
		return x.VipLvMax
	}
	return 0
}

func (x *AttrData) GetCp() int32 {
	if x != nil {
		return x.Cp
	}
	return 0
}

func (x *AttrData) GetStr() int32 {
	if x != nil {
		return x.Str
	}
	return 0
}

func (x *AttrData) GetAgi() int32 {
	if x != nil {
		return x.Agi
	}
	return 0
}

func (x *AttrData) GetCon() int32 {
	if x != nil {
		return x.Con
	}
	return 0
}

func (x *AttrData) GetIlt() int32 {
	if x != nil {
		return x.Ilt
	}
	return 0
}

func (x *AttrData) GetWis() int32 {
	if x != nil {
		return x.Wis
	}
	return 0
}

func (x *AttrData) GetHp() int32 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *AttrData) GetMp() int32 {
	if x != nil {
		return x.Mp
	}
	return 0
}

// 背包数据
type BagData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Money1        int32               `protobuf:"varint,1,opt,name=money1,proto3" json:"money1,omitempty"`                                                                                       //黄金
	Money2        int32               `protobuf:"varint,2,opt,name=money2,proto3" json:"money2,omitempty"`                                                                                       //金叶
	Money3        int32               `protobuf:"varint,3,opt,name=money3,proto3" json:"money3,omitempty"`                                                                                       //铜币
	BagSize       int32               `protobuf:"varint,4,opt,name=bagSize,proto3" json:"bagSize,omitempty"`                                                                                     //背包格子数量
	SelfStoreSize int32               `protobuf:"varint,5,opt,name=selfStoreSize,proto3" json:"selfStoreSize,omitempty"`                                                                         //个人仓库购买数量
	Store         map[int32]*ItemData `protobuf:"bytes,6,rep,name=store,proto3" json:"store,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //物品数据
}

func (x *BagData) Reset() {
	*x = BagData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BagData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BagData) ProtoMessage() {}

func (x *BagData) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BagData.ProtoReflect.Descriptor instead.
func (*BagData) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{3}
}

func (x *BagData) GetMoney1() int32 {
	if x != nil {
		return x.Money1
	}
	return 0
}

func (x *BagData) GetMoney2() int32 {
	if x != nil {
		return x.Money2
	}
	return 0
}

func (x *BagData) GetMoney3() int32 {
	if x != nil {
		return x.Money3
	}
	return 0
}

func (x *BagData) GetBagSize() int32 {
	if x != nil {
		return x.BagSize
	}
	return 0
}

func (x *BagData) GetSelfStoreSize() int32 {
	if x != nil {
		return x.SelfStoreSize
	}
	return 0
}

func (x *BagData) GetStore() map[int32]*ItemData {
	if x != nil {
		return x.Store
	}
	return nil
}

// 物品数据
type PetData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CfgId     int32      `protobuf:"varint,1,opt,name=cfgId,proto3" json:"cfgId,omitempty"`         //宠物配置id
	Name      string     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`            //自定义名字，可能存在
	Grow      int32      `protobuf:"varint,3,opt,name=grow,proto3" json:"grow,omitempty"`           //成长值
	Learn     int32      `protobuf:"varint,4,opt,name=learn,proto3" json:"learn,omitempty"`         //领悟值
	GrowLevel int32      `protobuf:"varint,5,opt,name=growLevel,proto3" json:"growLevel,omitempty"` //成长等级
	Attr      *AttrData  `protobuf:"bytes,6,opt,name=attr,proto3" json:"attr,omitempty"`            //属性
	Skill     *SkillData `protobuf:"bytes,7,opt,name=skill,proto3" json:"skill,omitempty"`          //技能
	Age       int64      `protobuf:"varint,8,opt,name=age,proto3" json:"age,omitempty"`             //寿命
	GrowExp   int32      `protobuf:"varint,9,opt,name=growExp,proto3" json:"growExp,omitempty"`     //成长经验
	Id        int64      `protobuf:"varint,10,opt,name=id,proto3" json:"id,omitempty"`              //宠物id
}

func (x *PetData) Reset() {
	*x = PetData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PetData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetData) ProtoMessage() {}

func (x *PetData) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetData.ProtoReflect.Descriptor instead.
func (*PetData) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{4}
}

func (x *PetData) GetCfgId() int32 {
	if x != nil {
		return x.CfgId
	}
	return 0
}

func (x *PetData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PetData) GetGrow() int32 {
	if x != nil {
		return x.Grow
	}
	return 0
}

func (x *PetData) GetLearn() int32 {
	if x != nil {
		return x.Learn
	}
	return 0
}

func (x *PetData) GetGrowLevel() int32 {
	if x != nil {
		return x.GrowLevel
	}
	return 0
}

func (x *PetData) GetAttr() *AttrData {
	if x != nil {
		return x.Attr
	}
	return nil
}

func (x *PetData) GetSkill() *SkillData {
	if x != nil {
		return x.Skill
	}
	return nil
}

func (x *PetData) GetAge() int64 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *PetData) GetGrowExp() int32 {
	if x != nil {
		return x.GrowExp
	}
	return 0
}

func (x *PetData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 物品数据
type ItemData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int32             `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                               //id
	SlotPos       int32             `protobuf:"varint,2,opt,name=slotPos,proto3" json:"slotPos,omitempty"`                                                                                     //位置
	Quantity      int32             `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`                                                                                   //数量
	Status        int32             `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`                                                                                       //状态
	Power1        *PowerData        `protobuf:"bytes,5,opt,name=power1,proto3" json:"power1,omitempty"`                                                                                        //基础属性1
	Power2        *PowerData        `protobuf:"bytes,6,opt,name=power2,proto3" json:"power2,omitempty"`                                                                                        //基础属性2
	Power3        *PowerData        `protobuf:"bytes,7,opt,name=power3,proto3" json:"power3,omitempty"`                                                                                        //基础属性3
	BindPower1    *PowerData        `protobuf:"bytes,8,opt,name=bindPower1,proto3" json:"bindPower1,omitempty"`                                                                                //绑定属性1
	BindPower2    *PowerData        `protobuf:"bytes,9,opt,name=bindPower2,proto3" json:"bindPower2,omitempty"`                                                                                //绑定属性2
	Power4        *PowerData        `protobuf:"bytes,10,opt,name=power4,proto3" json:"power4,omitempty"`                                                                                       //进阶属性1
	Power5        *PowerData        `protobuf:"bytes,11,opt,name=power5,proto3" json:"power5,omitempty"`                                                                                       //进阶属性2
	Power6        *PowerData        `protobuf:"bytes,12,opt,name=power6,proto3" json:"power6,omitempty"`                                                                                       //进阶属性3
	Power7        *PowerData        `protobuf:"bytes,13,opt,name=power7,proto3" json:"power7,omitempty"`                                                                                       //进阶属性4
	EnchantPower1 *PowerData        `protobuf:"bytes,14,opt,name=enchantPower1,proto3" json:"enchantPower1,omitempty"`                                                                         //附魔1
	EnchantPower2 *PowerData        `protobuf:"bytes,15,opt,name=enchantPower2,proto3" json:"enchantPower2,omitempty"`                                                                         //附魔2
	Durability    int32             `protobuf:"varint,16,opt,name=durability,proto3" json:"durability,omitempty"`                                                                              //耐久
	AttachDone    int32             `protobuf:"varint,17,opt,name=attachDone,proto3" json:"attachDone,omitempty"`                                                                              //宝石镶嵌数量
	AttachPower   *PowerData        `protobuf:"bytes,18,opt,name=attachPower,proto3" json:"attachPower,omitempty"`                                                                             //宝石属性
	ExpireTime    int64             `protobuf:"varint,19,opt,name=expireTime,proto3" json:"expireTime,omitempty"`                                                                              //过期时间
	Star          int32             `protobuf:"varint,20,opt,name=star,proto3" json:"star,omitempty"`                                                                                          //普通升星数量
	UpgradeStar   int32             `protobuf:"varint,21,opt,name=upgradeStar,proto3" json:"upgradeStar,omitempty"`                                                                            //进阶升星数量
	PetId         int64             `protobuf:"varint,22,opt,name=petId,proto3" json:"petId,omitempty"`                                                                                        //宠物唯一id
	PetItem       *PetData          `protobuf:"bytes,23,opt,name=petItem,proto3" json:"petItem,omitempty"`                                                                                     //宠物数据
	Extra         map[string]string `protobuf:"bytes,24,rep,name=extra,proto3" json:"extra,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //扩展信息
}

func (x *ItemData) Reset() {
	*x = ItemData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemData) ProtoMessage() {}

func (x *ItemData) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemData.ProtoReflect.Descriptor instead.
func (*ItemData) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{5}
}

func (x *ItemData) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ItemData) GetSlotPos() int32 {
	if x != nil {
		return x.SlotPos
	}
	return 0
}

func (x *ItemData) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *ItemData) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ItemData) GetPower1() *PowerData {
	if x != nil {
		return x.Power1
	}
	return nil
}

func (x *ItemData) GetPower2() *PowerData {
	if x != nil {
		return x.Power2
	}
	return nil
}

func (x *ItemData) GetPower3() *PowerData {
	if x != nil {
		return x.Power3
	}
	return nil
}

func (x *ItemData) GetBindPower1() *PowerData {
	if x != nil {
		return x.BindPower1
	}
	return nil
}

func (x *ItemData) GetBindPower2() *PowerData {
	if x != nil {
		return x.BindPower2
	}
	return nil
}

func (x *ItemData) GetPower4() *PowerData {
	if x != nil {
		return x.Power4
	}
	return nil
}

func (x *ItemData) GetPower5() *PowerData {
	if x != nil {
		return x.Power5
	}
	return nil
}

func (x *ItemData) GetPower6() *PowerData {
	if x != nil {
		return x.Power6
	}
	return nil
}

func (x *ItemData) GetPower7() *PowerData {
	if x != nil {
		return x.Power7
	}
	return nil
}

func (x *ItemData) GetEnchantPower1() *PowerData {
	if x != nil {
		return x.EnchantPower1
	}
	return nil
}

func (x *ItemData) GetEnchantPower2() *PowerData {
	if x != nil {
		return x.EnchantPower2
	}
	return nil
}

func (x *ItemData) GetDurability() int32 {
	if x != nil {
		return x.Durability
	}
	return 0
}

func (x *ItemData) GetAttachDone() int32 {
	if x != nil {
		return x.AttachDone
	}
	return 0
}

func (x *ItemData) GetAttachPower() *PowerData {
	if x != nil {
		return x.AttachPower
	}
	return nil
}

func (x *ItemData) GetExpireTime() int64 {
	if x != nil {
		return x.ExpireTime
	}
	return 0
}

func (x *ItemData) GetStar() int32 {
	if x != nil {
		return x.Star
	}
	return 0
}

func (x *ItemData) GetUpgradeStar() int32 {
	if x != nil {
		return x.UpgradeStar
	}
	return 0
}

func (x *ItemData) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *ItemData) GetPetItem() *PetData {
	if x != nil {
		return x.PetItem
	}
	return nil
}

func (x *ItemData) GetExtra() map[string]string {
	if x != nil {
		return x.Extra
	}
	return nil
}

// 条件数据
type Condition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  ConditionType.Type `protobuf:"varint,1,opt,name=type,proto3,enum=proto.ConditionType.Type" json:"type,omitempty"` //类型
	Id    int32              `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                                   //id
	Num   int32              `protobuf:"varint,3,opt,name=num,proto3" json:"num,omitempty"`                                 //需求数量
	Extra string             `protobuf:"bytes,4,opt,name=extra,proto3" json:"extra,omitempty"`                              //扩展数据
}

func (x *Condition) Reset() {
	*x = Condition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Condition) ProtoMessage() {}

func (x *Condition) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Condition.ProtoReflect.Descriptor instead.
func (*Condition) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{6}
}

func (x *Condition) GetType() ConditionType.Type {
	if x != nil {
		return x.Type
	}
	return ConditionType.Type(0)
}

func (x *Condition) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Condition) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *Condition) GetExtra() string {
	if x != nil {
		return x.Extra
	}
	return ""
}

// 任务模块数据
type TaskData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskStatus []byte      `protobuf:"bytes,1,opt,name=taskStatus,proto3" json:"taskStatus,omitempty"` //物品数据
	Tasks      []*TaskInfo `protobuf:"bytes,2,rep,name=tasks,proto3" json:"tasks,omitempty"`           //物品数据
}

func (x *TaskData) Reset() {
	*x = TaskData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskData) ProtoMessage() {}

func (x *TaskData) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskData.ProtoReflect.Descriptor instead.
func (*TaskData) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{7}
}

func (x *TaskData) GetTaskStatus() []byte {
	if x != nil {
		return x.TaskStatus
	}
	return nil
}

func (x *TaskData) GetTasks() []*TaskInfo {
	if x != nil {
		return x.Tasks
	}
	return nil
}

// 任务数据
type TaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int32        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`    //物品数据
	Cond []*Condition `protobuf:"bytes,2,rep,name=cond,proto3" json:"cond,omitempty"` //条件详情
}

func (x *TaskInfo) Reset() {
	*x = TaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskInfo) ProtoMessage() {}

func (x *TaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskInfo.ProtoReflect.Descriptor instead.
func (*TaskInfo) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{8}
}

func (x *TaskInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TaskInfo) GetCond() []*Condition {
	if x != nil {
		return x.Cond
	}
	return nil
}

// 属性数据
type PowerData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  MyDefine.POWER `protobuf:"varint,1,opt,name=type,proto3,enum=proto.MyDefine.POWER" json:"type,omitempty"` //属性类型
	Value int32          `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`                         //属性值
}

func (x *PowerData) Reset() {
	*x = PowerData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PowerData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PowerData) ProtoMessage() {}

func (x *PowerData) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PowerData.ProtoReflect.Descriptor instead.
func (*PowerData) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{9}
}

func (x *PowerData) GetType() MyDefine.POWER {
	if x != nil {
		return x.Type
	}
	return MyDefine.POWER(0)
}

func (x *PowerData) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

// 离线任务数据
type OfflineTaskData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *OfflineTaskData) Reset() {
	*x = OfflineTaskData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfflineTaskData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineTaskData) ProtoMessage() {}

func (x *OfflineTaskData) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineTaskData.ProtoReflect.Descriptor instead.
func (*OfflineTaskData) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{10}
}

// 点位数据
type Point struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	X int32 `protobuf:"varint,1,opt,name=x,proto3" json:"x,omitempty"` //x
	Y int32 `protobuf:"varint,2,opt,name=y,proto3" json:"y,omitempty"` //y
}

func (x *Point) Reset() {
	*x = Point{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Point) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Point) ProtoMessage() {}

func (x *Point) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Point.ProtoReflect.Descriptor instead.
func (*Point) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{11}
}

func (x *Point) GetX() int32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *Point) GetY() int32 {
	if x != nil {
		return x.Y
	}
	return 0
}

// 技能模块数据
type SkillData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sp                int32                `protobuf:"varint,1,opt,name=sp,proto3" json:"sp,omitempty"`                                                                                             //技能点
	List              map[int32]*SkillInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //技能列表数据
	Cnt               int32                `protobuf:"varint,3,opt,name=cnt,proto3" json:"cnt,omitempty"`                                                                                           //技能槽数量
	ActiveAutoSkillId int32                `protobuf:"varint,4,opt,name=activeAutoSkillId,proto3" json:"activeAutoSkillId,omitempty"`                                                               //自动释放的主动技能id
	AutoSkillId       []int32              `protobuf:"varint,5,rep,packed,name=autoSkillId,proto3" json:"autoSkillId,omitempty"`                                                                    //自动释放的自动技能id
}

func (x *SkillData) Reset() {
	*x = SkillData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SkillData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkillData) ProtoMessage() {}

func (x *SkillData) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkillData.ProtoReflect.Descriptor instead.
func (*SkillData) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{12}
}

func (x *SkillData) GetSp() int32 {
	if x != nil {
		return x.Sp
	}
	return 0
}

func (x *SkillData) GetList() map[int32]*SkillInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SkillData) GetCnt() int32 {
	if x != nil {
		return x.Cnt
	}
	return 0
}

func (x *SkillData) GetActiveAutoSkillId() int32 {
	if x != nil {
		return x.ActiveAutoSkillId
	}
	return 0
}

func (x *SkillData) GetAutoSkillId() []int32 {
	if x != nil {
		return x.AutoSkillId
	}
	return nil
}

// 技能信息
type SkillInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`               //技能id
	BaseLevel int32 `protobuf:"varint,2,opt,name=baseLevel,proto3" json:"baseLevel,omitempty"` //技能基础等级
	AddLevel  int32 `protobuf:"varint,3,opt,name=addLevel,proto3" json:"addLevel,omitempty"`   //技能增加等级
	IsLearn   bool  `protobuf:"varint,4,opt,name=isLearn,proto3" json:"isLearn,omitempty"`     //是不是学习而来
}

func (x *SkillInfo) Reset() {
	*x = SkillInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SkillInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SkillInfo) ProtoMessage() {}

func (x *SkillInfo) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SkillInfo.ProtoReflect.Descriptor instead.
func (*SkillInfo) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{13}
}

func (x *SkillInfo) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SkillInfo) GetBaseLevel() int32 {
	if x != nil {
		return x.BaseLevel
	}
	return 0
}

func (x *SkillInfo) GetAddLevel() int32 {
	if x != nil {
		return x.AddLevel
	}
	return 0
}

func (x *SkillInfo) GetIsLearn() bool {
	if x != nil {
		return x.IsLearn
	}
	return false
}

// 使用物品响应-宠物潜能石
type ItemUseResultPetAddSkillItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Old []int32      `protobuf:"varint,1,rep,packed,name=old,proto3" json:"old,omitempty"` //原本拥有的潜能技能id
	New []*SkillInfo `protobuf:"bytes,2,rep,name=new,proto3" json:"new,omitempty"`         //新的潜能技能
}

func (x *ItemUseResultPetAddSkillItem) Reset() {
	*x = ItemUseResultPetAddSkillItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ItemUseResultPetAddSkillItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemUseResultPetAddSkillItem) ProtoMessage() {}

func (x *ItemUseResultPetAddSkillItem) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemUseResultPetAddSkillItem.ProtoReflect.Descriptor instead.
func (*ItemUseResultPetAddSkillItem) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{14}
}

func (x *ItemUseResultPetAddSkillItem) GetOld() []int32 {
	if x != nil {
		return x.Old
	}
	return nil
}

func (x *ItemUseResultPetAddSkillItem) GetNew() []*SkillInfo {
	if x != nil {
		return x.New
	}
	return nil
}

// 战斗出招数据结构体
type BattlePlanObj struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     BattleDefine.Plan `protobuf:"varint,1,opt,name=type,proto3,enum=proto.BattleDefine.Plan" json:"type,omitempty"` //出招类型
	Position int32             `protobuf:"varint,2,opt,name=position,proto3" json:"position,omitempty"`                      //目标位置
	Extra    int32             `protobuf:"varint,3,opt,name=extra,proto3" json:"extra,omitempty"`                            //如果是使用技能，技能id；如果是使用物品，物品id
}

func (x *BattlePlanObj) Reset() {
	*x = BattlePlanObj{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_struct_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BattlePlanObj) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BattlePlanObj) ProtoMessage() {}

func (x *BattlePlanObj) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_struct_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BattlePlanObj.ProtoReflect.Descriptor instead.
func (*BattlePlanObj) Descriptor() ([]byte, []int) {
	return file_pbGame_struct_proto_rawDescGZIP(), []int{15}
}

func (x *BattlePlanObj) GetType() BattleDefine.Plan {
	if x != nil {
		return x.Type
	}
	return BattleDefine.Plan(0)
}

func (x *BattlePlanObj) GetPosition() int32 {
	if x != nil {
		return x.Position
	}
	return 0
}

func (x *BattlePlanObj) GetExtra() int32 {
	if x != nil {
		return x.Extra
	}
	return 0
}

var File_pbGame_struct_proto protoreflect.FileDescriptor

var file_pbGame_struct_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x70, 0x62,
	0x47, 0x61, 0x6d, 0x65, 0x2f, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x2f, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x4d,
	0x79, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2f, 0x4d, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2f, 0x42, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xed,
	0x01, 0x0a, 0x10, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x61, 0x74, 0x74,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x41, 0x74, 0x74, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x61, 0x74, 0x74, 0x72, 0x12, 0x24,
	0x0a, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x22, 0xdc,
	0x02, 0x0a, 0x0a, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x61, 0x70, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6d, 0x61,
	0x70, 0x49, 0x64, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01,
	0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x79, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x0a,
	0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x6f, 0x64,
	0x65, 0x12, 0x23, 0x0a, 0x04, 0x61, 0x74, 0x74, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x04, 0x61, 0x74, 0x74, 0x72, 0x12, 0x20, 0x0a, 0x03, 0x62, 0x61, 0x67, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x67, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x03, 0x62, 0x61, 0x67, 0x12, 0x23, 0x0a, 0x04, 0x74, 0x61, 0x73, 0x6b,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x12, 0x20, 0x0a,
	0x0b, 0x69, 0x74, 0x65, 0x6d, 0x53, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x0b, 0x69, 0x74, 0x65, 0x6d, 0x53, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x26, 0x0a, 0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64, 0x22, 0xf4, 0x02,
	0x0a, 0x08, 0x41, 0x74, 0x74, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x63,
	0x6f, 0x6e, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x31,
	0x12, 0x14, 0x0a, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x69, 0x63, 0x6f, 0x6e, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x33, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x63, 0x6f, 0x6e, 0x33, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x32, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x32, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x78, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x65, 0x78, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x78, 0x70, 0x32, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x65, 0x78, 0x70, 0x32, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x69, 0x70, 0x4c,
	0x76, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x69, 0x70, 0x4c, 0x76, 0x12, 0x1a,
	0x0a, 0x08, 0x76, 0x69, 0x70, 0x4c, 0x76, 0x4d, 0x61, 0x78, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x76, 0x69, 0x70, 0x4c, 0x76, 0x4d, 0x61, 0x78, 0x12, 0x0e, 0x0a, 0x02, 0x63, 0x70,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x63, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x74,
	0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x73, 0x74, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x61, 0x67, 0x69, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x67, 0x69, 0x12, 0x10,
	0x0a, 0x03, 0x63, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x63, 0x6f, 0x6e,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x6c, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x69,
	0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x77, 0x69, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x77, 0x69, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x68, 0x70, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x68, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x6d, 0x70, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x6d, 0x70, 0x22, 0x8d, 0x02, 0x0a, 0x07, 0x42, 0x61, 0x67, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x31, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x31, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x32, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x32,
	0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x67, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x62, 0x61, 0x67, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x65, 0x6c, 0x66, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x73, 0x65, 0x6c, 0x66, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2f, 0x0a, 0x05, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x42, 0x61, 0x67, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x05, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x1a, 0x49, 0x0a, 0x0a, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x25, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x84, 0x02, 0x0a, 0x07, 0x50, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x66, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x63, 0x66, 0x67, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x72,
	0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x67, 0x72, 0x6f, 0x77, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x65, 0x61, 0x72, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c,
	0x65, 0x61, 0x72, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x77, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x77, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x23, 0x0a, 0x04, 0x61, 0x74, 0x74, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x04, 0x61, 0x74, 0x74, 0x72, 0x12, 0x26, 0x0a, 0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53,
	0x6b, 0x69, 0x6c, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x12,
	0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x61, 0x67,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x77, 0x45, 0x78, 0x70, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x77, 0x45, 0x78, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xd8, 0x07, 0x0a, 0x08,
	0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x6c, 0x6f, 0x74,
	0x50, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x6c, 0x6f, 0x74, 0x50,
	0x6f, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x31,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x31,
	0x12, 0x28, 0x0a, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x32, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x32, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x33, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x70, 0x6f,
	0x77, 0x65, 0x72, 0x33, 0x12, 0x30, 0x0a, 0x0a, 0x62, 0x69, 0x6e, 0x64, 0x50, 0x6f, 0x77, 0x65,
	0x72, 0x31, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x62, 0x69, 0x6e, 0x64,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x31, 0x12, 0x30, 0x0a, 0x0a, 0x62, 0x69, 0x6e, 0x64, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x32, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x62, 0x69,
	0x6e, 0x64, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x32, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x34, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x34, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x35, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x35, 0x12, 0x28, 0x0a, 0x06,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x36, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06,
	0x70, 0x6f, 0x77, 0x65, 0x72, 0x36, 0x12, 0x28, 0x0a, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x37,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x06, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x37,
	0x12, 0x36, 0x0a, 0x0d, 0x65, 0x6e, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72,
	0x31, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0d, 0x65, 0x6e, 0x63, 0x68, 0x61,
	0x6e, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x31, 0x12, 0x36, 0x0a, 0x0d, 0x65, 0x6e, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x32, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x0d, 0x65, 0x6e, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x32,
	0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x75, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x64, 0x75, 0x72, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x12, 0x1e, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x44, 0x6f, 0x6e, 0x65, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x44, 0x6f, 0x6e, 0x65,
	0x12, 0x32, 0x0a, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x6f,
	0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x50,
	0x6f, 0x77, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x74, 0x61, 0x72, 0x18, 0x14, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x73, 0x74, 0x61, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x72, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x75,
	0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x61, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x65,
	0x74, 0x49, 0x64, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x28, 0x0a, 0x07, 0x70, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x65, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x07, 0x70, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x30, 0x0a, 0x05, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x18, 0x18, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x1a, 0x38, 0x0a, 0x0a,
	0x45, 0x78, 0x74, 0x72, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x72, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6e, 0x75, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x22, 0x51, 0x0a, 0x08, 0x54, 0x61,
	0x73, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x22, 0x40, 0x0a,
	0x08, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x24, 0x0a, 0x04, 0x63, 0x6f, 0x6e,
	0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x63, 0x6f, 0x6e, 0x64, 0x22,
	0x4c, 0x0a, 0x09, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x29, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x4d, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x4f, 0x57, 0x45,
	0x52, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x11, 0x0a,
	0x0f, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x61,
	0x22, 0x23, 0x0a, 0x05, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x01, 0x79, 0x22, 0xf8, 0x01, 0x0a, 0x09, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x73, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x02, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x63, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x11, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x41,
	0x75, 0x74, 0x6f, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x11, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x53, 0x6b, 0x69, 0x6c,
	0x6c, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x53, 0x6b, 0x69, 0x6c, 0x6c,
	0x49, 0x64, 0x18, 0x05, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x75, 0x74, 0x6f, 0x53, 0x6b,
	0x69, 0x6c, 0x6c, 0x49, 0x64, 0x1a, 0x49, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x26, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x6b, 0x69, 0x6c,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x6f, 0x0a, 0x09, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x62, 0x61, 0x73, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x62, 0x61, 0x73, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x61,
	0x64, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61,
	0x64, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x4c, 0x65, 0x61,
	0x72, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x4c, 0x65, 0x61, 0x72,
	0x6e, 0x22, 0x54, 0x0a, 0x1c, 0x49, 0x74, 0x65, 0x6d, 0x55, 0x73, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x50, 0x65, 0x74, 0x41, 0x64, 0x64, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x03,
	0x6f, 0x6c, 0x64, 0x12, 0x22, 0x0a, 0x03, 0x6e, 0x65, 0x77, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x03, 0x6e, 0x65, 0x77, 0x22, 0x6f, 0x0a, 0x0d, 0x42, 0x61, 0x74, 0x74, 0x6c,
	0x65, 0x50, 0x6c, 0x61, 0x6e, 0x4f, 0x62, 0x6a, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42,
	0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x50, 0x6c, 0x61, 0x6e,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x65, 0x78, 0x74, 0x72, 0x61, 0x42, 0x1c, 0x5a, 0x1a, 0x77, 0x6f, 0x72, 0x6c,
	0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x3b,
	0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_struct_proto_rawDescOnce sync.Once
	file_pbGame_struct_proto_rawDescData = file_pbGame_struct_proto_rawDesc
)

func file_pbGame_struct_proto_rawDescGZIP() []byte {
	file_pbGame_struct_proto_rawDescOnce.Do(func() {
		file_pbGame_struct_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_struct_proto_rawDescData)
	})
	return file_pbGame_struct_proto_rawDescData
}

var file_pbGame_struct_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_pbGame_struct_proto_goTypes = []interface{}{
	(*SimplePlayerInfo)(nil),             // 0: proto.SimplePlayerInfo
	(*PlayerInfo)(nil),                   // 1: proto.PlayerInfo
	(*AttrData)(nil),                     // 2: proto.AttrData
	(*BagData)(nil),                      // 3: proto.BagData
	(*PetData)(nil),                      // 4: proto.PetData
	(*ItemData)(nil),                     // 5: proto.ItemData
	(*Condition)(nil),                    // 6: proto.Condition
	(*TaskData)(nil),                     // 7: proto.TaskData
	(*TaskInfo)(nil),                     // 8: proto.TaskInfo
	(*PowerData)(nil),                    // 9: proto.PowerData
	(*OfflineTaskData)(nil),              // 10: proto.OfflineTaskData
	(*Point)(nil),                        // 11: proto.Point
	(*SkillData)(nil),                    // 12: proto.SkillData
	(*SkillInfo)(nil),                    // 13: proto.SkillInfo
	(*ItemUseResultPetAddSkillItem)(nil), // 14: proto.ItemUseResultPetAddSkillItem
	(*BattlePlanObj)(nil),                // 15: proto.BattlePlanObj
	nil,                                  // 16: proto.BagData.StoreEntry
	nil,                                  // 17: proto.ItemData.ExtraEntry
	nil,                                  // 18: proto.SkillData.ListEntry
	(ConditionType.Type)(0),              // 19: proto.ConditionType.Type
	(MyDefine.POWER)(0),                  // 20: proto.MyDefine.POWER
	(BattleDefine.Plan)(0),               // 21: proto.BattleDefine.Plan
}
var file_pbGame_struct_proto_depIdxs = []int32{
	2,  // 0: proto.SimplePlayerInfo.attr:type_name -> proto.AttrData
	10, // 1: proto.SimplePlayerInfo.offlineTask:type_name -> proto.OfflineTaskData
	2,  // 2: proto.PlayerInfo.attr:type_name -> proto.AttrData
	3,  // 3: proto.PlayerInfo.bag:type_name -> proto.BagData
	7,  // 4: proto.PlayerInfo.task:type_name -> proto.TaskData
	12, // 5: proto.PlayerInfo.skill:type_name -> proto.SkillData
	16, // 6: proto.BagData.store:type_name -> proto.BagData.StoreEntry
	2,  // 7: proto.PetData.attr:type_name -> proto.AttrData
	12, // 8: proto.PetData.skill:type_name -> proto.SkillData
	9,  // 9: proto.ItemData.power1:type_name -> proto.PowerData
	9,  // 10: proto.ItemData.power2:type_name -> proto.PowerData
	9,  // 11: proto.ItemData.power3:type_name -> proto.PowerData
	9,  // 12: proto.ItemData.bindPower1:type_name -> proto.PowerData
	9,  // 13: proto.ItemData.bindPower2:type_name -> proto.PowerData
	9,  // 14: proto.ItemData.power4:type_name -> proto.PowerData
	9,  // 15: proto.ItemData.power5:type_name -> proto.PowerData
	9,  // 16: proto.ItemData.power6:type_name -> proto.PowerData
	9,  // 17: proto.ItemData.power7:type_name -> proto.PowerData
	9,  // 18: proto.ItemData.enchantPower1:type_name -> proto.PowerData
	9,  // 19: proto.ItemData.enchantPower2:type_name -> proto.PowerData
	9,  // 20: proto.ItemData.attachPower:type_name -> proto.PowerData
	4,  // 21: proto.ItemData.petItem:type_name -> proto.PetData
	17, // 22: proto.ItemData.extra:type_name -> proto.ItemData.ExtraEntry
	19, // 23: proto.Condition.type:type_name -> proto.ConditionType.Type
	8,  // 24: proto.TaskData.tasks:type_name -> proto.TaskInfo
	6,  // 25: proto.TaskInfo.cond:type_name -> proto.Condition
	20, // 26: proto.PowerData.type:type_name -> proto.MyDefine.POWER
	18, // 27: proto.SkillData.list:type_name -> proto.SkillData.ListEntry
	13, // 28: proto.ItemUseResultPetAddSkillItem.new:type_name -> proto.SkillInfo
	21, // 29: proto.BattlePlanObj.type:type_name -> proto.BattleDefine.Plan
	5,  // 30: proto.BagData.StoreEntry.value:type_name -> proto.ItemData
	13, // 31: proto.SkillData.ListEntry.value:type_name -> proto.SkillInfo
	32, // [32:32] is the sub-list for method output_type
	32, // [32:32] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_pbGame_struct_proto_init() }
func file_pbGame_struct_proto_init() {
	if File_pbGame_struct_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbGame_struct_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SimplePlayerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PlayerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttrData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BagData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PetData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItemData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Condition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PowerData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfflineTaskData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Point); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SkillData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SkillInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ItemUseResultPetAddSkillItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_struct_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BattlePlanObj); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_struct_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_struct_proto_goTypes,
		DependencyIndexes: file_pbGame_struct_proto_depIdxs,
		MessageInfos:      file_pbGame_struct_proto_msgTypes,
	}.Build()
	File_pbGame_struct_proto = out.File
	file_pbGame_struct_proto_rawDesc = nil
	file_pbGame_struct_proto_goTypes = nil
	file_pbGame_struct_proto_depIdxs = nil
}
