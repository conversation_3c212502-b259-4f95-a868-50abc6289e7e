// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbGame/BattleDefine/BattleDefine.proto

package BattleDefine

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 条件定义
type Type int32

const (
	LOCAL        Type = 0   //本地
	REMOTE       Type = 2   //联网
	PK           Type = 4   //pk对战
	PET_PK       Type = 8   //宠物对战
	FINAL_BOSS   Type = 16  //boss
	CROSS_SERVER Type = 32  //跨服
	CROSS_RANK   Type = 64  //跨服pk
	CAMP_WAR     Type = 128 //国战
)

// Enum value maps for Type.
var (
	Type_name = map[int32]string{
		0:   "LOCAL",
		2:   "REMOTE",
		4:   "PK",
		8:   "PET_PK",
		16:  "FINAL_BOSS",
		32:  "CROSS_SERVER",
		64:  "CROSS_RANK",
		128: "CAMP_WAR",
	}
	Type_value = map[string]int32{
		"LOCAL":        0,
		"REMOTE":       2,
		"PK":           4,
		"PET_PK":       8,
		"FINAL_BOSS":   16,
		"CROSS_SERVER": 32,
		"CROSS_RANK":   64,
		"CAMP_WAR":     128,
	}
)

func (x Type) Enum() *Type {
	p := new(Type)
	*p = x
	return p
}

func (x Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_BattleDefine_BattleDefine_proto_enumTypes[0].Descriptor()
}

func (Type) Type() protoreflect.EnumType {
	return &file_pbGame_BattleDefine_BattleDefine_proto_enumTypes[0]
}

func (x Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type.Descriptor instead.
func (Type) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_BattleDefine_BattleDefine_proto_rawDescGZIP(), []int{0}
}

// 条件定义

type Result int32

const (
	NONE         Result = 0  //未知
	LEFT_WIN     Result = 1  //左边胜利
	RIGHT_WIN    Result = 2  //右边胜利
	ROUND_OVER   Result = 3  //超回合
	BOTH_LOSE    Result = 4  //双方都失败
	WAR_TIME_OUT Result = 8  //超时
	ARENA_OUT    Result = 10 //
)

// Enum value maps for Result.
var (
	Result_name = map[int32]string{
		0:  "NONE",
		1:  "LEFT_WIN",
		2:  "RIGHT_WIN",
		3:  "ROUND_OVER",
		4:  "BOTH_LOSE",
		8:  "WAR_TIME_OUT",
		10: "ARENA_OUT",
	}
	Result_value = map[string]int32{
		"NONE":         0,
		"LEFT_WIN":     1,
		"RIGHT_WIN":    2,
		"ROUND_OVER":   3,
		"BOTH_LOSE":    4,
		"WAR_TIME_OUT": 8,
		"ARENA_OUT":    10,
	}
)

func (x Result) Enum() *Result {
	p := new(Result)
	*p = x
	return p
}

func (x Result) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Result) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_BattleDefine_BattleDefine_proto_enumTypes[1].Descriptor()
}

func (Result) Type() protoreflect.EnumType {
	return &file_pbGame_BattleDefine_BattleDefine_proto_enumTypes[1]
}

func (x Result) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Result.Descriptor instead.
func (Result) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_BattleDefine_BattleDefine_proto_rawDescGZIP(), []int{1}
}

// 出招选择定义
type Order int32

const (
	OrderNone   Order = 0  //自动添加
	NONE_ORDER  Order = -1 //未知
	ATTACK      Order = 1  //攻击
	SKILL       Order = 2  //技能
	ITEM        Order = 3  //使用物品
	AUTO        Order = 4  //自动
	INFO        Order = 5  //查看
	ESCAPE      Order = 6  //逃跑
	REPLAY      Order = 7  //重播
	AUTO_ESCAPE Order = 8  //自动逃跑
)

// Enum value maps for Order.
var (
	Order_name = map[int32]string{
		0:  "OrderNone",
		-1: "NONE_ORDER",
		1:  "ATTACK",
		2:  "SKILL",
		3:  "ITEM",
		4:  "AUTO",
		5:  "INFO",
		6:  "ESCAPE",
		7:  "REPLAY",
		8:  "AUTO_ESCAPE",
	}
	Order_value = map[string]int32{
		"OrderNone":   0,
		"NONE_ORDER":  -1,
		"ATTACK":      1,
		"SKILL":       2,
		"ITEM":        3,
		"AUTO":        4,
		"INFO":        5,
		"ESCAPE":      6,
		"REPLAY":      7,
		"AUTO_ESCAPE": 8,
	}
)

func (x Order) Enum() *Order {
	p := new(Order)
	*p = x
	return p
}

func (x Order) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Order) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_BattleDefine_BattleDefine_proto_enumTypes[2].Descriptor()
}

func (Order) Type() protoreflect.EnumType {
	return &file_pbGame_BattleDefine_BattleDefine_proto_enumTypes[2]
}

func (x Order) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Order.Descriptor instead.
func (Order) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_BattleDefine_BattleDefine_proto_rawDescGZIP(), []int{2}
}

// 出招计划类型

type Plan int32

const (
	PLAN_NONE      Plan = 0 //未知
	PLAN_ATTACK    Plan = 1 //攻击
	PLAN_USE_SKILL Plan = 2 //使用技能
	PLAN_USE_ITEM  Plan = 3 //使用物品
	PLAN_ESCAPE    Plan = 4 //逃跑
)

// Enum value maps for Plan.
var (
	Plan_name = map[int32]string{
		0: "PLAN_NONE",
		1: "PLAN_ATTACK",
		2: "PLAN_USE_SKILL",
		3: "PLAN_USE_ITEM",
		4: "PLAN_ESCAPE",
	}
	Plan_value = map[string]int32{
		"PLAN_NONE":      0,
		"PLAN_ATTACK":    1,
		"PLAN_USE_SKILL": 2,
		"PLAN_USE_ITEM":  3,
		"PLAN_ESCAPE":    4,
	}
)

func (x Plan) Enum() *Plan {
	p := new(Plan)
	*p = x
	return p
}

func (x Plan) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Plan) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_BattleDefine_BattleDefine_proto_enumTypes[3].Descriptor()
}

func (Plan) Type() protoreflect.EnumType {
	return &file_pbGame_BattleDefine_BattleDefine_proto_enumTypes[3]
}

func (x Plan) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Plan.Descriptor instead.
func (Plan) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_BattleDefine_BattleDefine_proto_rawDescGZIP(), []int{3}
}

var File_pbGame_BattleDefine_BattleDefine_proto protoreflect.FileDescriptor

var file_pbGame_BattleDefine_BattleDefine_proto_rawDesc = []byte{
	0x0a, 0x26, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x2f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2a, 0x72, 0x0a, 0x04,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x4c, 0x4f, 0x43, 0x41, 0x4c, 0x10, 0x00, 0x12,
	0x0a, 0x0a, 0x06, 0x52, 0x45, 0x4d, 0x4f, 0x54, 0x45, 0x10, 0x02, 0x12, 0x06, 0x0a, 0x02, 0x50,
	0x4b, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x45, 0x54, 0x5f, 0x50, 0x4b, 0x10, 0x08, 0x12,
	0x0e, 0x0a, 0x0a, 0x46, 0x49, 0x4e, 0x41, 0x4c, 0x5f, 0x42, 0x4f, 0x53, 0x53, 0x10, 0x10, 0x12,
	0x10, 0x0a, 0x0c, 0x43, 0x52, 0x4f, 0x53, 0x53, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x10,
	0x20, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x52, 0x4f, 0x53, 0x53, 0x5f, 0x52, 0x41, 0x4e, 0x4b, 0x10,
	0x40, 0x12, 0x0d, 0x0a, 0x08, 0x43, 0x41, 0x4d, 0x50, 0x5f, 0x57, 0x41, 0x52, 0x10, 0x80, 0x01,
	0x2a, 0x6f, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x4f,
	0x4e, 0x45, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x4c, 0x45, 0x46, 0x54, 0x5f, 0x57, 0x49, 0x4e,
	0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x57, 0x49, 0x4e, 0x10,
	0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x10,
	0x03, 0x12, 0x0d, 0x0a, 0x09, 0x42, 0x4f, 0x54, 0x48, 0x5f, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0x04,
	0x12, 0x10, 0x0a, 0x0c, 0x57, 0x41, 0x52, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x4f, 0x55, 0x54,
	0x10, 0x08, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x52, 0x45, 0x4e, 0x41, 0x5f, 0x4f, 0x55, 0x54, 0x10,
	0x0a, 0x2a, 0x8d, 0x01, 0x0a, 0x05, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x0d, 0x0a, 0x09, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x0a, 0x4e, 0x4f,
	0x4e, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0x01, 0x12,
	0x09, 0x0a, 0x05, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x54,
	0x45, 0x4d, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x41, 0x55, 0x54, 0x4f, 0x10, 0x04, 0x12, 0x08,
	0x0a, 0x04, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x53, 0x43, 0x41,
	0x50, 0x45, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x50, 0x4c, 0x41, 0x59, 0x10, 0x07,
	0x12, 0x0f, 0x0a, 0x0b, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x45, 0x53, 0x43, 0x41, 0x50, 0x45, 0x10,
	0x08, 0x2a, 0x5e, 0x0a, 0x04, 0x50, 0x6c, 0x61, 0x6e, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x4c, 0x41,
	0x4e, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x4c, 0x41, 0x4e,
	0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x4c, 0x41,
	0x4e, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x10, 0x02, 0x12, 0x11, 0x0a,
	0x0d, 0x50, 0x4c, 0x41, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x10, 0x03,
	0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x4c, 0x41, 0x4e, 0x5f, 0x45, 0x53, 0x43, 0x41, 0x50, 0x45, 0x10,
	0x04, 0x42, 0x2f, 0x5a, 0x2d, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44,
	0x65, 0x66, 0x69, 0x6e, 0x65, 0x3b, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_BattleDefine_BattleDefine_proto_rawDescOnce sync.Once
	file_pbGame_BattleDefine_BattleDefine_proto_rawDescData = file_pbGame_BattleDefine_BattleDefine_proto_rawDesc
)

func file_pbGame_BattleDefine_BattleDefine_proto_rawDescGZIP() []byte {
	file_pbGame_BattleDefine_BattleDefine_proto_rawDescOnce.Do(func() {
		file_pbGame_BattleDefine_BattleDefine_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_BattleDefine_BattleDefine_proto_rawDescData)
	})
	return file_pbGame_BattleDefine_BattleDefine_proto_rawDescData
}

var file_pbGame_BattleDefine_BattleDefine_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_pbGame_BattleDefine_BattleDefine_proto_goTypes = []interface{}{
	(Type)(0),   // 0: proto.BattleDefine.Type
	(Result)(0), // 1: proto.BattleDefine.Result
	(Order)(0),  // 2: proto.BattleDefine.Order
	(Plan)(0),   // 3: proto.BattleDefine.Plan
}
var file_pbGame_BattleDefine_BattleDefine_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbGame_BattleDefine_BattleDefine_proto_init() }
func file_pbGame_BattleDefine_BattleDefine_proto_init() {
	if File_pbGame_BattleDefine_BattleDefine_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_BattleDefine_BattleDefine_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_BattleDefine_BattleDefine_proto_goTypes,
		DependencyIndexes: file_pbGame_BattleDefine_BattleDefine_proto_depIdxs,
		EnumInfos:         file_pbGame_BattleDefine_BattleDefine_proto_enumTypes,
	}.Build()
	File_pbGame_BattleDefine_BattleDefine_proto = out.File
	file_pbGame_BattleDefine_BattleDefine_proto_rawDesc = nil
	file_pbGame_BattleDefine_BattleDefine_proto_goTypes = nil
	file_pbGame_BattleDefine_BattleDefine_proto_depIdxs = nil
}
