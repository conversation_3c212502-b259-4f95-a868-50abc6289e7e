// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbGame/MyDefine/MyDefine.proto

package MyDefine

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 通用定义
type Const int32

const (
	None                 Const = 0    //自动添加
	CAN_DELETE           Const = 4    //占位
	BACK_ERROR_DUR       Const = -400 //
	BACK_ERROR_NULL_HAND Const = -401 //
)

// Enum value maps for Const.
var (
	Const_name = map[int32]string{
		0:    "None",
		4:    "CAN_DELETE",
		-400: "BACK_ERROR_DUR",
		-401: "BACK_ERROR_NULL_HAND",
	}
	Const_value = map[string]int32{
		"None":                 0,
		"CAN_DELETE":           4,
		"BACK_ERROR_DUR":       -400,
		"BACK_ERROR_NULL_HAND": -401,
	}
)

func (x Const) Enum() *Const {
	p := new(Const)
	*p = x
	return p
}

func (x Const) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Const) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_MyDefine_MyDefine_proto_enumTypes[0].Descriptor()
}

func (Const) Type() protoreflect.EnumType {
	return &file_pbGame_MyDefine_MyDefine_proto_enumTypes[0]
}

func (x Const) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Const.Descriptor instead.
func (Const) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_MyDefine_MyDefine_proto_rawDescGZIP(), []int{0}
}

// 属性能力定义

type POWER int32

const (
	POWER_NONE                      POWER = 0   //未知
	POWER_STR                       POWER = 1   //力量
	POWER_STR_PERCENT               POWER = 2   //力量百分比
	POWER_CON                       POWER = 3   //体质
	POWER_CON_PERCENT               POWER = 4   //体质百分比
	POWER_AGI                       POWER = 5   //敏捷
	POWER_AGI_PERCENT               POWER = 6   //敏捷百分比
	POWER_ILT                       POWER = 7   //智力
	POWER_ILT_PERCENT               POWER = 8   //智力百分比
	POWER_WIS                       POWER = 9   //感知
	POWER_WIS_PERCENT               POWER = 10  //感知百分比
	POWER_HPMAX                     POWER = 11  //
	POWER_HPMAX_PERCENT             POWER = 12  //
	POWER_MPMAX                     POWER = 13  //
	POWER_MPMAX_PERCENT             POWER = 14  //
	POWER_SPEED_PERCENT             POWER = 23  //
	POWER_HITRATE_PERCENT           POWER = 24  //
	POWER_DODGE_PERCENT             POWER = 25  //
	POWER_MAGIC_HITRATE_PERCENT     POWER = 26  //
	POWER_CRITICAL_PERCENT          POWER = 27  //
	POWER_ATK_STR_PERCENT           POWER = 28  //
	POWER_ATK_AGI_PERCENT           POWER = 29  //
	POWER_ATK_MAGIC_PERCENT         POWER = 30  //
	POWER_DEF_STR_PERCENT           POWER = 31  //
	POWER_DEF_AGI_PERCENT           POWER = 32  //
	POWER_DEF_MAGIC_PERCENT         POWER = 33  //
	POWER_WIL_PERCENT               POWER = 34  //
	POWER_TOUGH_PERCENT             POWER = 35  //
	POWER_REFLECTION_PERCENT        POWER = 36  //
	POWER_BLOCK_PERCENT             POWER = 37  //
	POWER_INSIGHT_PERCENT           POWER = 38  //
	POWER_PENETRATION_PERCENT       POWER = 39  //
	POWER_DEF_FIELD_PERCENT         POWER = 40  //
	POWER_BACK_PERCENT              POWER = 41  //
	POWER_MAGIC_BACK_PERCENT        POWER = 42  //
	POWER_LIFE_ABSORPTION_PERCENT   POWER = 43  //
	POWER_MANA_ABSORPTION_PERCENT   POWER = 44  //
	POWER_MAGIC_PENETRATION_PERCENT POWER = 45  //
	POWER_HIT_FORCE_PERCENT         POWER = 46  //
	POWER_HEAL_RECOVERY_PERCENT     POWER = 47  //
	POWER_MANA_RECOVERY_PERCENT     POWER = 48  //
	POWER_HP                        POWER = 49  //
	POWER_HP_PERCENT                POWER = 50  //
	POWER_MP                        POWER = 51  //
	POWER_MP_PERCENT                POWER = 52  //
	POWER_SPEED                     POWER = 53  //
	POWER_HITRATE                   POWER = 54  //
	POWER_DODGE                     POWER = 55  //
	POWER_MAGIC_HITRATE             POWER = 56  //
	POWER_CRITICAL                  POWER = 57  //
	POWER_ATK_STR                   POWER = 58  //
	POWER_ATK_AGI                   POWER = 59  //
	POWER_ATK_MAGIC                 POWER = 60  //
	POWER_DEF_STR                   POWER = 61  //
	POWER_DEF_AGI                   POWER = 62  //
	POWER_DEF_MAGIC                 POWER = 63  //
	POWER_WIL                       POWER = 64  //
	POWER_TOUGH                     POWER = 65  //
	POWER_REFLECTION                POWER = 66  //
	POWER_BLOCK                     POWER = 67  //
	POWER_INSIGHT                   POWER = 68  //
	POWER_PENETRATION               POWER = 69  //
	POWER_DEF_FIELD                 POWER = 70  //
	POWER_BACK                      POWER = 71  //
	POWER_MAGIC_BACK                POWER = 72  //
	POWER_LIFE_ABSORPTION           POWER = 73  //
	POWER_MANA_ABSORPTION           POWER = 74  //
	POWER_MAGIC_PENETRATION         POWER = 75  //
	POWER_HIT_FORCE                 POWER = 76  //
	POWER_HEAL_RECOVERY             POWER = 77  //
	POWER_MANA_RECOVERY             POWER = 78  //
	POWER_REMOVE_STATUS             POWER = 79  //
	POWER_RECOVER                   POWER = 80  //
	POWER_SKILL_DAMAGE              POWER = 81  //
	POWER_SKILL_HITRATE             POWER = 82  //
	POWER_SELF_CRITICAL             POWER = 84  //
	POWER_SKILL_HIT_FORCE           POWER = 85  //
	POWER_SKILL_MAGIC_PENETRATION   POWER = 86  //
	POWER_SKILL_BRK_ARMOR           POWER = 87  //
	POWER_SKILL_REMOVE_STATUS       POWER = 88  //
	POWER_PET_DAMAGE                POWER = 89  //
	POWER_PET_HPMAX_PERCENT         POWER = 90  //
	POWER_PET_MPMAX_PERCENT         POWER = 91  //
	POWER_PET_STR_PERCENT           POWER = 92  //
	POWER_PET_CON_PERCENT           POWER = 93  //
	POWER_PET_AGI_PERCENT           POWER = 94  //
	POWER_PET_ILT_PERCENT           POWER = 95  //
	POWER_PET_WIS_PERCENT           POWER = 96  //
	POWER_RECOVER_PERCENT           POWER = 97  //
	POWER_HPMP_RECOVER              POWER = 98  //
	POWER_OPEN_STORE                POWER = 99  //
	POWER_CHEST_LV1                 POWER = 100 //
	POWER_CHEST_LV2                 POWER = 101 //
	POWER_CHEST_LV3                 POWER = 102 //
	POWER_REQ_SLOT                  POWER = 103 //
	POWER_TO_WORLDMAP               POWER = 104 //
	POWER_TO_GXGY                   POWER = 105 //
	POWER_EXPIRE_TIME               POWER = 106 //
	POWER_RESET_MISSION             POWER = 107 //
	POWER_CHEST_KEY_LEVEL           POWER = 108 //
	POWER_PET_EGG                   POWER = 109 //
	POWER_COSTUME                   POWER = 110 //
	POWER_TRANSPORT                 POWER = 111 //
	POWER_POWER_TITLE               POWER = 112 //
	POWER_GUARD_STR_ATTACK          POWER = 113 //
	POWER_GUARD_AGI_ATTACK          POWER = 114 //
	POWER_GUARD_MAGIC_ATTACK        POWER = 115 //
	POWER_GUARD_CURSE_ATTACK        POWER = 116 //
	POWER_GUARD_ALL_ATTACK          POWER = 117 //
	POWER_PET_ADD_EXP               POWER = 118 //
	POWER_ADD_EXP                   POWER = 119 //
	POWER_EXP_BY_TIME               POWER = 120 //
	POWER_IDENTIFY                  POWER = 121 //
	POWER_SWORD_ATK_TIME            POWER = 122 //
	POWER_BLADE_ATK_TIME            POWER = 123 //
	POWER_HEAVY_ATK_TIME            POWER = 124 //
	POWER_LANCE_ATK_TIME            POWER = 125 //
	POWER_STAFF_ATK_TIME            POWER = 126 //
	POWER_HAND_ATK_TIME             POWER = 127 //
	POWER_BOW_ATK_TIME              POWER = 128 //
	POWER_HAND_ITEM_ATK_TIME        POWER = 129 //
	POWER_ALL_ATK_TIME              POWER = 130 //
	POWER_COMPOSITE                 POWER = 131 //
	POWER_SWORD_PERCENT             POWER = 133 //
	POWER_BLADE_PERCENT             POWER = 134 //
	POWER_HEAVY_PERCENT             POWER = 135 //
	POWER_LANCE_PERCENT             POWER = 136 //
	POWER_STAFF_PERCENT             POWER = 137 //
	POWER_HAND_PERCENT              POWER = 138 //
	POWER_BOW_PERCENT               POWER = 139 //
	POWER_HAND_ITEM_PERCENT         POWER = 140 //
	POWER_ALL_PERCENT               POWER = 141 //
	POWER_EQUIP_ARMOR_DUR_PERCENT   POWER = 142 //
	POWER_SKILL_HP                  POWER = 145 //
	POWER_SKILL_HP_PERCENT          POWER = 146 //
	POWER_SKILL_MP                  POWER = 147 //
	POWER_SKILL_MP_PERCENT          POWER = 148 //
	POWER_SKILL_LIFE_ABSORPTION     POWER = 149 //
	POWER_SKILL_MANA_ABSORPTION     POWER = 150 //
	POWER_SKILL_TARGET_BACK         POWER = 151 //
	POWER_SKILL_TARGET_MAGIC_BACK   POWER = 152 //
	POWER_SKILL_TARGET_BLOCK        POWER = 153 //
	POWER_SKILL_TARGET_INSIGHT      POWER = 154 //
	POWER_SKILL_TARGET_WIL          POWER = 155 //
	POWER_SKILL_TARGET_TOUCH        POWER = 156 //
	POWER_GRARD_MASTER_STR_ATTACK   POWER = 157 //
	POWER_GRARD_MASTER_AGI_ATTACK   POWER = 158 //
	POWER_GRARD_MASTER_MAGIC_ATTACK POWER = 159 //
	POWER_GRARD_MASTER_CURSE_ATTACK POWER = 160 //
	POWER_GRARD_MASTER_ALL_ATTACK   POWER = 161 //
	POWER_PET_GRARD_STR_ATTACK      POWER = 162 //
	POWER_PET_GRARD_AGI_ATTACK      POWER = 163 //
	POWER_PET_GRARD_MAGIC_ATTACK    POWER = 164 //
	POWER_PET_GRARD_CURSE_ATTACK    POWER = 165 //
	POWER_PET_GRARD_ALL_ATTACK      POWER = 166 //
	POWER_EXP_MISSION_BY_TIME       POWER = 167 //
	POWER_IGNORE_BACK               POWER = 168 //
	POWER_IGNORE_MAGIC_BACK         POWER = 169 //
	POWER_IGNORE_BLOCK              POWER = 170 //
	POWER_IGNORE_INSIGHT            POWER = 171 //
	POWER_IGNORE_WIL                POWER = 172 //
	POWER_IGNORE_TOUCH              POWER = 173 //
	POWER_BALL_ATK_TIME             POWER = 174 //
	POWER_BALL_PERCENT              POWER = 175 //
	POWER_SKILL_SCROLL              POWER = 176 //
	POWER_SKILL_SCROLL_PET          POWER = 177 //
	POWER_KEEPOUT_ATK_TIME          POWER = 178 //
	POWER_NEW_GET_PET               POWER = 179 //
	POWER_NEW_GET_ITEM              POWER = 180 //
	POWER_ENCHANT_ITEM              POWER = 181 //
	POWER_FORMATION_BOOK            POWER = 182 //
	POWER_CHEST_LV4                 POWER = 183 //
	POWER_COLOR_BOX                 POWER = 184 //
	POWER_TURN_MONSTER_CARD         POWER = 185 //
	POWER_SKILL_BOOK_PET            POWER = 186 //
	POWER_GUN_ATK_TIME              POWER = 190 //
	POWER_GUN_PERCENT               POWER = 191 //
	POWER_HAMMER_ATK_TIME           POWER = 192 //
	POWER_HAMMER_PERCENT            POWER = 193 //
	POWER_FAN_ATK_TIME              POWER = 194 //
	POWER_FAN_PERCENT               POWER = 195 //
	POWER_MAGIC_PERCENT             POWER = 196 //
	POWER_PHYSICS_PERCENT           POWER = 197 //
	POWER_HP_MP                     POWER = 198 //
	POWER_IGNORE_CRITICAL           POWER = 201 //
	POWER_IGNORE_CRITICAL_PERCENT   POWER = 202 //
	POWER_CRITICAL_DAMAGE           POWER = 203 //
	POWER_CRITICAL_DAMAGE_PERCENT   POWER = 204 //
	POWER_BLADE_L_ATK_TIME          POWER = 210 //
	POWER_BLADE_H_ATK_TIME          POWER = 211 //
	POWER_SWORD_L_ATK_TIME          POWER = 212 //
	POWER_SWORD_H_ATK_TIME          POWER = 213 //
	POWER_CROSSBOW_L_ATK_TIME       POWER = 214 //
	POWER_CROSSBOW_H_ATK_TIME       POWER = 215 //
	POWER_ARROW_ATK_TIME            POWER = 216 //
	POWER_BLADE_L_DAMAGE_PERCENT    POWER = 230 //
	POWER_BLADE_H_DAMAGE_PERCENT    POWER = 231 //
	POWER_SWORD_L_DAMAGE_PERCENT    POWER = 232 //
	POWER_SWORD_H_DAMAGE_PERCENT    POWER = 233 //
	POWER_CROSSBOW_L_DAMAGE_PERCENT POWER = 234 //
	POWER_CROSSBOW_H_DAMAGE_PERCENT POWER = 235 //
	POWER_ARROW_DAMAGE_PERCENT      POWER = 236 //
	POWER_DEF_STR_RANGE             POWER = 250 //
	POWER_DEF_STR_RANGE_PERCENT     POWER = 251 //
	POWER_DEF_STR_NEARBY            POWER = 252 //
	POWER_DEF_STR_NEARBY_PERCENT    POWER = 253 //
	POWER_DEF_AGI_RANGE             POWER = 254 //
	POWER_DEF_AGI_RANGE_PERCENT     POWER = 255 //
	POWER_DEF_AGI_NEARBY            POWER = 256 //
	POWER_DEF_AGI_NEARBY_PERCENT    POWER = 257 //
	POWER_ATK_STR_NEARBY            POWER = 258 //
	POWER_ATK_STR_NEARBY_PERCENT    POWER = 259 //
	POWER_ATK_STR_RANGE             POWER = 260 //
	POWER_ATK_STR_RANGE_PERCENT     POWER = 261 //
	POWER_ATK_AGI_NEARBY            POWER = 262 //
	POWER_ATK_AGI_NEARBY_PERCENT    POWER = 263 //
	POWER_ATK_AGI_RANGE             POWER = 264 //
	POWER_ATK_AGI_RANGE_PERCENT     POWER = 265 //
)

// Enum value maps for POWER.
var (
	POWER_name = map[int32]string{
		0:   "POWER_NONE",
		1:   "POWER_STR",
		2:   "POWER_STR_PERCENT",
		3:   "POWER_CON",
		4:   "POWER_CON_PERCENT",
		5:   "POWER_AGI",
		6:   "POWER_AGI_PERCENT",
		7:   "POWER_ILT",
		8:   "POWER_ILT_PERCENT",
		9:   "POWER_WIS",
		10:  "POWER_WIS_PERCENT",
		11:  "POWER_HPMAX",
		12:  "POWER_HPMAX_PERCENT",
		13:  "POWER_MPMAX",
		14:  "POWER_MPMAX_PERCENT",
		23:  "POWER_SPEED_PERCENT",
		24:  "POWER_HITRATE_PERCENT",
		25:  "POWER_DODGE_PERCENT",
		26:  "POWER_MAGIC_HITRATE_PERCENT",
		27:  "POWER_CRITICAL_PERCENT",
		28:  "POWER_ATK_STR_PERCENT",
		29:  "POWER_ATK_AGI_PERCENT",
		30:  "POWER_ATK_MAGIC_PERCENT",
		31:  "POWER_DEF_STR_PERCENT",
		32:  "POWER_DEF_AGI_PERCENT",
		33:  "POWER_DEF_MAGIC_PERCENT",
		34:  "POWER_WIL_PERCENT",
		35:  "POWER_TOUGH_PERCENT",
		36:  "POWER_REFLECTION_PERCENT",
		37:  "POWER_BLOCK_PERCENT",
		38:  "POWER_INSIGHT_PERCENT",
		39:  "POWER_PENETRATION_PERCENT",
		40:  "POWER_DEF_FIELD_PERCENT",
		41:  "POWER_BACK_PERCENT",
		42:  "POWER_MAGIC_BACK_PERCENT",
		43:  "POWER_LIFE_ABSORPTION_PERCENT",
		44:  "POWER_MANA_ABSORPTION_PERCENT",
		45:  "POWER_MAGIC_PENETRATION_PERCENT",
		46:  "POWER_HIT_FORCE_PERCENT",
		47:  "POWER_HEAL_RECOVERY_PERCENT",
		48:  "POWER_MANA_RECOVERY_PERCENT",
		49:  "POWER_HP",
		50:  "POWER_HP_PERCENT",
		51:  "POWER_MP",
		52:  "POWER_MP_PERCENT",
		53:  "POWER_SPEED",
		54:  "POWER_HITRATE",
		55:  "POWER_DODGE",
		56:  "POWER_MAGIC_HITRATE",
		57:  "POWER_CRITICAL",
		58:  "POWER_ATK_STR",
		59:  "POWER_ATK_AGI",
		60:  "POWER_ATK_MAGIC",
		61:  "POWER_DEF_STR",
		62:  "POWER_DEF_AGI",
		63:  "POWER_DEF_MAGIC",
		64:  "POWER_WIL",
		65:  "POWER_TOUGH",
		66:  "POWER_REFLECTION",
		67:  "POWER_BLOCK",
		68:  "POWER_INSIGHT",
		69:  "POWER_PENETRATION",
		70:  "POWER_DEF_FIELD",
		71:  "POWER_BACK",
		72:  "POWER_MAGIC_BACK",
		73:  "POWER_LIFE_ABSORPTION",
		74:  "POWER_MANA_ABSORPTION",
		75:  "POWER_MAGIC_PENETRATION",
		76:  "POWER_HIT_FORCE",
		77:  "POWER_HEAL_RECOVERY",
		78:  "POWER_MANA_RECOVERY",
		79:  "POWER_REMOVE_STATUS",
		80:  "POWER_RECOVER",
		81:  "POWER_SKILL_DAMAGE",
		82:  "POWER_SKILL_HITRATE",
		84:  "POWER_SELF_CRITICAL",
		85:  "POWER_SKILL_HIT_FORCE",
		86:  "POWER_SKILL_MAGIC_PENETRATION",
		87:  "POWER_SKILL_BRK_ARMOR",
		88:  "POWER_SKILL_REMOVE_STATUS",
		89:  "POWER_PET_DAMAGE",
		90:  "POWER_PET_HPMAX_PERCENT",
		91:  "POWER_PET_MPMAX_PERCENT",
		92:  "POWER_PET_STR_PERCENT",
		93:  "POWER_PET_CON_PERCENT",
		94:  "POWER_PET_AGI_PERCENT",
		95:  "POWER_PET_ILT_PERCENT",
		96:  "POWER_PET_WIS_PERCENT",
		97:  "POWER_RECOVER_PERCENT",
		98:  "POWER_HPMP_RECOVER",
		99:  "POWER_OPEN_STORE",
		100: "POWER_CHEST_LV1",
		101: "POWER_CHEST_LV2",
		102: "POWER_CHEST_LV3",
		103: "POWER_REQ_SLOT",
		104: "POWER_TO_WORLDMAP",
		105: "POWER_TO_GXGY",
		106: "POWER_EXPIRE_TIME",
		107: "POWER_RESET_MISSION",
		108: "POWER_CHEST_KEY_LEVEL",
		109: "POWER_PET_EGG",
		110: "POWER_COSTUME",
		111: "POWER_TRANSPORT",
		112: "POWER_POWER_TITLE",
		113: "POWER_GUARD_STR_ATTACK",
		114: "POWER_GUARD_AGI_ATTACK",
		115: "POWER_GUARD_MAGIC_ATTACK",
		116: "POWER_GUARD_CURSE_ATTACK",
		117: "POWER_GUARD_ALL_ATTACK",
		118: "POWER_PET_ADD_EXP",
		119: "POWER_ADD_EXP",
		120: "POWER_EXP_BY_TIME",
		121: "POWER_IDENTIFY",
		122: "POWER_SWORD_ATK_TIME",
		123: "POWER_BLADE_ATK_TIME",
		124: "POWER_HEAVY_ATK_TIME",
		125: "POWER_LANCE_ATK_TIME",
		126: "POWER_STAFF_ATK_TIME",
		127: "POWER_HAND_ATK_TIME",
		128: "POWER_BOW_ATK_TIME",
		129: "POWER_HAND_ITEM_ATK_TIME",
		130: "POWER_ALL_ATK_TIME",
		131: "POWER_COMPOSITE",
		133: "POWER_SWORD_PERCENT",
		134: "POWER_BLADE_PERCENT",
		135: "POWER_HEAVY_PERCENT",
		136: "POWER_LANCE_PERCENT",
		137: "POWER_STAFF_PERCENT",
		138: "POWER_HAND_PERCENT",
		139: "POWER_BOW_PERCENT",
		140: "POWER_HAND_ITEM_PERCENT",
		141: "POWER_ALL_PERCENT",
		142: "POWER_EQUIP_ARMOR_DUR_PERCENT",
		145: "POWER_SKILL_HP",
		146: "POWER_SKILL_HP_PERCENT",
		147: "POWER_SKILL_MP",
		148: "POWER_SKILL_MP_PERCENT",
		149: "POWER_SKILL_LIFE_ABSORPTION",
		150: "POWER_SKILL_MANA_ABSORPTION",
		151: "POWER_SKILL_TARGET_BACK",
		152: "POWER_SKILL_TARGET_MAGIC_BACK",
		153: "POWER_SKILL_TARGET_BLOCK",
		154: "POWER_SKILL_TARGET_INSIGHT",
		155: "POWER_SKILL_TARGET_WIL",
		156: "POWER_SKILL_TARGET_TOUCH",
		157: "POWER_GRARD_MASTER_STR_ATTACK",
		158: "POWER_GRARD_MASTER_AGI_ATTACK",
		159: "POWER_GRARD_MASTER_MAGIC_ATTACK",
		160: "POWER_GRARD_MASTER_CURSE_ATTACK",
		161: "POWER_GRARD_MASTER_ALL_ATTACK",
		162: "POWER_PET_GRARD_STR_ATTACK",
		163: "POWER_PET_GRARD_AGI_ATTACK",
		164: "POWER_PET_GRARD_MAGIC_ATTACK",
		165: "POWER_PET_GRARD_CURSE_ATTACK",
		166: "POWER_PET_GRARD_ALL_ATTACK",
		167: "POWER_EXP_MISSION_BY_TIME",
		168: "POWER_IGNORE_BACK",
		169: "POWER_IGNORE_MAGIC_BACK",
		170: "POWER_IGNORE_BLOCK",
		171: "POWER_IGNORE_INSIGHT",
		172: "POWER_IGNORE_WIL",
		173: "POWER_IGNORE_TOUCH",
		174: "POWER_BALL_ATK_TIME",
		175: "POWER_BALL_PERCENT",
		176: "POWER_SKILL_SCROLL",
		177: "POWER_SKILL_SCROLL_PET",
		178: "POWER_KEEPOUT_ATK_TIME",
		179: "POWER_NEW_GET_PET",
		180: "POWER_NEW_GET_ITEM",
		181: "POWER_ENCHANT_ITEM",
		182: "POWER_FORMATION_BOOK",
		183: "POWER_CHEST_LV4",
		184: "POWER_COLOR_BOX",
		185: "POWER_TURN_MONSTER_CARD",
		186: "POWER_SKILL_BOOK_PET",
		190: "POWER_GUN_ATK_TIME",
		191: "POWER_GUN_PERCENT",
		192: "POWER_HAMMER_ATK_TIME",
		193: "POWER_HAMMER_PERCENT",
		194: "POWER_FAN_ATK_TIME",
		195: "POWER_FAN_PERCENT",
		196: "POWER_MAGIC_PERCENT",
		197: "POWER_PHYSICS_PERCENT",
		198: "POWER_HP_MP",
		201: "POWER_IGNORE_CRITICAL",
		202: "POWER_IGNORE_CRITICAL_PERCENT",
		203: "POWER_CRITICAL_DAMAGE",
		204: "POWER_CRITICAL_DAMAGE_PERCENT",
		210: "POWER_BLADE_L_ATK_TIME",
		211: "POWER_BLADE_H_ATK_TIME",
		212: "POWER_SWORD_L_ATK_TIME",
		213: "POWER_SWORD_H_ATK_TIME",
		214: "POWER_CROSSBOW_L_ATK_TIME",
		215: "POWER_CROSSBOW_H_ATK_TIME",
		216: "POWER_ARROW_ATK_TIME",
		230: "POWER_BLADE_L_DAMAGE_PERCENT",
		231: "POWER_BLADE_H_DAMAGE_PERCENT",
		232: "POWER_SWORD_L_DAMAGE_PERCENT",
		233: "POWER_SWORD_H_DAMAGE_PERCENT",
		234: "POWER_CROSSBOW_L_DAMAGE_PERCENT",
		235: "POWER_CROSSBOW_H_DAMAGE_PERCENT",
		236: "POWER_ARROW_DAMAGE_PERCENT",
		250: "POWER_DEF_STR_RANGE",
		251: "POWER_DEF_STR_RANGE_PERCENT",
		252: "POWER_DEF_STR_NEARBY",
		253: "POWER_DEF_STR_NEARBY_PERCENT",
		254: "POWER_DEF_AGI_RANGE",
		255: "POWER_DEF_AGI_RANGE_PERCENT",
		256: "POWER_DEF_AGI_NEARBY",
		257: "POWER_DEF_AGI_NEARBY_PERCENT",
		258: "POWER_ATK_STR_NEARBY",
		259: "POWER_ATK_STR_NEARBY_PERCENT",
		260: "POWER_ATK_STR_RANGE",
		261: "POWER_ATK_STR_RANGE_PERCENT",
		262: "POWER_ATK_AGI_NEARBY",
		263: "POWER_ATK_AGI_NEARBY_PERCENT",
		264: "POWER_ATK_AGI_RANGE",
		265: "POWER_ATK_AGI_RANGE_PERCENT",
	}
	POWER_value = map[string]int32{
		"POWER_NONE":                      0,
		"POWER_STR":                       1,
		"POWER_STR_PERCENT":               2,
		"POWER_CON":                       3,
		"POWER_CON_PERCENT":               4,
		"POWER_AGI":                       5,
		"POWER_AGI_PERCENT":               6,
		"POWER_ILT":                       7,
		"POWER_ILT_PERCENT":               8,
		"POWER_WIS":                       9,
		"POWER_WIS_PERCENT":               10,
		"POWER_HPMAX":                     11,
		"POWER_HPMAX_PERCENT":             12,
		"POWER_MPMAX":                     13,
		"POWER_MPMAX_PERCENT":             14,
		"POWER_SPEED_PERCENT":             23,
		"POWER_HITRATE_PERCENT":           24,
		"POWER_DODGE_PERCENT":             25,
		"POWER_MAGIC_HITRATE_PERCENT":     26,
		"POWER_CRITICAL_PERCENT":          27,
		"POWER_ATK_STR_PERCENT":           28,
		"POWER_ATK_AGI_PERCENT":           29,
		"POWER_ATK_MAGIC_PERCENT":         30,
		"POWER_DEF_STR_PERCENT":           31,
		"POWER_DEF_AGI_PERCENT":           32,
		"POWER_DEF_MAGIC_PERCENT":         33,
		"POWER_WIL_PERCENT":               34,
		"POWER_TOUGH_PERCENT":             35,
		"POWER_REFLECTION_PERCENT":        36,
		"POWER_BLOCK_PERCENT":             37,
		"POWER_INSIGHT_PERCENT":           38,
		"POWER_PENETRATION_PERCENT":       39,
		"POWER_DEF_FIELD_PERCENT":         40,
		"POWER_BACK_PERCENT":              41,
		"POWER_MAGIC_BACK_PERCENT":        42,
		"POWER_LIFE_ABSORPTION_PERCENT":   43,
		"POWER_MANA_ABSORPTION_PERCENT":   44,
		"POWER_MAGIC_PENETRATION_PERCENT": 45,
		"POWER_HIT_FORCE_PERCENT":         46,
		"POWER_HEAL_RECOVERY_PERCENT":     47,
		"POWER_MANA_RECOVERY_PERCENT":     48,
		"POWER_HP":                        49,
		"POWER_HP_PERCENT":                50,
		"POWER_MP":                        51,
		"POWER_MP_PERCENT":                52,
		"POWER_SPEED":                     53,
		"POWER_HITRATE":                   54,
		"POWER_DODGE":                     55,
		"POWER_MAGIC_HITRATE":             56,
		"POWER_CRITICAL":                  57,
		"POWER_ATK_STR":                   58,
		"POWER_ATK_AGI":                   59,
		"POWER_ATK_MAGIC":                 60,
		"POWER_DEF_STR":                   61,
		"POWER_DEF_AGI":                   62,
		"POWER_DEF_MAGIC":                 63,
		"POWER_WIL":                       64,
		"POWER_TOUGH":                     65,
		"POWER_REFLECTION":                66,
		"POWER_BLOCK":                     67,
		"POWER_INSIGHT":                   68,
		"POWER_PENETRATION":               69,
		"POWER_DEF_FIELD":                 70,
		"POWER_BACK":                      71,
		"POWER_MAGIC_BACK":                72,
		"POWER_LIFE_ABSORPTION":           73,
		"POWER_MANA_ABSORPTION":           74,
		"POWER_MAGIC_PENETRATION":         75,
		"POWER_HIT_FORCE":                 76,
		"POWER_HEAL_RECOVERY":             77,
		"POWER_MANA_RECOVERY":             78,
		"POWER_REMOVE_STATUS":             79,
		"POWER_RECOVER":                   80,
		"POWER_SKILL_DAMAGE":              81,
		"POWER_SKILL_HITRATE":             82,
		"POWER_SELF_CRITICAL":             84,
		"POWER_SKILL_HIT_FORCE":           85,
		"POWER_SKILL_MAGIC_PENETRATION":   86,
		"POWER_SKILL_BRK_ARMOR":           87,
		"POWER_SKILL_REMOVE_STATUS":       88,
		"POWER_PET_DAMAGE":                89,
		"POWER_PET_HPMAX_PERCENT":         90,
		"POWER_PET_MPMAX_PERCENT":         91,
		"POWER_PET_STR_PERCENT":           92,
		"POWER_PET_CON_PERCENT":           93,
		"POWER_PET_AGI_PERCENT":           94,
		"POWER_PET_ILT_PERCENT":           95,
		"POWER_PET_WIS_PERCENT":           96,
		"POWER_RECOVER_PERCENT":           97,
		"POWER_HPMP_RECOVER":              98,
		"POWER_OPEN_STORE":                99,
		"POWER_CHEST_LV1":                 100,
		"POWER_CHEST_LV2":                 101,
		"POWER_CHEST_LV3":                 102,
		"POWER_REQ_SLOT":                  103,
		"POWER_TO_WORLDMAP":               104,
		"POWER_TO_GXGY":                   105,
		"POWER_EXPIRE_TIME":               106,
		"POWER_RESET_MISSION":             107,
		"POWER_CHEST_KEY_LEVEL":           108,
		"POWER_PET_EGG":                   109,
		"POWER_COSTUME":                   110,
		"POWER_TRANSPORT":                 111,
		"POWER_POWER_TITLE":               112,
		"POWER_GUARD_STR_ATTACK":          113,
		"POWER_GUARD_AGI_ATTACK":          114,
		"POWER_GUARD_MAGIC_ATTACK":        115,
		"POWER_GUARD_CURSE_ATTACK":        116,
		"POWER_GUARD_ALL_ATTACK":          117,
		"POWER_PET_ADD_EXP":               118,
		"POWER_ADD_EXP":                   119,
		"POWER_EXP_BY_TIME":               120,
		"POWER_IDENTIFY":                  121,
		"POWER_SWORD_ATK_TIME":            122,
		"POWER_BLADE_ATK_TIME":            123,
		"POWER_HEAVY_ATK_TIME":            124,
		"POWER_LANCE_ATK_TIME":            125,
		"POWER_STAFF_ATK_TIME":            126,
		"POWER_HAND_ATK_TIME":             127,
		"POWER_BOW_ATK_TIME":              128,
		"POWER_HAND_ITEM_ATK_TIME":        129,
		"POWER_ALL_ATK_TIME":              130,
		"POWER_COMPOSITE":                 131,
		"POWER_SWORD_PERCENT":             133,
		"POWER_BLADE_PERCENT":             134,
		"POWER_HEAVY_PERCENT":             135,
		"POWER_LANCE_PERCENT":             136,
		"POWER_STAFF_PERCENT":             137,
		"POWER_HAND_PERCENT":              138,
		"POWER_BOW_PERCENT":               139,
		"POWER_HAND_ITEM_PERCENT":         140,
		"POWER_ALL_PERCENT":               141,
		"POWER_EQUIP_ARMOR_DUR_PERCENT":   142,
		"POWER_SKILL_HP":                  145,
		"POWER_SKILL_HP_PERCENT":          146,
		"POWER_SKILL_MP":                  147,
		"POWER_SKILL_MP_PERCENT":          148,
		"POWER_SKILL_LIFE_ABSORPTION":     149,
		"POWER_SKILL_MANA_ABSORPTION":     150,
		"POWER_SKILL_TARGET_BACK":         151,
		"POWER_SKILL_TARGET_MAGIC_BACK":   152,
		"POWER_SKILL_TARGET_BLOCK":        153,
		"POWER_SKILL_TARGET_INSIGHT":      154,
		"POWER_SKILL_TARGET_WIL":          155,
		"POWER_SKILL_TARGET_TOUCH":        156,
		"POWER_GRARD_MASTER_STR_ATTACK":   157,
		"POWER_GRARD_MASTER_AGI_ATTACK":   158,
		"POWER_GRARD_MASTER_MAGIC_ATTACK": 159,
		"POWER_GRARD_MASTER_CURSE_ATTACK": 160,
		"POWER_GRARD_MASTER_ALL_ATTACK":   161,
		"POWER_PET_GRARD_STR_ATTACK":      162,
		"POWER_PET_GRARD_AGI_ATTACK":      163,
		"POWER_PET_GRARD_MAGIC_ATTACK":    164,
		"POWER_PET_GRARD_CURSE_ATTACK":    165,
		"POWER_PET_GRARD_ALL_ATTACK":      166,
		"POWER_EXP_MISSION_BY_TIME":       167,
		"POWER_IGNORE_BACK":               168,
		"POWER_IGNORE_MAGIC_BACK":         169,
		"POWER_IGNORE_BLOCK":              170,
		"POWER_IGNORE_INSIGHT":            171,
		"POWER_IGNORE_WIL":                172,
		"POWER_IGNORE_TOUCH":              173,
		"POWER_BALL_ATK_TIME":             174,
		"POWER_BALL_PERCENT":              175,
		"POWER_SKILL_SCROLL":              176,
		"POWER_SKILL_SCROLL_PET":          177,
		"POWER_KEEPOUT_ATK_TIME":          178,
		"POWER_NEW_GET_PET":               179,
		"POWER_NEW_GET_ITEM":              180,
		"POWER_ENCHANT_ITEM":              181,
		"POWER_FORMATION_BOOK":            182,
		"POWER_CHEST_LV4":                 183,
		"POWER_COLOR_BOX":                 184,
		"POWER_TURN_MONSTER_CARD":         185,
		"POWER_SKILL_BOOK_PET":            186,
		"POWER_GUN_ATK_TIME":              190,
		"POWER_GUN_PERCENT":               191,
		"POWER_HAMMER_ATK_TIME":           192,
		"POWER_HAMMER_PERCENT":            193,
		"POWER_FAN_ATK_TIME":              194,
		"POWER_FAN_PERCENT":               195,
		"POWER_MAGIC_PERCENT":             196,
		"POWER_PHYSICS_PERCENT":           197,
		"POWER_HP_MP":                     198,
		"POWER_IGNORE_CRITICAL":           201,
		"POWER_IGNORE_CRITICAL_PERCENT":   202,
		"POWER_CRITICAL_DAMAGE":           203,
		"POWER_CRITICAL_DAMAGE_PERCENT":   204,
		"POWER_BLADE_L_ATK_TIME":          210,
		"POWER_BLADE_H_ATK_TIME":          211,
		"POWER_SWORD_L_ATK_TIME":          212,
		"POWER_SWORD_H_ATK_TIME":          213,
		"POWER_CROSSBOW_L_ATK_TIME":       214,
		"POWER_CROSSBOW_H_ATK_TIME":       215,
		"POWER_ARROW_ATK_TIME":            216,
		"POWER_BLADE_L_DAMAGE_PERCENT":    230,
		"POWER_BLADE_H_DAMAGE_PERCENT":    231,
		"POWER_SWORD_L_DAMAGE_PERCENT":    232,
		"POWER_SWORD_H_DAMAGE_PERCENT":    233,
		"POWER_CROSSBOW_L_DAMAGE_PERCENT": 234,
		"POWER_CROSSBOW_H_DAMAGE_PERCENT": 235,
		"POWER_ARROW_DAMAGE_PERCENT":      236,
		"POWER_DEF_STR_RANGE":             250,
		"POWER_DEF_STR_RANGE_PERCENT":     251,
		"POWER_DEF_STR_NEARBY":            252,
		"POWER_DEF_STR_NEARBY_PERCENT":    253,
		"POWER_DEF_AGI_RANGE":             254,
		"POWER_DEF_AGI_RANGE_PERCENT":     255,
		"POWER_DEF_AGI_NEARBY":            256,
		"POWER_DEF_AGI_NEARBY_PERCENT":    257,
		"POWER_ATK_STR_NEARBY":            258,
		"POWER_ATK_STR_NEARBY_PERCENT":    259,
		"POWER_ATK_STR_RANGE":             260,
		"POWER_ATK_STR_RANGE_PERCENT":     261,
		"POWER_ATK_AGI_NEARBY":            262,
		"POWER_ATK_AGI_NEARBY_PERCENT":    263,
		"POWER_ATK_AGI_RANGE":             264,
		"POWER_ATK_AGI_RANGE_PERCENT":     265,
	}
)

func (x POWER) Enum() *POWER {
	p := new(POWER)
	*p = x
	return p
}

func (x POWER) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (POWER) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_MyDefine_MyDefine_proto_enumTypes[1].Descriptor()
}

func (POWER) Type() protoreflect.EnumType {
	return &file_pbGame_MyDefine_MyDefine_proto_enumTypes[1]
}

func (x POWER) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use POWER.Descriptor instead.
func (POWER) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_MyDefine_MyDefine_proto_rawDescGZIP(), []int{1}
}

var File_pbGame_MyDefine_MyDefine_proto protoreflect.FileDescriptor

var file_pbGame_MyDefine_MyDefine_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x4d, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x2f, 0x4d, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x4d, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65,
	0x2a, 0x61, 0x0a, 0x05, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x6f, 0x6e,
	0x65, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x41, 0x4e, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54,
	0x45, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x0e, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x5f, 0x44, 0x55, 0x52, 0x10, 0xf0, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01,
	0x12, 0x21, 0x0a, 0x14, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x4e,
	0x55, 0x4c, 0x4c, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x10, 0xef, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0x01, 0x2a, 0xfb, 0x2c, 0x0a, 0x05, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x12, 0x0e, 0x0a,
	0x0a, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0d, 0x0a,
	0x09, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x52, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e,
	0x54, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e,
	0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4e, 0x5f,
	0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x41, 0x47, 0x49, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45,
	0x52, 0x5f, 0x41, 0x47, 0x49, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x06, 0x12,
	0x0d, 0x0a, 0x09, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x49, 0x4c, 0x54, 0x10, 0x07, 0x12, 0x15,
	0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x49, 0x4c, 0x54, 0x5f, 0x50, 0x45, 0x52, 0x43,
	0x45, 0x4e, 0x54, 0x10, 0x08, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x57,
	0x49, 0x53, 0x10, 0x09, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x57, 0x49,
	0x53, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x0a, 0x12, 0x0f, 0x0a, 0x0b, 0x50,
	0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x50, 0x4d, 0x41, 0x58, 0x10, 0x0b, 0x12, 0x17, 0x0a, 0x13,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x50, 0x4d, 0x41, 0x58, 0x5f, 0x50, 0x45, 0x52, 0x43,
	0x45, 0x4e, 0x54, 0x10, 0x0c, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4d,
	0x50, 0x4d, 0x41, 0x58, 0x10, 0x0d, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x4d, 0x50, 0x4d, 0x41, 0x58, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x0e, 0x12,
	0x17, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x50, 0x45, 0x45, 0x44, 0x5f, 0x50,
	0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x17, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45,
	0x52, 0x5f, 0x48, 0x49, 0x54, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e,
	0x54, 0x10, 0x18, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x44, 0x4f, 0x44,
	0x47, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x19, 0x12, 0x1f, 0x0a, 0x1b,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x48, 0x49, 0x54, 0x52,
	0x41, 0x54, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x1a, 0x12, 0x1a, 0x0a,
	0x16, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f,
	0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x1b, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45,
	0x4e, 0x54, 0x10, 0x1c, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x41, 0x54,
	0x4b, 0x5f, 0x41, 0x47, 0x49, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x1d, 0x12,
	0x1b, 0x0a, 0x17, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x4d, 0x41, 0x47,
	0x49, 0x43, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x1e, 0x12, 0x19, 0x0a, 0x15,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x46, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x50, 0x45,
	0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x1f, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x44, 0x45, 0x46, 0x5f, 0x41, 0x47, 0x49, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54,
	0x10, 0x20, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x46, 0x5f,
	0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x21, 0x12,
	0x15, 0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x57, 0x49, 0x4c, 0x5f, 0x50, 0x45, 0x52,
	0x43, 0x45, 0x4e, 0x54, 0x10, 0x22, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x54, 0x4f, 0x55, 0x47, 0x48, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x23, 0x12,
	0x1c, 0x0a, 0x18, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x46, 0x4c, 0x45, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x24, 0x12, 0x17, 0x0a,
	0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x5f, 0x50, 0x45, 0x52,
	0x43, 0x45, 0x4e, 0x54, 0x10, 0x25, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x49, 0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10,
	0x26, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x4e, 0x45, 0x54,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x27,
	0x12, 0x1b, 0x0a, 0x17, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x46, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x28, 0x12, 0x16, 0x0a,
	0x12, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x50, 0x45, 0x52, 0x43,
	0x45, 0x4e, 0x54, 0x10, 0x29, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4d,
	0x41, 0x47, 0x49, 0x43, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e,
	0x54, 0x10, 0x2a, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4c, 0x49, 0x46,
	0x45, 0x5f, 0x41, 0x42, 0x53, 0x4f, 0x52, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x52,
	0x43, 0x45, 0x4e, 0x54, 0x10, 0x2b, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x4d, 0x41, 0x4e, 0x41, 0x5f, 0x41, 0x42, 0x53, 0x4f, 0x52, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x2c, 0x12, 0x23, 0x0a, 0x1f, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x50, 0x45, 0x4e, 0x45, 0x54, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x2d, 0x12, 0x1b,
	0x0a, 0x17, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x49, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x43,
	0x45, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x2e, 0x12, 0x1f, 0x0a, 0x1b, 0x50,
	0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45,
	0x52, 0x59, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x2f, 0x12, 0x1f, 0x0a, 0x1b,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56,
	0x45, 0x52, 0x59, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x30, 0x12, 0x0c, 0x0a,
	0x08, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x50, 0x10, 0x31, 0x12, 0x14, 0x0a, 0x10, 0x50,
	0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x50, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10,
	0x32, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4d, 0x50, 0x10, 0x33, 0x12,
	0x14, 0x0a, 0x10, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4d, 0x50, 0x5f, 0x50, 0x45, 0x52, 0x43,
	0x45, 0x4e, 0x54, 0x10, 0x34, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53,
	0x50, 0x45, 0x45, 0x44, 0x10, 0x35, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x48, 0x49, 0x54, 0x52, 0x41, 0x54, 0x45, 0x10, 0x36, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x44, 0x4f, 0x44, 0x47, 0x45, 0x10, 0x37, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f,
	0x57, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x48, 0x49, 0x54, 0x52, 0x41, 0x54,
	0x45, 0x10, 0x38, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x49,
	0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x39, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x53, 0x54, 0x52, 0x10, 0x3a, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4f,
	0x57, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x41, 0x47, 0x49, 0x10, 0x3b, 0x12, 0x13, 0x0a,
	0x0f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43,
	0x10, 0x3c, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x46, 0x5f,
	0x53, 0x54, 0x52, 0x10, 0x3d, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x44,
	0x45, 0x46, 0x5f, 0x41, 0x47, 0x49, 0x10, 0x3e, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x4f, 0x57, 0x45,
	0x52, 0x5f, 0x44, 0x45, 0x46, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x10, 0x3f, 0x12, 0x0d, 0x0a,
	0x09, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x57, 0x49, 0x4c, 0x10, 0x40, 0x12, 0x0f, 0x0a, 0x0b,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x54, 0x4f, 0x55, 0x47, 0x48, 0x10, 0x41, 0x12, 0x14, 0x0a,
	0x10, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x46, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x42, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x42, 0x4c, 0x4f,
	0x43, 0x4b, 0x10, 0x43, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x49, 0x4e,
	0x53, 0x49, 0x47, 0x48, 0x54, 0x10, 0x44, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x50, 0x45, 0x4e, 0x45, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x45, 0x12, 0x13,
	0x0a, 0x0f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x46, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x10, 0x46, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x43,
	0x4b, 0x10, 0x47, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x47,
	0x49, 0x43, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x48, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x4c, 0x49, 0x46, 0x45, 0x5f, 0x41, 0x42, 0x53, 0x4f, 0x52, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x49, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4d, 0x41,
	0x4e, 0x41, 0x5f, 0x41, 0x42, 0x53, 0x4f, 0x52, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x4a, 0x12,
	0x1b, 0x0a, 0x17, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x50,
	0x45, 0x4e, 0x45, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x4b, 0x12, 0x13, 0x0a, 0x0f,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x49, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x10,
	0x4c, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x5f,
	0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x59, 0x10, 0x4d, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f,
	0x57, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52,
	0x59, 0x10, 0x4e, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x4d,
	0x4f, 0x56, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x4f, 0x12, 0x11, 0x0a, 0x0d,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x10, 0x50, 0x12,
	0x16, 0x0a, 0x12, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x44,
	0x41, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x51, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x48, 0x49, 0x54, 0x52, 0x41, 0x54, 0x45, 0x10, 0x52,
	0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x43,
	0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0x54, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x48, 0x49, 0x54, 0x5f, 0x46, 0x4f, 0x52,
	0x43, 0x45, 0x10, 0x55, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x4b,
	0x49, 0x4c, 0x4c, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x50, 0x45, 0x4e, 0x45, 0x54, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x56, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x42, 0x52, 0x4b, 0x5f, 0x41, 0x52, 0x4d, 0x4f, 0x52,
	0x10, 0x57, 0x12, 0x1d, 0x0a, 0x19, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x4b, 0x49, 0x4c,
	0x4c, 0x5f, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10,
	0x58, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x44,
	0x41, 0x4d, 0x41, 0x47, 0x45, 0x10, 0x59, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x50, 0x45, 0x54, 0x5f, 0x48, 0x50, 0x4d, 0x41, 0x58, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45,
	0x4e, 0x54, 0x10, 0x5a, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x45,
	0x54, 0x5f, 0x4d, 0x50, 0x4d, 0x41, 0x58, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10,
	0x5b, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x53,
	0x54, 0x52, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x5c, 0x12, 0x19, 0x0a, 0x15,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x5f, 0x50, 0x45,
	0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x5d, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x50, 0x45, 0x54, 0x5f, 0x41, 0x47, 0x49, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54,
	0x10, 0x5e, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x54, 0x5f,
	0x49, 0x4c, 0x54, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x5f, 0x12, 0x19, 0x0a,
	0x15, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x57, 0x49, 0x53, 0x5f, 0x50,
	0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x60, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45,
	0x52, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e,
	0x54, 0x10, 0x61, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x50, 0x4d,
	0x50, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x56, 0x45, 0x52, 0x10, 0x62, 0x12, 0x14, 0x0a, 0x10, 0x50,
	0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x10,
	0x63, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x45, 0x53, 0x54,
	0x5f, 0x4c, 0x56, 0x31, 0x10, 0x64, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x43, 0x48, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x56, 0x32, 0x10, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x50,
	0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x56, 0x33, 0x10, 0x66,
	0x12, 0x12, 0x0a, 0x0e, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x51, 0x5f, 0x53, 0x4c,
	0x4f, 0x54, 0x10, 0x67, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x54, 0x4f,
	0x5f, 0x57, 0x4f, 0x52, 0x4c, 0x44, 0x4d, 0x41, 0x50, 0x10, 0x68, 0x12, 0x11, 0x0a, 0x0d, 0x50,
	0x4f, 0x57, 0x45, 0x52, 0x5f, 0x54, 0x4f, 0x5f, 0x47, 0x58, 0x47, 0x59, 0x10, 0x69, 0x12, 0x15,
	0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x5f, 0x54,
	0x49, 0x4d, 0x45, 0x10, 0x6a, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x52,
	0x45, 0x53, 0x45, 0x54, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x6b, 0x12, 0x19,
	0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x45, 0x53, 0x54, 0x5f, 0x4b, 0x45,
	0x59, 0x5f, 0x4c, 0x45, 0x56, 0x45, 0x4c, 0x10, 0x6c, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x45, 0x47, 0x47, 0x10, 0x6d, 0x12, 0x11, 0x0a, 0x0d,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x53, 0x54, 0x55, 0x4d, 0x45, 0x10, 0x6e, 0x12,
	0x13, 0x0a, 0x0f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x50, 0x4f,
	0x52, 0x54, 0x10, 0x6f, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x4f,
	0x57, 0x45, 0x52, 0x5f, 0x54, 0x49, 0x54, 0x4c, 0x45, 0x10, 0x70, 0x12, 0x1a, 0x0a, 0x16, 0x50,
	0x4f, 0x57, 0x45, 0x52, 0x5f, 0x47, 0x55, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x41,
	0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0x71, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x47, 0x55, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x47, 0x49, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43,
	0x4b, 0x10, 0x72, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x47, 0x55, 0x41,
	0x52, 0x44, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10,
	0x73, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x47, 0x55, 0x41, 0x52, 0x44,
	0x5f, 0x43, 0x55, 0x52, 0x53, 0x45, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0x74, 0x12,
	0x1a, 0x0a, 0x16, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x47, 0x55, 0x41, 0x52, 0x44, 0x5f, 0x41,
	0x4c, 0x4c, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0x75, 0x12, 0x15, 0x0a, 0x11, 0x50,
	0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x45, 0x58, 0x50,
	0x10, 0x76, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x41, 0x44, 0x44, 0x5f,
	0x45, 0x58, 0x50, 0x10, 0x77, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x45,
	0x58, 0x50, 0x5f, 0x42, 0x59, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x78, 0x12, 0x12, 0x0a, 0x0e,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x59, 0x10, 0x79,
	0x12, 0x18, 0x0a, 0x14, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x5f,
	0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x7a, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x4f,
	0x57, 0x45, 0x52, 0x5f, 0x42, 0x4c, 0x41, 0x44, 0x45, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x10, 0x7b, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x45,
	0x41, 0x56, 0x59, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x7c, 0x12, 0x18,
	0x0a, 0x14, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x41, 0x54,
	0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x7d, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x4f, 0x57, 0x45,
	0x52, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45,
	0x10, 0x7e, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x41, 0x4e, 0x44,
	0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x7f, 0x12, 0x17, 0x0a, 0x12, 0x50,
	0x4f, 0x57, 0x45, 0x52, 0x5f, 0x42, 0x4f, 0x57, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d,
	0x45, 0x10, 0x80, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x41,
	0x4e, 0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45,
	0x10, 0x81, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x41, 0x4c, 0x4c,
	0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x82, 0x01, 0x12, 0x14, 0x0a, 0x0f,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x45, 0x10,
	0x83, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x57, 0x4f, 0x52,
	0x44, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x85, 0x01, 0x12, 0x18, 0x0a, 0x13,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x42, 0x4c, 0x41, 0x44, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x43,
	0x45, 0x4e, 0x54, 0x10, 0x86, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x48, 0x45, 0x41, 0x56, 0x59, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x87, 0x01,
	0x12, 0x18, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f,
	0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x88, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x50, 0x4f,
	0x57, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x46, 0x46, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e,
	0x54, 0x10, 0x89, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x41,
	0x4e, 0x44, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x8a, 0x01, 0x12, 0x16, 0x0a,
	0x11, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x42, 0x4f, 0x57, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45,
	0x4e, 0x54, 0x10, 0x8b, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48,
	0x41, 0x4e, 0x44, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54,
	0x10, 0x8c, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x41, 0x4c, 0x4c,
	0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x8d, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x50,
	0x4f, 0x57, 0x45, 0x52, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x50, 0x5f, 0x41, 0x52, 0x4d, 0x4f, 0x52,
	0x5f, 0x44, 0x55, 0x52, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x8e, 0x01, 0x12,
	0x13, 0x0a, 0x0e, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x48,
	0x50, 0x10, 0x91, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x4b,
	0x49, 0x4c, 0x4c, 0x5f, 0x48, 0x50, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x92,
	0x01, 0x12, 0x13, 0x0a, 0x0e, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x4b, 0x49, 0x4c, 0x4c,
	0x5f, 0x4d, 0x50, 0x10, 0x93, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x4d, 0x50, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54,
	0x10, 0x94, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x4b, 0x49,
	0x4c, 0x4c, 0x5f, 0x4c, 0x49, 0x46, 0x45, 0x5f, 0x41, 0x42, 0x53, 0x4f, 0x52, 0x50, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x95, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53,
	0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x5f, 0x41, 0x42, 0x53, 0x4f, 0x52, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x96, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x42, 0x41,
	0x43, 0x4b, 0x10, 0x97, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53,
	0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x4d, 0x41, 0x47, 0x49,
	0x43, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x98, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f,
	0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x99, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x50, 0x4f, 0x57, 0x45,
	0x52, 0x5f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x49,
	0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x10, 0x9a, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f,
	0x57, 0x49, 0x4c, 0x10, 0x9b, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x4f, 0x55,
	0x43, 0x48, 0x10, 0x9c, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x47,
	0x52, 0x41, 0x52, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x52, 0x5f,
	0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0x9d, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x47, 0x52, 0x41, 0x52, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x54, 0x45, 0x52, 0x5f,
	0x41, 0x47, 0x49, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0x9e, 0x01, 0x12, 0x24, 0x0a,
	0x1f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x47, 0x52, 0x41, 0x52, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x54, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b,
	0x10, 0x9f, 0x01, 0x12, 0x24, 0x0a, 0x1f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x47, 0x52, 0x41,
	0x52, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x43, 0x55, 0x52, 0x53, 0x45, 0x5f,
	0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0xa0, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x47, 0x52, 0x41, 0x52, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x54, 0x45, 0x52, 0x5f,
	0x41, 0x4c, 0x4c, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0xa1, 0x01, 0x12, 0x1f, 0x0a,
	0x1a, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x47, 0x52, 0x41, 0x52, 0x44,
	0x5f, 0x53, 0x54, 0x52, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0xa2, 0x01, 0x12, 0x1f,
	0x0a, 0x1a, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x47, 0x52, 0x41, 0x52,
	0x44, 0x5f, 0x41, 0x47, 0x49, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0xa3, 0x01, 0x12,
	0x21, 0x0a, 0x1c, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x54, 0x5f, 0x47, 0x52, 0x41,
	0x52, 0x44, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10,
	0xa4, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x54, 0x5f,
	0x47, 0x52, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x55, 0x52, 0x53, 0x45, 0x5f, 0x41, 0x54, 0x54, 0x41,
	0x43, 0x4b, 0x10, 0xa5, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50,
	0x45, 0x54, 0x5f, 0x47, 0x52, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x41, 0x54, 0x54,
	0x41, 0x43, 0x4b, 0x10, 0xa6, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x45, 0x58, 0x50, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x59, 0x5f, 0x54,
	0x49, 0x4d, 0x45, 0x10, 0xa7, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0xa8, 0x01, 0x12, 0x1c,
	0x0a, 0x17, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x4d,
	0x41, 0x47, 0x49, 0x43, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x10, 0xa9, 0x01, 0x12, 0x17, 0x0a, 0x12,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x42, 0x4c, 0x4f,
	0x43, 0x4b, 0x10, 0xaa, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x49,
	0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x49, 0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x10, 0xab, 0x01,
	0x12, 0x15, 0x0a, 0x10, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45,
	0x5f, 0x57, 0x49, 0x4c, 0x10, 0xac, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x54, 0x4f, 0x55, 0x43, 0x48, 0x10, 0xad, 0x01,
	0x12, 0x18, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x4c, 0x4c, 0x5f, 0x41,
	0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0xae, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x50, 0x4f,
	0x57, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54,
	0x10, 0xaf, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x4b, 0x49,
	0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x4f, 0x4c, 0x4c, 0x10, 0xb0, 0x01, 0x12, 0x1b, 0x0a, 0x16,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x4f,
	0x4c, 0x4c, 0x5f, 0x50, 0x45, 0x54, 0x10, 0xb1, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x4b, 0x45, 0x45, 0x50, 0x4f, 0x55, 0x54, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54,
	0x49, 0x4d, 0x45, 0x10, 0xb2, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x4e, 0x45, 0x57, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x50, 0x45, 0x54, 0x10, 0xb3, 0x01, 0x12, 0x17,
	0x0a, 0x12, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x47, 0x45, 0x54, 0x5f,
	0x49, 0x54, 0x45, 0x4d, 0x10, 0xb4, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x45, 0x4e, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x10, 0xb5, 0x01,
	0x12, 0x19, 0x0a, 0x14, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x10, 0xb6, 0x01, 0x12, 0x14, 0x0a, 0x0f, 0x50,
	0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x45, 0x53, 0x54, 0x5f, 0x4c, 0x56, 0x34, 0x10, 0xb7,
	0x01, 0x12, 0x14, 0x0a, 0x0f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4c, 0x4f, 0x52,
	0x5f, 0x42, 0x4f, 0x58, 0x10, 0xb8, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x4d, 0x4f, 0x4e, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x10, 0xb9, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53,
	0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x50, 0x45, 0x54, 0x10, 0xba, 0x01,
	0x12, 0x17, 0x0a, 0x12, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x47, 0x55, 0x4e, 0x5f, 0x41, 0x54,
	0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0xbe, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x47, 0x55, 0x4e, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0xbf,
	0x01, 0x12, 0x1a, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x41, 0x4d, 0x4d, 0x45,
	0x52, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0xc0, 0x01, 0x12, 0x19, 0x0a,
	0x14, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x41, 0x4d, 0x4d, 0x45, 0x52, 0x5f, 0x50, 0x45,
	0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0xc1, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x50, 0x4f, 0x57, 0x45,
	0x52, 0x5f, 0x46, 0x41, 0x4e, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0xc2,
	0x01, 0x12, 0x16, 0x0a, 0x11, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x4e, 0x5f, 0x50,
	0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0xc3, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54,
	0x10, 0xc4, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x50, 0x48, 0x59,
	0x53, 0x49, 0x43, 0x53, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0xc5, 0x01, 0x12,
	0x10, 0x0a, 0x0b, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x48, 0x50, 0x5f, 0x4d, 0x50, 0x10, 0xc6,
	0x01, 0x12, 0x1a, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x49, 0x47, 0x4e, 0x4f, 0x52,
	0x45, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x10, 0xc9, 0x01, 0x12, 0x22, 0x0a,
	0x1d, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x43, 0x52,
	0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0xca,
	0x01, 0x12, 0x1a, 0x0a, 0x15, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x49,
	0x43, 0x41, 0x4c, 0x5f, 0x44, 0x41, 0x4d, 0x41, 0x47, 0x45, 0x10, 0xcb, 0x01, 0x12, 0x22, 0x0a,
	0x1d, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x49, 0x54, 0x49, 0x43, 0x41, 0x4c, 0x5f,
	0x44, 0x41, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0xcc,
	0x01, 0x12, 0x1b, 0x0a, 0x16, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x42, 0x4c, 0x41, 0x44, 0x45,
	0x5f, 0x4c, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0xd2, 0x01, 0x12, 0x1b,
	0x0a, 0x16, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x42, 0x4c, 0x41, 0x44, 0x45, 0x5f, 0x48, 0x5f,
	0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0xd3, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x50,
	0x4f, 0x57, 0x45, 0x52, 0x5f, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x4c, 0x5f, 0x41, 0x54, 0x4b,
	0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0xd4, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x50, 0x4f, 0x57, 0x45,
	0x52, 0x5f, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x48, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x10, 0xd5, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43,
	0x52, 0x4f, 0x53, 0x53, 0x42, 0x4f, 0x57, 0x5f, 0x4c, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x10, 0xd6, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43,
	0x52, 0x4f, 0x53, 0x53, 0x42, 0x4f, 0x57, 0x5f, 0x48, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x10, 0xd7, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x41,
	0x52, 0x52, 0x4f, 0x57, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0xd8, 0x01,
	0x12, 0x21, 0x0a, 0x1c, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x42, 0x4c, 0x41, 0x44, 0x45, 0x5f,
	0x4c, 0x5f, 0x44, 0x41, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54,
	0x10, 0xe6, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x42, 0x4c, 0x41,
	0x44, 0x45, 0x5f, 0x48, 0x5f, 0x44, 0x41, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x43,
	0x45, 0x4e, 0x54, 0x10, 0xe7, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f,
	0x53, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x4c, 0x5f, 0x44, 0x41, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x50,
	0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0xe8, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x5f, 0x48, 0x5f, 0x44, 0x41, 0x4d, 0x41, 0x47,
	0x45, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0xe9, 0x01, 0x12, 0x24, 0x0a, 0x1f,
	0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x4f, 0x53, 0x53, 0x42, 0x4f, 0x57, 0x5f, 0x4c,
	0x5f, 0x44, 0x41, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10,
	0xea, 0x01, 0x12, 0x24, 0x0a, 0x1f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x43, 0x52, 0x4f, 0x53,
	0x53, 0x42, 0x4f, 0x57, 0x5f, 0x48, 0x5f, 0x44, 0x41, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x50, 0x45,
	0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0xeb, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x50, 0x4f, 0x57, 0x45,
	0x52, 0x5f, 0x41, 0x52, 0x52, 0x4f, 0x57, 0x5f, 0x44, 0x41, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x50,
	0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0xec, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x5f, 0x44, 0x45, 0x46, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45,
	0x10, 0xfa, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x46,
	0x5f, 0x53, 0x54, 0x52, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45,
	0x4e, 0x54, 0x10, 0xfb, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x44,
	0x45, 0x46, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x4e, 0x45, 0x41, 0x52, 0x42, 0x59, 0x10, 0xfc, 0x01,
	0x12, 0x21, 0x0a, 0x1c, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x46, 0x5f, 0x53, 0x54,
	0x52, 0x5f, 0x4e, 0x45, 0x41, 0x52, 0x42, 0x59, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54,
	0x10, 0xfd, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x46,
	0x5f, 0x41, 0x47, 0x49, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x10, 0xfe, 0x01, 0x12, 0x20, 0x0a,
	0x1b, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x46, 0x5f, 0x41, 0x47, 0x49, 0x5f, 0x52,
	0x41, 0x4e, 0x47, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0xff, 0x01, 0x12,
	0x19, 0x0a, 0x14, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x46, 0x5f, 0x41, 0x47, 0x49,
	0x5f, 0x4e, 0x45, 0x41, 0x52, 0x42, 0x59, 0x10, 0x80, 0x02, 0x12, 0x21, 0x0a, 0x1c, 0x50, 0x4f,
	0x57, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x46, 0x5f, 0x41, 0x47, 0x49, 0x5f, 0x4e, 0x45, 0x41, 0x52,
	0x42, 0x59, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x81, 0x02, 0x12, 0x19, 0x0a,
	0x14, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x4e,
	0x45, 0x41, 0x52, 0x42, 0x59, 0x10, 0x82, 0x02, 0x12, 0x21, 0x0a, 0x1c, 0x50, 0x4f, 0x57, 0x45,
	0x52, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x4e, 0x45, 0x41, 0x52, 0x42, 0x59,
	0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x83, 0x02, 0x12, 0x18, 0x0a, 0x13, 0x50,
	0x4f, 0x57, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x52, 0x41, 0x4e,
	0x47, 0x45, 0x10, 0x84, 0x02, 0x12, 0x20, 0x0a, 0x1b, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x41,
	0x54, 0x4b, 0x5f, 0x53, 0x54, 0x52, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x50, 0x45, 0x52,
	0x43, 0x45, 0x4e, 0x54, 0x10, 0x85, 0x02, 0x12, 0x19, 0x0a, 0x14, 0x50, 0x4f, 0x57, 0x45, 0x52,
	0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x41, 0x47, 0x49, 0x5f, 0x4e, 0x45, 0x41, 0x52, 0x42, 0x59, 0x10,
	0x86, 0x02, 0x12, 0x21, 0x0a, 0x1c, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x4b, 0x5f,
	0x41, 0x47, 0x49, 0x5f, 0x4e, 0x45, 0x41, 0x52, 0x42, 0x59, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45,
	0x4e, 0x54, 0x10, 0x87, 0x02, 0x12, 0x18, 0x0a, 0x13, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x41,
	0x54, 0x4b, 0x5f, 0x41, 0x47, 0x49, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x88, 0x02, 0x12,
	0x20, 0x0a, 0x1b, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x41, 0x54, 0x4b, 0x5f, 0x41, 0x47, 0x49,
	0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x10, 0x89,
	0x02, 0x42, 0x27, 0x5a, 0x25, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x4d, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x3b, 0x4d, 0x79, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_pbGame_MyDefine_MyDefine_proto_rawDescOnce sync.Once
	file_pbGame_MyDefine_MyDefine_proto_rawDescData = file_pbGame_MyDefine_MyDefine_proto_rawDesc
)

func file_pbGame_MyDefine_MyDefine_proto_rawDescGZIP() []byte {
	file_pbGame_MyDefine_MyDefine_proto_rawDescOnce.Do(func() {
		file_pbGame_MyDefine_MyDefine_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_MyDefine_MyDefine_proto_rawDescData)
	})
	return file_pbGame_MyDefine_MyDefine_proto_rawDescData
}

var file_pbGame_MyDefine_MyDefine_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pbGame_MyDefine_MyDefine_proto_goTypes = []interface{}{
	(Const)(0), // 0: proto.MyDefine.Const
	(POWER)(0), // 1: proto.MyDefine.POWER
}
var file_pbGame_MyDefine_MyDefine_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbGame_MyDefine_MyDefine_proto_init() }
func file_pbGame_MyDefine_MyDefine_proto_init() {
	if File_pbGame_MyDefine_MyDefine_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_MyDefine_MyDefine_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_MyDefine_MyDefine_proto_goTypes,
		DependencyIndexes: file_pbGame_MyDefine_MyDefine_proto_depIdxs,
		EnumInfos:         file_pbGame_MyDefine_MyDefine_proto_enumTypes,
	}.Build()
	File_pbGame_MyDefine_MyDefine_proto = out.File
	file_pbGame_MyDefine_MyDefine_proto_rawDesc = nil
	file_pbGame_MyDefine_MyDefine_proto_goTypes = nil
	file_pbGame_MyDefine_MyDefine_proto_depIdxs = nil
}
