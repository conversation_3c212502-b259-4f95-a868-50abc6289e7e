// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbGame/type.proto

package pbGame

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 攻击type定义 攻击方式
type AtkType int32

const (
	Str      AtkType = 0 //近身劈砍
	RangeStr AtkType = 1 //远程劈砍
	Agi      AtkType = 2 //近身穿刺
	RangeAgi AtkType = 3 //远程穿刺
	Magic    AtkType = 4 //魔法
	Curse    AtkType = 5 //诅咒
	Bless    AtkType = 6 //祝福
)

// Enum value maps for AtkType.
var (
	AtkType_name = map[int32]string{
		0: "Str",
		1: "RangeStr",
		2: "Agi",
		3: "RangeAgi",
		4: "Magic",
		5: "Curse",
		6: "Bless",
	}
	AtkType_value = map[string]int32{
		"Str":      0,
		"RangeStr": 1,
		"Agi":      2,
		"RangeAgi": 3,
		"Magic":    4,
		"Curse":    5,
		"Bless":    6,
	}
)

func (x AtkType) Enum() *AtkType {
	p := new(AtkType)
	*p = x
	return p
}

func (x AtkType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AtkType) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_type_proto_enumTypes[0].Descriptor()
}

func (AtkType) Type() protoreflect.EnumType {
	return &file_pbGame_type_proto_enumTypes[0]
}

func (x AtkType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AtkType.Descriptor instead.
func (AtkType) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_type_proto_rawDescGZIP(), []int{0}
}

// 货币定义

type CurrencyType int32

const (
	CurrencyTypeNone CurrencyType = 0  //自动添加
	Money1__1        CurrencyType = -1 //黄金
	Money2__2        CurrencyType = -2 //金叶
	Money3__3        CurrencyType = -3 //铜币
	Exp__4           CurrencyType = -4 //经验值
	Active__5        CurrencyType = -5 //活跃度
	TOWER__6         CurrencyType = -6 //爬塔货币
)

// Enum value maps for CurrencyType.
var (
	CurrencyType_name = map[int32]string{
		0:  "CurrencyTypeNone",
		-1: "Money1__1",
		-2: "Money2__2",
		-3: "Money3__3",
		-4: "Exp__4",
		-5: "Active__5",
		-6: "TOWER__6",
	}
	CurrencyType_value = map[string]int32{
		"CurrencyTypeNone": 0,
		"Money1__1":        -1,
		"Money2__2":        -2,
		"Money3__3":        -3,
		"Exp__4":           -4,
		"Active__5":        -5,
		"TOWER__6":         -6,
	}
)

func (x CurrencyType) Enum() *CurrencyType {
	p := new(CurrencyType)
	*p = x
	return p
}

func (x CurrencyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CurrencyType) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_type_proto_enumTypes[1].Descriptor()
}

func (CurrencyType) Type() protoreflect.EnumType {
	return &file_pbGame_type_proto_enumTypes[1]
}

func (x CurrencyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CurrencyType.Descriptor instead.
func (CurrencyType) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_type_proto_rawDescGZIP(), []int{1}
}

var File_pbGame_type_proto protoreflect.FileDescriptor

var file_pbGame_type_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2a, 0x58, 0x0a, 0x07, 0x41, 0x74,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x53, 0x74, 0x72, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x74, 0x72, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03,
	0x41, 0x67, 0x69, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x41, 0x67,
	0x69, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x10, 0x04, 0x12, 0x09,
	0x0a, 0x05, 0x43, 0x75, 0x72, 0x73, 0x65, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x6c, 0x65,
	0x73, 0x73, 0x10, 0x06, 0x2a, 0xb0, 0x01, 0x0a, 0x0c, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x09, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x31, 0x5f, 0x5f, 0x31, 0x10, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0x01, 0x12, 0x16, 0x0a, 0x09, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x32, 0x5f, 0x5f, 0x32,
	0x10, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x16, 0x0a, 0x09, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x33, 0x5f, 0x5f, 0x33, 0x10, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0x01, 0x12, 0x13, 0x0a, 0x06, 0x45, 0x78, 0x70, 0x5f, 0x5f, 0x34, 0x10, 0xfc, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x12, 0x16, 0x0a, 0x09, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x5f, 0x5f, 0x35, 0x10, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01,
	0x12, 0x15, 0x0a, 0x08, 0x54, 0x4f, 0x57, 0x45, 0x52, 0x5f, 0x5f, 0x36, 0x10, 0xfa, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x01, 0x42, 0x1c, 0x5a, 0x1a, 0x77, 0x6f, 0x72, 0x6c, 0x64,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x3b, 0x70,
	0x62, 0x47, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_type_proto_rawDescOnce sync.Once
	file_pbGame_type_proto_rawDescData = file_pbGame_type_proto_rawDesc
)

func file_pbGame_type_proto_rawDescGZIP() []byte {
	file_pbGame_type_proto_rawDescOnce.Do(func() {
		file_pbGame_type_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_type_proto_rawDescData)
	})
	return file_pbGame_type_proto_rawDescData
}

var file_pbGame_type_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_pbGame_type_proto_goTypes = []interface{}{
	(AtkType)(0),      // 0: proto.AtkType
	(CurrencyType)(0), // 1: proto.CurrencyType
}
var file_pbGame_type_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbGame_type_proto_init() }
func file_pbGame_type_proto_init() {
	if File_pbGame_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_type_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_type_proto_goTypes,
		DependencyIndexes: file_pbGame_type_proto_depIdxs,
		EnumInfos:         file_pbGame_type_proto_enumTypes,
	}.Build()
	File_pbGame_type_proto = out.File
	file_pbGame_type_proto_rawDesc = nil
	file_pbGame_type_proto_goTypes = nil
	file_pbGame_type_proto_depIdxs = nil
}
