// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbGame/ConditionType/ConditionType.proto

package ConditionType

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// 条件定义
type Type int32

const (
	None         Type = 0  //自动添加
	Level        Type = 1  //等级
	KillMonster  Type = 2  //杀怪数
	Money1       Type = 3  //黄金
	Money2       Type = 4  //金叶
	Money3       Type = 5  //铜币
	Honor        Type = 6  //荣誉
	HaveItem     Type = 7  //物品
	EquipItem    Type = 8  //装备物品
	MissionDone  Type = 9  //完成任务
	MissionDoing Type = 10 //接受任务（当前持有某任务）
	PlayerRace   Type = 11 //阵营
	InMap        Type = 12 //在某个地图
	AfterDate    Type = 13 //某个日期之后
	AfterTime    Type = 14 //某个小时之后（每天）
	PlayerSex    Type = 15 //性别
	JoinCountry  Type = 16 //加入国家
	HaveBuff     Type = 17 //持有Buff
	PlayerHp     Type = 18 //生命值达到
	PlayerMp     Type = 19 //法力值达到
	PlayerCon    Type = 20 //体质
	PlayerStr    Type = 21 //力量
	PlayerIlt    Type = 22 //智力
	PlayerAgi    Type = 23 //敏捷
	Vip          Type = 24 //vip等级
	CityLevel    Type = 27 //城市等级
	CityBranch   Type = 28 //完成城市任务
	CityDegree   Type = 29 //城市繁荣度
	CityArmy     Type = 30 //城市军力
	PlayerJob    Type = 32 //职业
	CountryRank  Type = 33 //国家职位是xxx
	CountryRank2 Type = 34 //国家职位达到xxx
	PlayerExp    Type = 35 //经验值
)

// Enum value maps for Type.
var (
	Type_name = map[int32]string{
		0:  "None",
		1:  "Level",
		2:  "KillMonster",
		3:  "Money1",
		4:  "Money2",
		5:  "Money3",
		6:  "Honor",
		7:  "HaveItem",
		8:  "EquipItem",
		9:  "MissionDone",
		10: "MissionDoing",
		11: "PlayerRace",
		12: "InMap",
		13: "AfterDate",
		14: "AfterTime",
		15: "PlayerSex",
		16: "JoinCountry",
		17: "HaveBuff",
		18: "PlayerHp",
		19: "PlayerMp",
		20: "PlayerCon",
		21: "PlayerStr",
		22: "PlayerIlt",
		23: "PlayerAgi",
		24: "Vip",
		27: "CityLevel",
		28: "CityBranch",
		29: "CityDegree",
		30: "CityArmy",
		32: "PlayerJob",
		33: "CountryRank",
		34: "CountryRank2",
		35: "PlayerExp",
	}
	Type_value = map[string]int32{
		"None":         0,
		"Level":        1,
		"KillMonster":  2,
		"Money1":       3,
		"Money2":       4,
		"Money3":       5,
		"Honor":        6,
		"HaveItem":     7,
		"EquipItem":    8,
		"MissionDone":  9,
		"MissionDoing": 10,
		"PlayerRace":   11,
		"InMap":        12,
		"AfterDate":    13,
		"AfterTime":    14,
		"PlayerSex":    15,
		"JoinCountry":  16,
		"HaveBuff":     17,
		"PlayerHp":     18,
		"PlayerMp":     19,
		"PlayerCon":    20,
		"PlayerStr":    21,
		"PlayerIlt":    22,
		"PlayerAgi":    23,
		"Vip":          24,
		"CityLevel":    27,
		"CityBranch":   28,
		"CityDegree":   29,
		"CityArmy":     30,
		"PlayerJob":    32,
		"CountryRank":  33,
		"CountryRank2": 34,
		"PlayerExp":    35,
	}
)

func (x Type) Enum() *Type {
	p := new(Type)
	*p = x
	return p
}

func (x Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type) Descriptor() protoreflect.EnumDescriptor {
	return file_pbGame_ConditionType_ConditionType_proto_enumTypes[0].Descriptor()
}

func (Type) Type() protoreflect.EnumType {
	return &file_pbGame_ConditionType_ConditionType_proto_enumTypes[0]
}

func (x Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type.Descriptor instead.
func (Type) EnumDescriptor() ([]byte, []int) {
	return file_pbGame_ConditionType_ConditionType_proto_rawDescGZIP(), []int{0}
}

var File_pbGame_ConditionType_ConditionType_proto protoreflect.FileDescriptor

var file_pbGame_ConditionType_ConditionType_proto_rawDesc = []byte{
	0x0a, 0x28, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x2f, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x2a,
	0xe1, 0x03, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x6f, 0x6e, 0x65,
	0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x10, 0x01, 0x12, 0x0f, 0x0a,
	0x0b, 0x4b, 0x69, 0x6c, 0x6c, 0x4d, 0x6f, 0x6e, 0x73, 0x74, 0x65, 0x72, 0x10, 0x02, 0x12, 0x0a,
	0x0a, 0x06, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x31, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x32, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x33,
	0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x48, 0x6f, 0x6e, 0x6f, 0x72, 0x10, 0x06, 0x12, 0x0c, 0x0a,
	0x08, 0x48, 0x61, 0x76, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x10, 0x07, 0x12, 0x0d, 0x0a, 0x09, 0x45,
	0x71, 0x75, 0x69, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x10, 0x08, 0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x6e, 0x65, 0x10, 0x09, 0x12, 0x10, 0x0a, 0x0c, 0x4d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x69, 0x6e, 0x67, 0x10, 0x0a, 0x12, 0x0e, 0x0a,
	0x0a, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x61, 0x63, 0x65, 0x10, 0x0b, 0x12, 0x09, 0x0a,
	0x05, 0x49, 0x6e, 0x4d, 0x61, 0x70, 0x10, 0x0c, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x66, 0x74, 0x65,
	0x72, 0x44, 0x61, 0x74, 0x65, 0x10, 0x0d, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x66, 0x74, 0x65, 0x72,
	0x54, 0x69, 0x6d, 0x65, 0x10, 0x0e, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x53, 0x65, 0x78, 0x10, 0x0f, 0x12, 0x0f, 0x0a, 0x0b, 0x4a, 0x6f, 0x69, 0x6e, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x72, 0x79, 0x10, 0x10, 0x12, 0x0c, 0x0a, 0x08, 0x48, 0x61, 0x76, 0x65, 0x42, 0x75,
	0x66, 0x66, 0x10, 0x11, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x48, 0x70,
	0x10, 0x12, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x70, 0x10, 0x13,
	0x12, 0x0d, 0x0a, 0x09, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x10, 0x14, 0x12,
	0x0d, 0x0a, 0x09, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x53, 0x74, 0x72, 0x10, 0x15, 0x12, 0x0d,
	0x0a, 0x09, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6c, 0x74, 0x10, 0x16, 0x12, 0x0d, 0x0a,
	0x09, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x41, 0x67, 0x69, 0x10, 0x17, 0x12, 0x07, 0x0a, 0x03,
	0x56, 0x69, 0x70, 0x10, 0x18, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x69, 0x74, 0x79, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x10, 0x1b, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x69, 0x74, 0x79, 0x42, 0x72, 0x61, 0x6e,
	0x63, 0x68, 0x10, 0x1c, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x69, 0x74, 0x79, 0x44, 0x65, 0x67, 0x72,
	0x65, 0x65, 0x10, 0x1d, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x69, 0x74, 0x79, 0x41, 0x72, 0x6d, 0x79,
	0x10, 0x1e, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4a, 0x6f, 0x62, 0x10,
	0x20, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x61, 0x6e, 0x6b,
	0x10, 0x21, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x61, 0x6e,
	0x6b, 0x32, 0x10, 0x22, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x45, 0x78,
	0x70, 0x10, 0x23, 0x42, 0x31, 0x5a, 0x2f, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x43, 0x6f, 0x6e, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x3b, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_ConditionType_ConditionType_proto_rawDescOnce sync.Once
	file_pbGame_ConditionType_ConditionType_proto_rawDescData = file_pbGame_ConditionType_ConditionType_proto_rawDesc
)

func file_pbGame_ConditionType_ConditionType_proto_rawDescGZIP() []byte {
	file_pbGame_ConditionType_ConditionType_proto_rawDescOnce.Do(func() {
		file_pbGame_ConditionType_ConditionType_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_ConditionType_ConditionType_proto_rawDescData)
	})
	return file_pbGame_ConditionType_ConditionType_proto_rawDescData
}

var file_pbGame_ConditionType_ConditionType_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_pbGame_ConditionType_ConditionType_proto_goTypes = []interface{}{
	(Type)(0), // 0: proto.ConditionType.Type
}
var file_pbGame_ConditionType_ConditionType_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbGame_ConditionType_ConditionType_proto_init() }
func file_pbGame_ConditionType_ConditionType_proto_init() {
	if File_pbGame_ConditionType_ConditionType_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_ConditionType_ConditionType_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_ConditionType_ConditionType_proto_goTypes,
		DependencyIndexes: file_pbGame_ConditionType_ConditionType_proto_depIdxs,
		EnumInfos:         file_pbGame_ConditionType_ConditionType_proto_enumTypes,
	}.Build()
	File_pbGame_ConditionType_ConditionType_proto = out.File
	file_pbGame_ConditionType_ConditionType_proto_rawDesc = nil
	file_pbGame_ConditionType_ConditionType_proto_goTypes = nil
	file_pbGame_ConditionType_ConditionType_proto_depIdxs = nil
}
