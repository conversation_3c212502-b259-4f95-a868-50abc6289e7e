// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbGame/pet.proto

package pbGame

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	Response "world/common/pbBase/Response"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are messages.
type C2S_PetAddSkillSureMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PetId int64 `protobuf:"varint,1,opt,name=petId,proto3" json:"petId,omitempty"` //宠物id
}

func (x *C2S_PetAddSkillSureMessage) Reset() {
	*x = C2S_PetAddSkillSureMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_pet_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PetAddSkillSureMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PetAddSkillSureMessage) ProtoMessage() {}

func (x *C2S_PetAddSkillSureMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_pet_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PetAddSkillSureMessage.ProtoReflect.Descriptor instead.
func (*C2S_PetAddSkillSureMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_pet_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_PetAddSkillSureMessage) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

type S2C_PetAddSkillSureMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_PetAddSkillSureMessage) Reset() {
	*x = S2C_PetAddSkillSureMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_pet_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PetAddSkillSureMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PetAddSkillSureMessage) ProtoMessage() {}

func (x *S2C_PetAddSkillSureMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_pet_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PetAddSkillSureMessage.ProtoReflect.Descriptor instead.
func (*S2C_PetAddSkillSureMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_pet_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_PetAddSkillSureMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_PetSealMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SlotPos int32 `protobuf:"varint,1,opt,name=slotPos,proto3" json:"slotPos,omitempty"` //位置
	PetId   int64 `protobuf:"varint,2,opt,name=petId,proto3" json:"petId,omitempty"`     //宠物id
	Type    int32 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`       //封印类型
}

func (x *C2S_PetSealMessage) Reset() {
	*x = C2S_PetSealMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_pet_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PetSealMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PetSealMessage) ProtoMessage() {}

func (x *C2S_PetSealMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_pet_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PetSealMessage.ProtoReflect.Descriptor instead.
func (*C2S_PetSealMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_pet_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_PetSealMessage) GetSlotPos() int32 {
	if x != nil {
		return x.SlotPos
	}
	return 0
}

func (x *C2S_PetSealMessage) GetPetId() int64 {
	if x != nil {
		return x.PetId
	}
	return 0
}

func (x *C2S_PetSealMessage) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type S2C_PetSealMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	Book *ItemData     `protobuf:"bytes,2,opt,name=book,proto3" json:"book,omitempty"`                           //封印成功返回的技能书物品
}

func (x *S2C_PetSealMessage) Reset() {
	*x = S2C_PetSealMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_pet_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PetSealMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PetSealMessage) ProtoMessage() {}

func (x *S2C_PetSealMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_pet_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PetSealMessage.ProtoReflect.Descriptor instead.
func (*S2C_PetSealMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_pet_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_PetSealMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_PetSealMessage) GetBook() *ItemData {
	if x != nil {
		return x.Book
	}
	return nil
}

type C2S_PetSkillBookLearnMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SlotPos        int32 `protobuf:"varint,1,opt,name=slotPos,proto3" json:"slotPos,omitempty"`               //技能书位置
	PetItemSlotPos int32 `protobuf:"varint,2,opt,name=petItemSlotPos,proto3" json:"petItemSlotPos,omitempty"` //宠物物品位置
}

func (x *C2S_PetSkillBookLearnMessage) Reset() {
	*x = C2S_PetSkillBookLearnMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_pet_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_PetSkillBookLearnMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_PetSkillBookLearnMessage) ProtoMessage() {}

func (x *C2S_PetSkillBookLearnMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_pet_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_PetSkillBookLearnMessage.ProtoReflect.Descriptor instead.
func (*C2S_PetSkillBookLearnMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_pet_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_PetSkillBookLearnMessage) GetSlotPos() int32 {
	if x != nil {
		return x.SlotPos
	}
	return 0
}

func (x *C2S_PetSkillBookLearnMessage) GetPetItemSlotPos() int32 {
	if x != nil {
		return x.PetItemSlotPos
	}
	return 0
}

type S2C_PetSkillBookLearnMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	Skill *SkillInfo    `protobuf:"bytes,2,opt,name=skill,proto3" json:"skill,omitempty"`                         //学习后的技能
}

func (x *S2C_PetSkillBookLearnMessage) Reset() {
	*x = S2C_PetSkillBookLearnMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_pet_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_PetSkillBookLearnMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_PetSkillBookLearnMessage) ProtoMessage() {}

func (x *S2C_PetSkillBookLearnMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_pet_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_PetSkillBookLearnMessage.ProtoReflect.Descriptor instead.
func (*S2C_PetSkillBookLearnMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_pet_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_PetSkillBookLearnMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_PetSkillBookLearnMessage) GetSkill() *SkillInfo {
	if x != nil {
		return x.Skill
	}
	return nil
}

var File_pbGame_pet_proto protoreflect.FileDescriptor

var file_pbGame_pet_proto_rawDesc = []byte{
	0x0a, 0x10, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x70, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x42, 0x61, 0x73,
	0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x70, 0x62, 0x47, 0x61, 0x6d,
	0x65, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x32,
	0x0a, 0x1a, 0x43, 0x32, 0x53, 0x5f, 0x50, 0x65, 0x74, 0x41, 0x64, 0x64, 0x53, 0x6b, 0x69, 0x6c,
	0x6c, 0x53, 0x75, 0x72, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x65, 0x74, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74,
	0x49, 0x64, 0x22, 0x46, 0x0a, 0x1a, 0x53, 0x32, 0x43, 0x5f, 0x50, 0x65, 0x74, 0x41, 0x64, 0x64,
	0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x53, 0x75, 0x72, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x58, 0x0a, 0x12, 0x43, 0x32,
	0x53, 0x5f, 0x50, 0x65, 0x74, 0x53, 0x65, 0x61, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x6c, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x73, 0x6c, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x65,
	0x74, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x70, 0x65, 0x74, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x63, 0x0a, 0x12, 0x53, 0x32, 0x43, 0x5f, 0x50, 0x65, 0x74, 0x53,
	0x65, 0x61, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x62, 0x6f, 0x6f, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x04, 0x62, 0x6f, 0x6f, 0x6b, 0x22, 0x60, 0x0a, 0x1c, 0x43, 0x32, 0x53,
	0x5f, 0x50, 0x65, 0x74, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x42, 0x6f, 0x6f, 0x6b, 0x4c, 0x65, 0x61,
	0x72, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x6c, 0x6f,
	0x74, 0x50, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x6c, 0x6f, 0x74,
	0x50, 0x6f, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x70, 0x65, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x6c,
	0x6f, 0x74, 0x50, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x70, 0x65, 0x74,
	0x49, 0x74, 0x65, 0x6d, 0x53, 0x6c, 0x6f, 0x74, 0x50, 0x6f, 0x73, 0x22, 0x70, 0x0a, 0x1c, 0x53,
	0x32, 0x43, 0x5f, 0x50, 0x65, 0x74, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x42, 0x6f, 0x6f, 0x6b, 0x4c,
	0x65, 0x61, 0x72, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x6b, 0x69,
	0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x42, 0x1c, 0x5a,
	0x1a, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62,
	0x47, 0x61, 0x6d, 0x65, 0x3b, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_pbGame_pet_proto_rawDescOnce sync.Once
	file_pbGame_pet_proto_rawDescData = file_pbGame_pet_proto_rawDesc
)

func file_pbGame_pet_proto_rawDescGZIP() []byte {
	file_pbGame_pet_proto_rawDescOnce.Do(func() {
		file_pbGame_pet_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_pet_proto_rawDescData)
	})
	return file_pbGame_pet_proto_rawDescData
}

var file_pbGame_pet_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_pbGame_pet_proto_goTypes = []interface{}{
	(*C2S_PetAddSkillSureMessage)(nil),   // 0: proto.C2S_PetAddSkillSureMessage
	(*S2C_PetAddSkillSureMessage)(nil),   // 1: proto.S2C_PetAddSkillSureMessage
	(*C2S_PetSealMessage)(nil),           // 2: proto.C2S_PetSealMessage
	(*S2C_PetSealMessage)(nil),           // 3: proto.S2C_PetSealMessage
	(*C2S_PetSkillBookLearnMessage)(nil), // 4: proto.C2S_PetSkillBookLearnMessage
	(*S2C_PetSkillBookLearnMessage)(nil), // 5: proto.S2C_PetSkillBookLearnMessage
	(Response.Code)(0),                   // 6: proto.Response.Code
	(*ItemData)(nil),                     // 7: proto.ItemData
	(*SkillInfo)(nil),                    // 8: proto.SkillInfo
}
var file_pbGame_pet_proto_depIdxs = []int32{
	6, // 0: proto.S2C_PetAddSkillSureMessage.code:type_name -> proto.Response.Code
	6, // 1: proto.S2C_PetSealMessage.code:type_name -> proto.Response.Code
	7, // 2: proto.S2C_PetSealMessage.book:type_name -> proto.ItemData
	6, // 3: proto.S2C_PetSkillBookLearnMessage.code:type_name -> proto.Response.Code
	8, // 4: proto.S2C_PetSkillBookLearnMessage.skill:type_name -> proto.SkillInfo
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_pbGame_pet_proto_init() }
func file_pbGame_pet_proto_init() {
	if File_pbGame_pet_proto != nil {
		return
	}
	file_pbGame_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pbGame_pet_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PetAddSkillSureMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_pet_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PetAddSkillSureMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_pet_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PetSealMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_pet_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PetSealMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_pet_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_PetSkillBookLearnMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_pet_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_PetSkillBookLearnMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_pet_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_pet_proto_goTypes,
		DependencyIndexes: file_pbGame_pet_proto_depIdxs,
		MessageInfos:      file_pbGame_pet_proto_msgTypes,
	}.Build()
	File_pbGame_pet_proto = out.File
	file_pbGame_pet_proto_rawDesc = nil
	file_pbGame_pet_proto_goTypes = nil
	file_pbGame_pet_proto_depIdxs = nil
}
