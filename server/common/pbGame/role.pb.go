// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbGame/role.proto

package pbGame

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	Response "world/common/pbBase/Response"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are messages.
type C2S_ActorAttributeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info map[int32]int32 `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` //加点详情
}

func (x *C2S_ActorAttributeMessage) Reset() {
	*x = C2S_ActorAttributeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_role_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ActorAttributeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ActorAttributeMessage) ProtoMessage() {}

func (x *C2S_ActorAttributeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_role_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ActorAttributeMessage.ProtoReflect.Descriptor instead.
func (*C2S_ActorAttributeMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_role_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_ActorAttributeMessage) GetInfo() map[int32]int32 {
	if x != nil {
		return x.Info
	}
	return nil
}

type S2C_ActorAttributeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	Cp     int32         `protobuf:"varint,2,opt,name=cp,proto3" json:"cp,omitempty"`                              //未分配属性点
	Hp     int32         `protobuf:"varint,3,opt,name=hp,proto3" json:"hp,omitempty"`                              //血
	Mp     int32         `protobuf:"varint,4,opt,name=mp,proto3" json:"mp,omitempty"`                              //蓝
	Str    int32         `protobuf:"varint,5,opt,name=str,proto3" json:"str,omitempty"`                            //力量
	Con    int32         `protobuf:"varint,6,opt,name=con,proto3" json:"con,omitempty"`                            //体质
	Agi    int32         `protobuf:"varint,7,opt,name=agi,proto3" json:"agi,omitempty"`                            //敏捷
	Ilt    int32         `protobuf:"varint,8,opt,name=ilt,proto3" json:"ilt,omitempty"`                            //智力
	Wis    int32         `protobuf:"varint,9,opt,name=wis,proto3" json:"wis,omitempty"`                            //感知
	Money1 int32         `protobuf:"varint,10,opt,name=money1,proto3" json:"money1,omitempty"`                     //黄金
	Money2 int32         `protobuf:"varint,11,opt,name=money2,proto3" json:"money2,omitempty"`                     //金叶
	Money3 int32         `protobuf:"varint,12,opt,name=money3,proto3" json:"money3,omitempty"`                     //铜币
}

func (x *S2C_ActorAttributeMessage) Reset() {
	*x = S2C_ActorAttributeMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_role_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ActorAttributeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ActorAttributeMessage) ProtoMessage() {}

func (x *S2C_ActorAttributeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_role_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ActorAttributeMessage.ProtoReflect.Descriptor instead.
func (*S2C_ActorAttributeMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_role_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_ActorAttributeMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_ActorAttributeMessage) GetCp() int32 {
	if x != nil {
		return x.Cp
	}
	return 0
}

func (x *S2C_ActorAttributeMessage) GetHp() int32 {
	if x != nil {
		return x.Hp
	}
	return 0
}

func (x *S2C_ActorAttributeMessage) GetMp() int32 {
	if x != nil {
		return x.Mp
	}
	return 0
}

func (x *S2C_ActorAttributeMessage) GetStr() int32 {
	if x != nil {
		return x.Str
	}
	return 0
}

func (x *S2C_ActorAttributeMessage) GetCon() int32 {
	if x != nil {
		return x.Con
	}
	return 0
}

func (x *S2C_ActorAttributeMessage) GetAgi() int32 {
	if x != nil {
		return x.Agi
	}
	return 0
}

func (x *S2C_ActorAttributeMessage) GetIlt() int32 {
	if x != nil {
		return x.Ilt
	}
	return 0
}

func (x *S2C_ActorAttributeMessage) GetWis() int32 {
	if x != nil {
		return x.Wis
	}
	return 0
}

func (x *S2C_ActorAttributeMessage) GetMoney1() int32 {
	if x != nil {
		return x.Money1
	}
	return 0
}

func (x *S2C_ActorAttributeMessage) GetMoney2() int32 {
	if x != nil {
		return x.Money2
	}
	return 0
}

func (x *S2C_ActorAttributeMessage) GetMoney3() int32 {
	if x != nil {
		return x.Money3
	}
	return 0
}

type C2S_LearnSkillByShopMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShopId     int32 `protobuf:"varint,1,opt,name=shopId,proto3" json:"shopId,omitempty"`         //技能商店id
	SkillId    int32 `protobuf:"varint,2,opt,name=skillId,proto3" json:"skillId,omitempty"`       //技能id
	SkillLevel int32 `protobuf:"varint,3,opt,name=skillLevel,proto3" json:"skillLevel,omitempty"` //技能等级
}

func (x *C2S_LearnSkillByShopMessage) Reset() {
	*x = C2S_LearnSkillByShopMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_role_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_LearnSkillByShopMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_LearnSkillByShopMessage) ProtoMessage() {}

func (x *C2S_LearnSkillByShopMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_role_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_LearnSkillByShopMessage.ProtoReflect.Descriptor instead.
func (*C2S_LearnSkillByShopMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_role_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_LearnSkillByShopMessage) GetShopId() int32 {
	if x != nil {
		return x.ShopId
	}
	return 0
}

func (x *C2S_LearnSkillByShopMessage) GetSkillId() int32 {
	if x != nil {
		return x.SkillId
	}
	return 0
}

func (x *C2S_LearnSkillByShopMessage) GetSkillLevel() int32 {
	if x != nil {
		return x.SkillLevel
	}
	return 0
}

type S2C_LearnSkillByShopMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
	Money1 int32         `protobuf:"varint,2,opt,name=money1,proto3" json:"money1,omitempty"`                      //黄金
	Money2 int32         `protobuf:"varint,3,opt,name=money2,proto3" json:"money2,omitempty"`                      //金叶
	Money3 int32         `protobuf:"varint,4,opt,name=money3,proto3" json:"money3,omitempty"`                      //铜币
}

func (x *S2C_LearnSkillByShopMessage) Reset() {
	*x = S2C_LearnSkillByShopMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_role_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_LearnSkillByShopMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_LearnSkillByShopMessage) ProtoMessage() {}

func (x *S2C_LearnSkillByShopMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_role_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_LearnSkillByShopMessage.ProtoReflect.Descriptor instead.
func (*S2C_LearnSkillByShopMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_role_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_LearnSkillByShopMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_LearnSkillByShopMessage) GetMoney1() int32 {
	if x != nil {
		return x.Money1
	}
	return 0
}

func (x *S2C_LearnSkillByShopMessage) GetMoney2() int32 {
	if x != nil {
		return x.Money2
	}
	return 0
}

func (x *S2C_LearnSkillByShopMessage) GetMoney3() int32 {
	if x != nil {
		return x.Money3
	}
	return 0
}

type C2S_AutoSkillSetMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsPet    bool  `protobuf:"varint,1,opt,name=isPet,proto3" json:"isPet,omitempty"`       //是不是设置宠物
	IsActive bool  `protobuf:"varint,2,opt,name=isActive,proto3" json:"isActive,omitempty"` //是不是设置主动技能
	SkillId  int32 `protobuf:"varint,3,opt,name=skillId,proto3" json:"skillId,omitempty"`   //技能id
}

func (x *C2S_AutoSkillSetMessage) Reset() {
	*x = C2S_AutoSkillSetMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_role_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_AutoSkillSetMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_AutoSkillSetMessage) ProtoMessage() {}

func (x *C2S_AutoSkillSetMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_role_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_AutoSkillSetMessage.ProtoReflect.Descriptor instead.
func (*C2S_AutoSkillSetMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_role_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_AutoSkillSetMessage) GetIsPet() bool {
	if x != nil {
		return x.IsPet
	}
	return false
}

func (x *C2S_AutoSkillSetMessage) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *C2S_AutoSkillSetMessage) GetSkillId() int32 {
	if x != nil {
		return x.SkillId
	}
	return 0
}

type S2C_AutoSkillSetMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_AutoSkillSetMessage) Reset() {
	*x = S2C_AutoSkillSetMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_role_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_AutoSkillSetMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_AutoSkillSetMessage) ProtoMessage() {}

func (x *S2C_AutoSkillSetMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_role_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_AutoSkillSetMessage.ProtoReflect.Descriptor instead.
func (*S2C_AutoSkillSetMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_role_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_AutoSkillSetMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

var File_pbGame_role_proto protoreflect.FileDescriptor

var file_pbGame_role_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x72, 0x6f, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62, 0x42, 0x61,
	0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x94, 0x01, 0x0a, 0x19, 0x43,
	0x32, 0x53, 0x5f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x3e, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43,
	0x32, 0x53, 0x5f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x1a, 0x37, 0x0a, 0x09, 0x49, 0x6e, 0x66, 0x6f,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x97, 0x02, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x63, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x63, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x68, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x68, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x6d, 0x70, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6d, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x74, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x73, 0x74, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x63,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x63, 0x6f, 0x6e, 0x12, 0x10, 0x0a,
	0x03, 0x61, 0x67, 0x69, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x67, 0x69, 0x12,
	0x10, 0x0a, 0x03, 0x69, 0x6c, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x69, 0x6c,
	0x74, 0x12, 0x10, 0x0a, 0x03, 0x77, 0x69, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x77, 0x69, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x31, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x31, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x32, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x6e,
	0x65, 0x79, 0x32, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x22, 0x6f, 0x0a, 0x1b, 0x43,
	0x32, 0x53, 0x5f, 0x4c, 0x65, 0x61, 0x72, 0x6e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x42, 0x79, 0x53,
	0x68, 0x6f, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68,
	0x6f, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x68, 0x6f, 0x70,
	0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x73, 0x6b, 0x69, 0x6c, 0x6c, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x8f, 0x01, 0x0a,
	0x1b, 0x53, 0x32, 0x43, 0x5f, 0x4c, 0x65, 0x61, 0x72, 0x6e, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x42,
	0x79, 0x53, 0x68, 0x6f, 0x70, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x31,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x31, 0x12, 0x16,
	0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x32, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x32, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x33, 0x22, 0x65,
	0x0a, 0x17, 0x43, 0x32, 0x53, 0x5f, 0x41, 0x75, 0x74, 0x6f, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x53,
	0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x73, 0x50,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x50, 0x65, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x69, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73,
	0x6b, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x6b,
	0x69, 0x6c, 0x6c, 0x49, 0x64, 0x22, 0x43, 0x0a, 0x17, 0x53, 0x32, 0x43, 0x5f, 0x41, 0x75, 0x74,
	0x6f, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x53, 0x65, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x1c, 0x5a, 0x1a, 0x77, 0x6f,
	0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61, 0x6d,
	0x65, 0x3b, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_role_proto_rawDescOnce sync.Once
	file_pbGame_role_proto_rawDescData = file_pbGame_role_proto_rawDesc
)

func file_pbGame_role_proto_rawDescGZIP() []byte {
	file_pbGame_role_proto_rawDescOnce.Do(func() {
		file_pbGame_role_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_role_proto_rawDescData)
	})
	return file_pbGame_role_proto_rawDescData
}

var file_pbGame_role_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_pbGame_role_proto_goTypes = []interface{}{
	(*C2S_ActorAttributeMessage)(nil),   // 0: proto.C2S_ActorAttributeMessage
	(*S2C_ActorAttributeMessage)(nil),   // 1: proto.S2C_ActorAttributeMessage
	(*C2S_LearnSkillByShopMessage)(nil), // 2: proto.C2S_LearnSkillByShopMessage
	(*S2C_LearnSkillByShopMessage)(nil), // 3: proto.S2C_LearnSkillByShopMessage
	(*C2S_AutoSkillSetMessage)(nil),     // 4: proto.C2S_AutoSkillSetMessage
	(*S2C_AutoSkillSetMessage)(nil),     // 5: proto.S2C_AutoSkillSetMessage
	nil,                                 // 6: proto.C2S_ActorAttributeMessage.InfoEntry
	(Response.Code)(0),                  // 7: proto.Response.Code
}
var file_pbGame_role_proto_depIdxs = []int32{
	6, // 0: proto.C2S_ActorAttributeMessage.info:type_name -> proto.C2S_ActorAttributeMessage.InfoEntry
	7, // 1: proto.S2C_ActorAttributeMessage.code:type_name -> proto.Response.Code
	7, // 2: proto.S2C_LearnSkillByShopMessage.code:type_name -> proto.Response.Code
	7, // 3: proto.S2C_AutoSkillSetMessage.code:type_name -> proto.Response.Code
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_pbGame_role_proto_init() }
func file_pbGame_role_proto_init() {
	if File_pbGame_role_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbGame_role_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ActorAttributeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_role_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ActorAttributeMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_role_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_LearnSkillByShopMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_role_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_LearnSkillByShopMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_role_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_AutoSkillSetMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_role_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_AutoSkillSetMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_role_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_role_proto_goTypes,
		DependencyIndexes: file_pbGame_role_proto_depIdxs,
		MessageInfos:      file_pbGame_role_proto_msgTypes,
	}.Build()
	File_pbGame_role_proto = out.File
	file_pbGame_role_proto_rawDesc = nil
	file_pbGame_role_proto_goTypes = nil
	file_pbGame_role_proto_depIdxs = nil
}
