// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: pbGame/battle.proto

package pbGame

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	Response "world/common/pbBase/Response"
	BattleDefine "world/common/pbGame/BattleDefine"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are messages.
type C2S_BattlePassTestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId int32 `protobuf:"varint,1,opt,name=groupId,proto3" json:"groupId,omitempty"` //战斗id
}

func (x *C2S_BattlePassTestMessage) Reset() {
	*x = C2S_BattlePassTestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_battle_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_BattlePassTestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_BattlePassTestMessage) ProtoMessage() {}

func (x *C2S_BattlePassTestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_battle_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_BattlePassTestMessage.ProtoReflect.Descriptor instead.
func (*C2S_BattlePassTestMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_battle_proto_rawDescGZIP(), []int{0}
}

func (x *C2S_BattlePassTestMessage) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

type S2C_BattlePassTestMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code Response.Code `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"` //响应码
}

func (x *S2C_BattlePassTestMessage) Reset() {
	*x = S2C_BattlePassTestMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_battle_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_BattlePassTestMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_BattlePassTestMessage) ProtoMessage() {}

func (x *S2C_BattlePassTestMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_battle_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_BattlePassTestMessage.ProtoReflect.Descriptor instead.
func (*S2C_BattlePassTestMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_battle_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_BattlePassTestMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

type C2S_RunLocalBattleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Seed    int32 `protobuf:"varint,1,opt,name=seed,proto3" json:"seed,omitempty"`       //种子 必须小于等于10000
	StartHp int32 `protobuf:"varint,2,opt,name=startHp,proto3" json:"startHp,omitempty"` //初始生命值
	StartMp int32 `protobuf:"varint,3,opt,name=startMp,proto3" json:"startMp,omitempty"` //初始法力值
	EndHp   int32 `protobuf:"varint,4,opt,name=endHp,proto3" json:"endHp,omitempty"`     //最终生命值
	EndMp   int32 `protobuf:"varint,5,opt,name=endMp,proto3" json:"endMp,omitempty"`     //最终法力值
	GroupId int32 `protobuf:"varint,6,opt,name=groupId,proto3" json:"groupId,omitempty"` //战斗id
	HasPet  bool  `protobuf:"varint,7,opt,name=hasPet,proto3" json:"hasPet,omitempty"`   //是否携带宠物
}

func (x *C2S_RunLocalBattleMessage) Reset() {
	*x = C2S_RunLocalBattleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_battle_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_RunLocalBattleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_RunLocalBattleMessage) ProtoMessage() {}

func (x *C2S_RunLocalBattleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_battle_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_RunLocalBattleMessage.ProtoReflect.Descriptor instead.
func (*C2S_RunLocalBattleMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_battle_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_RunLocalBattleMessage) GetSeed() int32 {
	if x != nil {
		return x.Seed
	}
	return 0
}

func (x *C2S_RunLocalBattleMessage) GetStartHp() int32 {
	if x != nil {
		return x.StartHp
	}
	return 0
}

func (x *C2S_RunLocalBattleMessage) GetStartMp() int32 {
	if x != nil {
		return x.StartMp
	}
	return 0
}

func (x *C2S_RunLocalBattleMessage) GetEndHp() int32 {
	if x != nil {
		return x.EndHp
	}
	return 0
}

func (x *C2S_RunLocalBattleMessage) GetEndMp() int32 {
	if x != nil {
		return x.EndMp
	}
	return 0
}

func (x *C2S_RunLocalBattleMessage) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *C2S_RunLocalBattleMessage) GetHasPet() bool {
	if x != nil {
		return x.HasPet
	}
	return false
}

type S2C_RunLocalBattleMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code   Response.Code       `protobuf:"varint,1,opt,name=code,proto3,enum=proto.Response.Code" json:"code,omitempty"`           //响应码
	Result BattleDefine.Result `protobuf:"varint,2,opt,name=result,proto3,enum=proto.BattleDefine.Result" json:"result,omitempty"` //战斗结果
}

func (x *S2C_RunLocalBattleMessage) Reset() {
	*x = S2C_RunLocalBattleMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbGame_battle_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_RunLocalBattleMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_RunLocalBattleMessage) ProtoMessage() {}

func (x *S2C_RunLocalBattleMessage) ProtoReflect() protoreflect.Message {
	mi := &file_pbGame_battle_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_RunLocalBattleMessage.ProtoReflect.Descriptor instead.
func (*S2C_RunLocalBattleMessage) Descriptor() ([]byte, []int) {
	return file_pbGame_battle_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_RunLocalBattleMessage) GetCode() Response.Code {
	if x != nil {
		return x.Code
	}
	return Response.Code(0)
}

func (x *S2C_RunLocalBattleMessage) GetResult() BattleDefine.Result {
	if x != nil {
		return x.Result
	}
	return BattleDefine.Result(0)
}

var File_pbGame_battle_proto protoreflect.FileDescriptor

var file_pbGame_battle_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x2f, 0x62, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x70, 0x62,
	0x42, 0x61, 0x73, 0x65, 0x2f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x70, 0x62,
	0x47, 0x61, 0x6d, 0x65, 0x2f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x2f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x35, 0x0a, 0x19, 0x43, 0x32, 0x53, 0x5f, 0x42, 0x61, 0x74, 0x74,
	0x6c, 0x65, 0x50, 0x61, 0x73, 0x73, 0x54, 0x65, 0x73, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x22, 0x45, 0x0a, 0x19, 0x53,
	0x32, 0x43, 0x5f, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x50, 0x61, 0x73, 0x73, 0x54, 0x65, 0x73,
	0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x22, 0xc1, 0x01, 0x0a, 0x19, 0x43, 0x32, 0x53, 0x5f, 0x52, 0x75, 0x6e, 0x4c, 0x6f,
	0x63, 0x61, 0x6c, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x65, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x73, 0x65, 0x65, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x48, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x48, 0x70, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x4d, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6e, 0x64, 0x48,
	0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65, 0x6e, 0x64, 0x48, 0x70, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6e, 0x64, 0x4d, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x65,
	0x6e, 0x64, 0x4d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x68, 0x61, 0x73, 0x50, 0x65, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x68, 0x61, 0x73, 0x50, 0x65, 0x74, 0x22, 0x79, 0x0a, 0x19, 0x53, 0x32, 0x43, 0x5f, 0x52, 0x75,
	0x6e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x28, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x32, 0x0a,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x61, 0x74, 0x74, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x69,
	0x6e, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x42, 0x1c, 0x5a, 0x1a, 0x77, 0x6f, 0x72, 0x6c, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x3b, 0x70, 0x62, 0x47, 0x61, 0x6d, 0x65, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbGame_battle_proto_rawDescOnce sync.Once
	file_pbGame_battle_proto_rawDescData = file_pbGame_battle_proto_rawDesc
)

func file_pbGame_battle_proto_rawDescGZIP() []byte {
	file_pbGame_battle_proto_rawDescOnce.Do(func() {
		file_pbGame_battle_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbGame_battle_proto_rawDescData)
	})
	return file_pbGame_battle_proto_rawDescData
}

var file_pbGame_battle_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_pbGame_battle_proto_goTypes = []interface{}{
	(*C2S_BattlePassTestMessage)(nil), // 0: proto.C2S_BattlePassTestMessage
	(*S2C_BattlePassTestMessage)(nil), // 1: proto.S2C_BattlePassTestMessage
	(*C2S_RunLocalBattleMessage)(nil), // 2: proto.C2S_RunLocalBattleMessage
	(*S2C_RunLocalBattleMessage)(nil), // 3: proto.S2C_RunLocalBattleMessage
	(Response.Code)(0),                // 4: proto.Response.Code
	(BattleDefine.Result)(0),          // 5: proto.BattleDefine.Result
}
var file_pbGame_battle_proto_depIdxs = []int32{
	4, // 0: proto.S2C_BattlePassTestMessage.code:type_name -> proto.Response.Code
	4, // 1: proto.S2C_RunLocalBattleMessage.code:type_name -> proto.Response.Code
	5, // 2: proto.S2C_RunLocalBattleMessage.result:type_name -> proto.BattleDefine.Result
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_pbGame_battle_proto_init() }
func file_pbGame_battle_proto_init() {
	if File_pbGame_battle_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbGame_battle_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_BattlePassTestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_battle_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_BattlePassTestMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_battle_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_RunLocalBattleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pbGame_battle_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_RunLocalBattleMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbGame_battle_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbGame_battle_proto_goTypes,
		DependencyIndexes: file_pbGame_battle_proto_depIdxs,
		MessageInfos:      file_pbGame_battle_proto_msgTypes,
	}.Build()
	File_pbGame_battle_proto = out.File
	file_pbGame_battle_proto_rawDesc = nil
	file_pbGame_battle_proto_goTypes = nil
	file_pbGame_battle_proto_depIdxs = nil
}
