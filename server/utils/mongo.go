package ut

import (
	"errors"
	"fmt"
	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"math/big"
	"reflect"
	"strconv"
	"sync"
)

type MongoId struct {
	timestamp     int64      // 时间戳 4字节
	machineCode   int64      // 机器码 3字节
	pid           int64      // PID 2字节
	countdown     int64      // 随机计数器 3字节
	primitive     string     // 原始数据
	bigIntVal     *big.Int   //转换为BitInt的值
	int64Val      uint64     // 转换为int64的值
	bigIntValLock *sync.Once // 一次性锁
	int64ValLock  *sync.Once // 一次性锁
}

func NewMongoIdFrom(id string) (m *MongoId, err error) {
	if len(id) != 24 {
		return nil, errors.New(fmt.Sprintf("%s不是一个正确的mongo id,序列失败", id))
	}
	m = &MongoId{
		timestamp:     HexToInt64(id[0:8]),
		machineCode:   HexToInt64(id[8:14]),
		pid:           HexToInt64(id[14:18]),
		countdown:     HexToInt64(id[18:]),
		primitive:     id,
		bigIntValLock: new(sync.Once),
		int64ValLock:  new(sync.Once),
	}
	return
}

// BitInt 转换为BitInt,由于包含时间戳使得这个数据很大而不方便使用,这个数据始终安全和唯一。
func (this *MongoId) BitInt() *big.Int {
	this.bigIntValLock.Do(func() {
		this.bigIntVal, _ = big.NewInt(0).SetString(fmt.Sprintf(`%d%d%d%d`, this.timestamp, this.machineCode, this.pid, this.countdown), 10)
	})
	return this.bigIntVal
}

// Int64 转换为int64,不包含时间戳,这个数据客户端可以使用Long库来包装使用,这个数据始终安全和唯一,和Get()获取的值无法比较.
func (this *MongoId) Int64() uint64 {
	this.int64ValLock.Do(func() {
		this.int64Val, _ = strconv.ParseUint(fmt.Sprintf(`%d%d%d`, this.machineCode, this.pid, this.countdown), 10, 64)
	})
	return this.int64Val
}

// Get 获取原始值,16进制字符串
func (this *MongoId) Get() string {
	return this.primitive
}

// ObjectId 用于查询的id值
func (this *MongoId) ObjectId() primitive.ObjectID {
	hex, _ := primitive.ObjectIDFromHex(this.primitive)
	return hex
}

// FieldToBsonAuto 获取一个基于obj对象生成的bson.M用来做db保存,需要设置tag:`bson:xxx`
func FieldToBsonAuto(obj interface{}) bson.M {
	v := reflect.ValueOf(obj)
	// 如果值是指针就需要转换
	if v.Kind() == reflect.Pointer {
		// 获取指针指向的对象值
		v = v.Elem()
	}
	t := v.Type()
	r := bson.M{}
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		tag := field.Tag.Get("bson")
		if IsEmpty(tag) {
			tag = field.Tag.Get("json")
		}
		if IsEmpty(tag) || tag == "-" {
			continue // 跳过
		}
		val := reflect.ValueOf(v.Interface()).Field(i)
		if val.Kind() == reflect.Struct || val.Kind() == reflect.Pointer {
			//log.Debug("Save field :%s, instance :% s", field, t)
			if !val.Elem().IsValid() {
				continue
			}
			r[tag] = val.Elem().Interface()
			continue
		}
		r[tag] = val.Interface()
	}
	return r
}

// FieldToBson 获取一个基于传入的fields列表生成的bson.M，用于文档操作,所有fields列表的字段必须都存在于obj上,需要设置tag:`bson:xxx`
func FieldToBson(obj interface{}, fields ...string) bson.M {
	v := reflect.ValueOf(obj)
	// 如果值是指针就需要转换
	if v.Kind() == reflect.Pointer {
		// 获取指针指向的对象值
		v = v.Elem()
	}

	t := v.Type()
	r := bson.M{}
	for _, field := range fields {
		rf, b := t.FieldByName(field)
		if !b {
			log.Warning("Skip.none field :%s, instance :% s", field, t)
			continue
		}
		bson := rf.Tag.Get("bson")
		if bson == "" {
			log.Warning("Skip.field has no bson tag:%s, instance :% s", field, t)
			continue
		}
		val := reflect.ValueOf(v.Interface()).FieldByName(field)
		if val.Kind() == reflect.Struct || val.Kind() == reflect.Pointer {
			//log.Warning("Skip.field type error:%s, instance :% s", field, t)
			r[bson] = val.Elem().Interface()
			continue
		}
		r[bson] = val.Interface()
	}
	return r
}
