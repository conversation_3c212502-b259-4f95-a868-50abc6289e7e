package ut

import (
	"crypto/ecdsa"
	"crypto/md5"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"math/rand"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"
	"world/common/pbBase/ModelConst"
	"world/utils/array"
	"world/utils/bit"

	"github.com/dgrijalva/jwt-go"
	"github.com/huyangv/vmqant/log"
	"github.com/sasha-s/go-deadlock"
	"github.com/spf13/cast"
	"golang.org/x/crypto/ssh"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	isSetRandSeed = false
)

type DataWeight interface {
	GetWeight() int
}

type Number interface {
	int8 | int16 | int32 | int | int64 | uint8 | uint16 | uint32 | uint | uint64 | float32 | float64
}

type BitNumberType interface {
	int32 | int | int64 | ModelConst.Type
}

const (
	TIME_MONTH  int64 = 30 * 7 * 24 * 60 * 60 * 1000 //月
	TIME_WEEK   int64 = 7 * 24 * 60 * 60 * 1000      //周
	TIME_DAY    int64 = 24 * 60 * 60 * 1000          //天
	TIME_HOUR   int64 = 60 * 60 * 1000               //时
	TIME_MINUTE int64 = 60 * 1000                    //分
	TIME_SECOND int64 = 1000                         //秒
)

// Now 当前的时间戳 毫秒
func Now() int64 {
	return time.Now().UnixMilli()
}

func Now32() int {
	return int(time.Now().UnixMilli() / 1000)
}

// DateZeroTime 根据毫秒获取当天零点毫秒
func DateZeroTime(msec int64) int64 {
	date := time.UnixMilli(msec)
	year, month, day := date.Date()
	addTime := time.Date(year, month, day, 0, 0, 0, 0, date.Location())
	return addTime.UnixMilli()
}

func NowZeroTime() int {
	date := time.Now()
	year, month, day := date.Date()
	addTime := time.Date(year, month, day, 0, 0, 0, 0, date.Location())
	return int(addTime.UnixNano() / 1e6)
}

// NextDateTime 根据毫秒获取下一天的零点毫秒
func NextDateTime(msec int64) int64 {
	return DateZeroTime(msec) + TIME_DAY
}

// Random 生成随机数包括min和max
func Random(min int, max int) int {
	if min >= max {
		return min
	} else if !isSetRandSeed {
		isSetRandSeed = true
		rand.Seed(time.Now().Unix())
	}
	return rand.Intn(int(math.Max(float64(max-min), 0))+1) + min
}

func RandomFloat64(min float64, max float64) float64 {
	if min >= max {
		return min
	} else if !isSetRandSeed {
		isSetRandSeed = true
		rand.Seed(time.Now().Unix())
	}
	return min + (max-min)*rand.Float64()
}

func RandomIn[T any](ary []T) (it T, index int) {
	if len(ary) == 0 {
		return
	}
	index = Random(0, len(ary)-1)
	return ary[index], index
}

// Chance 概率
func Chance(odds int) bool {
	mul := 100
	return odds > 0 && Random(0, 100*mul-1) < odds*mul
}

func ChanceF(odds float64) bool {
	mul := 100.0
	return odds > 0 && RandomFloat64(0, 100*mul-1) < odds*mul
}

func RandomIndexByDataWeight(weights []DataWeight) int {
	return RandomIndexByWeight(weights, func(m DataWeight) int { return m.GetWeight() })
}

func RandomIndexByTotalWeight(weights []int) int {
	return RandomIndexByWeight(weights, func(m int) int { return m })
}

func RandomIndexByWeight[T any](weights []T, predicate func(T) int) int {
	cnt := len(weights)
	if cnt == 0 {
		return -1
	}
	totalWeight := 0
	for _, m := range weights {
		totalWeight += predicate(m)
	}
	if totalWeight > 0 {
		offset := Random(1, totalWeight)
		for i := 0; i < cnt; i++ {
			val := predicate(weights[i])
			offset -= val
			if offset <= 0 {
				return i
			}
		}
	}
	return Random(0, cnt-1)
}

// 打乱数组
func RandomArray[T any](_array []T) []T {
	array := _array[:]
	for i := len(array) - 1; i >= 0; i-- {
		randomIndex := Random(0, 1e10+7) % (i + 1)
		a := array[randomIndex]
		b := array[i]
		array[i] = a
		array[randomIndex] = b
	}
	return array
}

// UID7 生成一个随机7位UID
func UID7() string {
	return strconv.Itoa(Random(1000000, 9999999))
}
func UID8() string {
	return strconv.Itoa(Random(10000000, 99999999))
}
func UID6() string {
	return strconv.Itoa(Random(100000, 999999))
}

// MD5 生成md5
func MD5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

// If 三元判断
func If[T any](condition bool, trueVal, falseVal T) T {
	if condition {
		return trueVal
	}
	return falseVal
}

func Int(val interface{}) int {
	switch reply := val.(type) {
	case float32:
		return int(reply)
	case float64:
		return int(reply)
	case int32:
		return int(reply)
	case int64:
		return int(reply)
	case int:
		return reply
	case string:
		s, _ := strconv.Atoi(reply)
		return s
	case nil:
		return 0
	}
	return 0
}

func Float32(val interface{}) float32 {
	switch reply := val.(type) {
	case float32:
		return reply
	case float64:
		return float32(reply)
	case int:
		return float32(reply)
	case string:
		s, _ := strconv.ParseFloat(reply, 32)
		return float32(s)
	case nil:
		return 0
	}
	return 0
}

func Float64(val interface{}) float64 {
	switch reply := val.(type) {
	case float64:
		return reply
	case float32:
		return float64(reply)
	case int:
		return float64(reply)
	case string:
		s, _ := strconv.ParseFloat(reply, 64)
		return s
	case nil:
		return 0
	}
	return 0
}

func String(val interface{}) string {
	switch reply := val.(type) {
	case string:
		return reply
	case float64:
		return Itoa(reply)
	case int:
		return Itoa(reply)
	case bool:
		return strconv.FormatBool(reply)
	case nil:
		return ""
	}
	return ""
}

func Bool(val interface{}) bool {
	switch reply := val.(type) {
	case bool:
		return reply
	case nil:
		return false
	}
	return false
}

func IntArray(val interface{}) []int {
	switch reply := val.(type) {
	case []int:
		return reply
	case []float64:
		return array.Map(reply, func(m float64, _ int) int { return int(m) })
	case []interface{}:
		return array.Map(reply, func(m interface{}, _ int) int { return Int(m) })
	case nil:
		return []int{}
	}
	return []int{}
}

func StringArray(val interface{}) []string {
	switch reply := val.(type) {
	case []string:
		return reply
	case []interface{}:
		return array.Map(reply, func(m interface{}, _ int) string { return String(m) })
	case nil:
		return []string{}
	}
	return []string{}
}

func Bytes(v any) []byte {
	body, _ := json.Marshal(v)
	return body
}

func MapArray(data interface{}) []map[string]interface{} {
	arr := []map[string]interface{}{}
	if data == nil {
		return arr
	}
	switch reply := data.(type) {
	case []interface{}:
		for _, v := range reply {
			arr = append(arr, v.(map[string]interface{}))
		}
		return arr
	case []map[string]interface{}:
		return reply
	case primitive.A:
		for _, v := range reply {
			arr = append(arr, MapInterface(v))
		}
		return arr
	case nil:
	}
	return arr
}

func MapInterface(data interface{}) map[string]interface{} {
	switch reply := data.(type) {
	case map[string]interface{}:
		return reply
	case nil:
	}
	return map[string]interface{}{}
}

func RpcInterfaceMap(reply interface{}, _err interface{}) (ret map[string]interface{}, err string) {
	switch e := _err.(type) {
	case string:
		err = e
	case error:
		if _err != nil {
			err = e.Error()
		}
	}
	switch reply := reply.(type) {
	case map[string]interface{}:
		ret = reply
	}
	return
}

func RpcInt(reply interface{}, _err interface{}) (ret int, err string) {
	switch e := _err.(type) {
	case string:
		err = e
	case error:
		if _err != nil {
			err = e.Error()
		}
	}
	ret = Int(reply)
	return
}

func RpcBool(reply interface{}, _err interface{}) (ret bool, err string) {
	switch e := _err.(type) {
	case string:
		err = e
	case error:
		if _err != nil {
			err = e.Error()
		}
	}
	ret = Bool(reply)
	return
}

func Rpcbytes(reply interface{}, _err interface{}) (ret []byte, err string) {
	switch e := _err.(type) {
	case string:
		err = e
	case error:
		if _err != nil {
			err = e.Error()
		}
	}
	switch reply := reply.(type) {
	case []byte:
		ret = reply
	}
	return
}

// int转字符串
func Itoa(val interface{}) string {
	return strconv.Itoa(Int(val))
}

// string转int
func Atoi(val interface{}) int {
	r, _ := strconv.Atoi(String(val))
	return r
}

// string转Float
func Atof(val interface{}) float64 {
	r, _ := strconv.ParseFloat(String(val), 64)
	return r
}

// 将一个字符串拆分为数组
func StringToInts(val string, separator string) []int {
	if val == "" {
		return []int{}
	}
	arr := strings.Split(val, separator)
	ret := []int{}
	for _, s := range arr {
		ret = append(ret, Atoi(s))
	}
	return ret
}

func StringToFloats(val string, separator string) []float64 {
	if val == "" {
		return []float64{}
	}
	arr := strings.Split(val, separator)
	ret := []float64{}
	for _, s := range arr {
		ret = append(ret, Atof(s))
	}
	return ret
}

// WrapString 包装字符串
func WrapString(separator string, arr ...interface{}) string {
	if len(arr) == 0 {
		return ""
	}
	str := ""
	for _, m := range arr {
		if str != "" {
			str += separator
		}
		str += String(m)
	}
	return str
}

// GetStringLen 获取字符串长度 一个汉字2个长度
func GetStringLen(str string) int {
	if str == "" {
		return 0
	}
	count := 0
	for _, ch := range str {
		count += If((ch >= 0x0001 && ch <= 0x007e) || (0xff60 <= ch && ch <= 0xff9f), 1, 2)
	}
	return count
}

// WorkDir 获取当前的项目路径
func WorkDir() (dir string) {
	var err error
	dir, err = os.Getwd()
	if err != nil {
		file, _ := exec.LookPath(os.Args[0])
		ApplicationPath, _ := filepath.Abs(file)
		dir, _ = filepath.Split(ApplicationPath)
	}
	return
}

func SetTimeout(duration time.Duration, cb func()) {
	go func() {
		timer := time.NewTimer(duration)
		defer timer.Stop()
		select {
		case <-timer.C:
			cb()
		}
	}()
}

// LoadConfig 加载配置文件
func LoadConfig(filename string) ([]byte, error) {
	return LoadJsonByPath(filename, "/bin/conf/json/")
}

func LoadJsonByPath(filename string, path string) ([]byte, error) {
	file, err := os.Open(WorkDir() + path + filename + ".json")
	if err != nil {
		return nil, err
	}
	return io.ReadAll(file)
}

// Max 同类型数字max
func Max[T Number](a, b T) T {
	if a > b {
		return a
	}
	return b
}

// Min 同类型数字min
func Min[T Number](a, b T) T {
	if a > b {
		return b
	}
	return a
}

// Sum 同类型数字相加
func Sum[T Number](a, b T) T {
	return a + b
}

func Clamp01[T Number](a T) int {
	if a <= 0 {
		return 0
	}
	return 1
}

// ToInt32 数组转int32组
func ToInt32[T any](arr []T) []int32 {
	out := make([]int32, len(arr))
	for i, val := range arr {
		out[i] = cast.ToInt32(val)
	}
	return out
}

// ToUInt64 数组转int64组
func ToUInt64[T any](arr []T) []uint64 {
	out := make([]uint64, len(arr))
	for i, val := range arr {
		out[i] = cast.ToUint64(val)
	}
	return out
}

// ToInt 数组转int组
func ToInt[T any](arr []T) []int {
	out := make([]int, len(arr))
	for i, val := range arr {
		out[i] = cast.ToInt(val)
	}
	return out
}

func Abs(x int) int {
	return int(math.Abs(float64(x)))
}

// IsEmpty 字符串是否为空 忽略空格
func IsEmpty(str string) bool {
	return len(strings.ReplaceAll(str, " ", "")) == 0
}

func Unlock(lock *deadlock.Mutex) {
	if lock != nil {
		lock.Unlock()
	}
}

func TraceMemStats() {
	var ms runtime.MemStats
	runtime.ReadMemStats(&ms)
	log.Info("Alloc:%d, Sys:%d, HeapSys:%d, HeapAlloc:%d, HeapInuse:%d, HeapReleased:%d, StackSys:%d", ms.Alloc, ms.Sys, ms.HeapSys, ms.HeapAlloc, ms.HeapInuse, ms.HeapReleased, ms.StackSys)
}

// HexToInt64 hex 转 int64 忽略错误
func HexToInt64(hex string) int64 {
	i, _ := strconv.ParseInt(hex, 16, 32)
	return i
}

// GetApplicationDir 获取项目server根路径
func GetApplicationDir() string {
	dir, _ := os.Getwd()
	return dir
}

// ReadFileContent 从文件中读取内容 路径以server根目录为基准，例如/bin/twomiles.cn.key
func ReadFileContent(path string) (v string, err error) {
	if !strings.HasPrefix(path, "/") {
		path = fmt.Sprintf("/%s", path)
	}
	file, err := os.ReadFile(fmt.Sprintf("%s%s", GetApplicationDir(), path))
	if err != nil {
		return
	}
	v = string(file)
	return
}

// GetAge 根据身份证获取年龄
func GetAge(idCard string) int {
	if len(idCard) < 18 {
		return 0
	}
	birthday := idCard[6:14]
	t, err := time.Parse("20060102", birthday)
	if err != nil {
		return 0
	}
	age := time.Now().Sub(t).Hours() / 24 / 365
	return cast.ToInt(age)
}

// Trim 字符串去掉所有空格
func Trim(str string) string {
	return strings.Join(strings.Fields(str), "")
}

// InitialIsChar 判断首个字符是不是字母 upper=true时区分大小写
func InitialIsChar(line string, upper bool) bool {
	line = Trim(line)
	if len(line) == 0 {
		return false
	}
	reg := "^[A-Za-z]"
	if upper {
		reg = "^[A-Z]"
	}
	matched, _ := regexp.Match(reg, []byte(line))
	return matched
}

// NotHasSuffixOrAppend  判断字符串str是不是以suffix结尾，如果不是，则追加dist字符串到末尾并返回str
func NotHasSuffixOrAppend(str string, suffix, dist string) string {
	if !strings.HasSuffix(str, suffix) {
		return fmt.Sprintf("%s%s", str, dist)
	}
	return str
}

// HasSuffixOrRemove 判断字符串str是不是以suffix结尾，如果是，则移除末尾的suffix并返回str
func HasSuffixOrRemove(str string, suffix string) string {
	if strings.HasSuffix(str, suffix) {
		return str[:len(str)-len(suffix)]
	}
	return str
}

// NotHasPrefixOrAppend 判断字符串str是不是以prefix开头，如果不是，则追加dist字符串到开头并返回str
func NotHasPrefixOrAppend(str string, prefix, dist string) string {
	if !strings.HasPrefix(str, prefix) {
		return fmt.Sprintf("%s%s", dist, str)
	}
	return str
}

// HasPrefixOrRemove 判断字符串str是不是以prefix开头，如果是，则移除开头的prefix并返回str
func HasPrefixOrRemove(str string, prefix string) string {
	if strings.HasPrefix(str, prefix) {
		return strings.Replace(str, prefix, "", 1)
	}
	return str
}

// GetExecuteTimeWrapFunc wrap一个方法 输出执行时间
func GetExecuteTimeWrapFunc(f func()) func() {
	return func() {
		begin := time.Now()
		defer func() {
			log.Debug("execute during: %fs", time.Since(begin).Seconds())
		}()
		f()
	}
}

func ToFixed2(val float64) float64 {
	return ToFixed(val, 2)
}

func ToFixed(val float64, precision int) float64 {
	return math.Floor(val*math.Pow10(precision)) / math.Pow10(precision)
}

func ToRound2(val float64) float64 {
	return ToRound(val, 2)
}

func ToRound(val float64, precision int) float64 {
	return math.Round(val*math.Pow10(precision)) / math.Pow10(precision)
}

// FirstToLower 首字母转小写
func FirstToLower(str string) string {
	firstChar := str[:1]
	return strings.ToLower(firstChar) + str[1:]
}

func roundToDecimal(n float64, decimals int) float64 {
	shift := math.Pow(10, float64(decimals))
	return math.Round(n*shift) / shift
}

func RoundNumber[T Number](n T) T {
	sn := cast.ToString(n)

	if len(sn) <= 2 {
		return T(5 * roundToDecimal(float64(n/5), 0))
	}

	return T(roundToDecimal(float64(n), 2-len(sn)))
}

// 适用于比较1.0.1这类版本号,支持长度不限
// 返回值>0表示A比B大，0表示相等
func CmpVersion(a, b string) int {
	vA := StringToInts(a, ".")
	vB := StringToInts(b, ".")
	vALen := len(vA)
	vBLen := len(vB)
	for i := 0; i < vALen; i++ {
		a, b := vA[i], 0
		if i < vBLen {
			b = vB[i]
		}
		if a == b {
			continue
		} else {
			return a - b
		}
	}
	if vBLen > vALen {
		return -1
	} else {
		return 0
	}
}

// ------------------------------ ssh相关 --------------------------------
// 远程执行脚本
func SshExcuteShell(remoteAddr, cmd string, args ...string) (rst string, err error) {
	log.Info("sshExcuteShell start remoteAddr: %v, cmd: %v, args: %v", remoteAddr, cmd, args)
	pemPath := "./jiuwanmu.pem"
	key, err := loadPEM(pemPath)
	if err != nil {
		log.Error("Failed to load PEM file: %v", err)
	}

	// SSH 连接配置
	config := &ssh.ClientConfig{
		User: "root",
		Auth: []ssh.AuthMethod{
			ssh.PublicKeys(key),
		},
		// Auth: []ssh.AuthMethod{
		// 	ssh.Password("password"),
		// },
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	// 建立 SSH 连接
	conn, err := ssh.Dial("tcp", fmt.Sprintf("%s:22", remoteAddr), config)
	if err != nil {
		log.Error("Failed to dial: %v", err)
	}
	defer conn.Close()

	// 创建 SSH 会话
	session, err := conn.NewSession()
	if err != nil {
		log.Error("Failed to create session: %v", err)
	}
	defer session.Close()

	// 执行远程命令
	output, err := session.CombinedOutput(cmd + " " + escapeArgs(args))
	if err != nil {
		log.Error("Failed to execute command: %v", err)
	}

	// 输出命令执行结果
	rst = string(output)
	log.Info("sshExcuteShell rst: %v", rst)
	return
}

// 加载 PEM 文件
func loadPEM(pemPath string) (ssh.Signer, error) {
	pemBytes, err := os.ReadFile(pemPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read PEM file: %v", err)
	}

	signer, err := ssh.ParsePrivateKey(pemBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %v", err)
	}

	return signer, nil
}

// 转义参数
func escapeArgs(args []string) string {
	escapedArgs := make([]string, len(args))
	for i, arg := range args {
		escapedArgs[i] = fmt.Sprintf("%q", arg)
	}
	return strings.Join(escapedArgs, " ")
}

// 根据x509证书字符串解析证书
func GetCertByCertStr(certStr string) (cert *x509.Certificate, err error) {
	// 解码Base64编码的证书字符串
	derBytes, err := base64.StdEncoding.DecodeString(certStr)
	if err != nil {
		log.Error("getpublicKeyByCertStr base64 certStr: %v err: %v", certStr, err)
		return
	}

	// 解析X.509证书
	cert, err = x509.ParseCertificate(derBytes)
	if err != nil {
		log.Error("getpublicKeyByCertStr cert parse certStr: %v err: %v", certStr, err)
		return
	}
	return
}

// 验证证书信任链 [终端证书 中间证书 根证书]
func VerifyCertChain(certChain []*x509.Certificate) error {
	if len(certChain) < 3 {
		return errors.New("certChain len error")
	}
	// 创建一个根证书池，用于验证证书链中的证书
	roots := x509.NewCertPool()
	roots.AddCert(certChain[2]) // 将根证书添加到根证书池

	// 验证证书链
	opts := x509.VerifyOptions{
		Roots:         x509.NewCertPool(),                      // 根证书池
		CurrentTime:   time.Now(),                              // 指定当前时间
		KeyUsages:     []x509.ExtKeyUsage{x509.ExtKeyUsageAny}, // 指定密钥用途（可根据需要调整）
		Intermediates: x509.NewCertPool(),                      // 中间证书池
	}
	opts.Roots.AddCert(certChain[2])         // 将根证书添加到根证书池
	opts.Intermediates.AddCert(certChain[1]) // 将中间证书添加到中间证书池

	// 验证证书
	_, err := certChain[0].Verify(opts)
	return err
}

// 解析JWS数据
func ParseJwsToken(signedPayload string, publicKey *ecdsa.PublicKey) (jwt.MapClaims, error) {
	// 解析JWS格式的推送信息
	retToken, err := jwt.Parse(signedPayload, func(token *jwt.Token) (interface{}, error) {
		return publicKey, nil
	})
	if err != nil {
		log.Error("ParseJwsToken reponse jwt err: %v", err)
		return nil, err
	}
	mapClaims := retToken.Claims.(jwt.MapClaims)
	if mapClaims == nil {
		errStr := "ParseJwsToken mapClaims nil"
		log.Error(errStr)
		err = errors.New(errStr)
		return nil, err
	}
	return mapClaims, nil
}

var nameRegex = regexp.MustCompile(`^[\p{L}\p{N}\p{P}\s]{2,6}$`)

// IsValidRoleName
/*
 * @description 检查角色名是否合法,使用正则表达式,长度2-6之间
 * @param name
 * @return bool
 */
func IsValidRoleName(name string) bool {
	return nameRegex.MatchString(name)
}

// FillAry [T any]
/*
 * @description 将ary数据复制到长度为size的新ary中，返回新的ary
 * @param ary
 * @param size
 * @return []T
 */
func FillAry[T any](ary []T, size int) []T {
	tmp := make([]T, size)
	if ary == nil {
		return tmp
	}
	copy(tmp, ary)
	return tmp
}

// IsTimeOut 判断时间是否超出 i
func IsTimeOut(time, i int64) bool {
	return Now()-time >= i
}

// SumValue
/*
 * @description 计算和，并且限制范围
 * @param base
 * @param add
 * @param min
 * @param max
 * @return int
 */
func SumValue(base, add, min, max int) int {
	val := base + add
	if val < min {
		val = min
	}
	if val > max {
		val = max
	}
	return val
}

func ParseMapKV[K comparable, V any, K1 comparable, V1 any](in map[K1]V1, predicate func(key K1, value V1) (K, V)) map[K]V {
	out := make(map[K]V)
	for key, value := range in {
		k, v := predicate(key, value)
		out[k] = v
	}
	return out
}

// SetHandAndFeet 设置初始手和脚
//
// Parameters:
//   - icon *int64
func SetHandAndFeet(icon *int64) {
	bit.OrAssign(icon, bit.ShiftLeft(bit.And(1, ModelConst.LEN_HAND_STYLE), ModelConst.OFFSET_HAND_STYLE))
	bit.OrAssign(icon, bit.ShiftLeft(bit.And(1, ModelConst.LEN_FEET_STYLE), ModelConst.OFFSET_FEET_STYLE))
}

// SetIconValue 设置图标数值,支持设置主要位和次要位(可选)
//
// Parameters:
//   - target int64 目标数值
//   - source int64 源数值
//   - mainOffset int64 主要位的偏移量
//   - mainLength int64 主要位的长度
//   - subOffset int64 次要位的偏移量(可选,-1表示不设置)
//   - subLength int64 次要位的长度(可选)
//   - needFix bool 是否需要修正偏移量(+32)
//
// Returns:
//   - int64 设置后的结果
func SetIconValue[T BitNumberType](target, source int64, mainOffset, mainLength, subOffset, subLength T, needFix bool) int64 {
	_mainOffset := int64(mainOffset)
	_mainLength := int64(mainLength)
	_subOffset := int64(subOffset)
	_subLength := int64(subLength)

	if needFix {
		_mainOffset += bit.HIGHT
	}

	// 获取源数值中的位值
	bitValue := bit.And(bit.ShiftRight(source, _mainOffset), _mainLength)

	if bitValue > 0 {
		// 设置主要位
		bit.AndAssign(&target, bit.Not(bit.ShiftLeft(_mainLength, _mainOffset)))
		bit.OrAssign(&target, bit.ShiftLeft(bit.And(bitValue, _mainLength), _mainOffset))

		// 设置次要位(如果有)
		if _subOffset >= 0 {
			bitValue = bit.And(bit.ShiftRight(source, _subOffset), _subLength)
			bit.AndAssign(&target, bit.Not(bit.ShiftLeft(_subLength, _subOffset)))
			bit.OrAssign(&target, bit.ShiftLeft(bit.And(bitValue, _subLength), _subOffset))
		}
	}
	return target
}
