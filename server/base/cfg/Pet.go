package cfg

import (
	"world/common/pbGame/ITEM_GRADE"
	ut "world/utils"

	"github.com/samber/lo"
)

const ConfigNamePet = "Pet"

type Pet[K int] struct {
	Id         K                `json:"id"`
	Name       string           `json:"name"`
	Str        int              `json:"str"`
	Con        int              `json:"con"`
	Agi        int              `json:"agi"`
	Ilt        int              `json:"ilt"`
	Wis        int              `json:"wis"`
	Grow       int              `json:"grow"`
	Learn      int              `json:"learn"`
	HitRate    int              `json:"petHitrate"`
	AtkType    int              `json:"atkType"`
	AtkMin     int              `json:"atkMin"`
	AtkMax     int              `json:"atkMax"`
	PetAtkTime int              `json:"petAtkTime"`
	Job        int              `json:"job"`
	BornStatus int              `json:"bornStatus"`
	Grade      ITEM_GRADE.Type  `json:"grade"`
	BornSkill  []*PetBornSkill  `json:"bornSkill"`
	LearnSkill []*PetLearnSkill `json:"learnSkill"`
}

func (p *Pet[K]) GetName() string {
	return ConfigNamePet
}
func (p *Pet[K]) GetUnique() K {
	return p.Id
}
func (p *Pet[K]) OnInit() {
}

func (p *Pet[K]) RandomGetGrow() int {
	v := 5 + int(float64(p.Grow-5)*((ut.RandomFloat64(0, 1.0)+ut.RandomFloat64(0, 1.0))/2))
	return v
}

func (p *Pet[K]) RandomGetLearn() int {
	v := 5 + int(float64(p.Learn-5)*((ut.RandomFloat64(0, 1.0)+ut.RandomFloat64(0, 1.0))/2))
	return v
}

// RandomLearnSkill 随机获取一个领悟技能
//
// Parameters:
//   - exclude []int 需要排除的技能
//
// Returns:
//   - int
func (p *Pet[K]) RandomLearnSkill(exclude []int) int {
	// 复制一份
	ary := make([]*PetLearnSkill, len(p.LearnSkill))
	copy(ary, p.LearnSkill)
	// 打乱
	ary = ut.RandomArray(ary)

	ary = lo.Filter(ary, func(i1 *PetLearnSkill, _ int) bool {
		return !lo.Contains(exclude, i1.Id)
	})

	idx := ut.Random(0, len(ary)-1)
	return ary[idx].Id
}
