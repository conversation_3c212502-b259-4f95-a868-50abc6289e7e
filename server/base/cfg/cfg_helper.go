package cfg

import (
	ut "world/utils"

	"github.com/samber/lo"
)

func afterInit() {
	initMapNpc()
	initSkill()
}

func initMapNpc() {
	lo.ForEach(ContainerNpc.GetData(), func(v *Npc[int], _ int) {
		mapBean, _ := ContainerMap.GetBeanByUnique(v.MapId)
		if mapBean.NpcMap == nil {
			mapBean.NpcMap = make(map[int]*Npc[int])
		}
		// 关联地图
		mapBean.NpcMap[v.Id] = v
		if v.TaskMap == nil {
			v.TaskMap = make(map[int]*Mission[int])
		}
		// 关联任务
		for _, taskId := range v.Missions {
			taskBean, _ := ContainerMission.GetBeanByUnique(taskId)
			v.TaskMap[taskId] = taskBean
		}
	})
}

// initSkill 主要是初始化技能的最大等级数据
func initSkill() {
	ary := ContainerSkill.GetData()
	info := make(map[int]int)

	for _, data := range ary {
		lv := 0
		if v, ok := info[data.Id]; ok {
			lv = v
		}
		info[data.Id] = ut.Max(lv, data.Level)
	}

	lo.ForEach(ContainerSkill.GetData(), func(v *Skill[string], _ int) {
		v.ConfigMaxLevel = info[v.Id]
	})
}
