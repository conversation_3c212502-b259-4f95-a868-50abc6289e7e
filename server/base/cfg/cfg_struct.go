package cfg

import "fmt"

// LEVEL_MAX 服务器限制最高等级
var lv_max int = 0

func setLevelMax(v int) { lv_max = v }
func LevelMax() int     { return lv_max + 1 }

type ConfigCondition struct {
	Type int `json:"type"`
	Id   int `json:"id"`
	Num  int `json:"num"`
}

// RewardItem 奖励物品
type RewardItem struct {
	Id       int `json:"id"`       // 物品id
	Quantity int `json:"quantity"` // 数量
}

// GetSkillConfigMaxLevel 获取技能存在的配置最大等级
//
// Parameters:
//   - skillId int
//
// Returns:
//   - int
func GetSkillConfigMaxLevel(skillId int) int {
	bean, _ := ContainerSkill.GetBeanByUnique(fmt.Sprintf("%d-1", skillId))
	if bean == nil {
		return 0
	}
	return bean.ConfigMaxLevel
}

// 宠物天赋技能
type PetBornSkill struct {
	Id    int `json:"id"`
	Level int `json:"level"`
}

// 宠物领悟技能
type PetLearnSkill struct {
	Id   int `json:"id"`
	Seal int `json:"seal"` // 封印概率
}
