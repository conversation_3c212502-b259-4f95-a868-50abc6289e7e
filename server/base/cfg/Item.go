package cfg

import (
	"world/common/pbGame"
	"world/common/pbGame/ITEM_GRADE"
	"world/common/pbGame/ITEM_TYPE"
	"world/common/pbGame/MyDefine"
)

const ConfigNameItem = "Item"

type Item[K int] struct {
	Id                   K                 `json:"id"`
	Type                 ITEM_TYPE.Type    `json:"type"`
	Grade                ITEM_GRADE.Type   `json:"grade"`
	ReqLv                int               `json:"reqLv"`
	ReqStr               int               `json:"reqStr"`
	ReqCon               int               `json:"reqCon"`
	ReqAgi               int               `json:"reqAgi"`
	ReqIlt               int               `json:"reqIlt"`
	ReqWis               int               `json:"reqWis"`
	AtkType              pbGame.AtkType    `json:"atkType"`
	AtkTime              int               `json:"atk_time"`
	AtkMin               int               `json:"atkMin"`
	AtkMax               int               `json:"atkMax"`
	DefStr               int               `json:"def_str"`
	DefAgi               int               `json:"def_agi"`
	DefMag               int               `json:"def_mag"`
	HitRate              int               `json:"hitrate"`
	Round                int               `json:"round"`
	Area                 int               `json:"area"`
	DurMax               int               `json:"durMax"`
	VipLevelReq          int               `json:"vipLevelReq"` // 需求vip等级
	FashionIcon1         int64             `json:"fashIcon1"`
	FashionIcon2         int64             `json:"fashIcon2"`
	FashionIcon3         int64             `json:"fashIcon3"`
	BagIcon              int               `json:"bagIcon"`
	AutoBind             int               `json:"autoBinding"`
	StackNum             int               `json:"stackNum"`
	DecomChest           string            `json:"decomCHest"`
	Decompose            bool              `json:"-"`
	Price                int               `json:"price"`
	Power1               int               `json:"power1"`
	PowerValue1          int               `json:"powerValue1"`
	PowerValueBlood1     int               `json:"PowerValueBlood1"`    // 能量精华数据
	PowerValueBloodMax1  int               `json:"PowerValueBloodMax1"` // 能量精华数据
	OwnTime              int               `json:"ownTime"`
	Power2               int               `json:"power2"`
	PowerValue2          int               `json:"powerValue2"`
	Power3               int               `json:"power3"`
	PowerValue3          int               `json:"powerValue3"`
	Icon                 int               `json:"icon"`
	AttachCount          int               `json:"attachCount"`
	ItemSet              int               `json:"itemSet"`
	BindPower1           int               `json:"bindPower1"`
	BindPowerValue1      int               `json:"bindPowerValue1"`
	BindPower2           int               `json:"bindPower2"`
	BindPowerValue2      int               `json:"bindPowerValue2"`
	AscensionStar        int               `json:"ascensionStar"`        // 升星
	UpgradeAscensionStar int               `json:"upgradeAscensionStar"` // 进阶升星
	PetProvideExp        int               `json:"petProvideExp"`        // 提供宠物装备经验
	SealGrade            int               `json:"seal_grade"`
	SealType             int               `json:"seal_type"`
	cachePower1          *pbGame.PowerData `json:"-"`
	cachePower2          *pbGame.PowerData `json:"-"`
	cachePower3          *pbGame.PowerData `json:"-"`
	cacheBindPower1      *pbGame.PowerData `json:"-"`
	cacheBindPower2      *pbGame.PowerData `json:"-"`
}

func (i *Item[K]) GetName() string {
	return ConfigNameItem
}
func (i *Item[K]) GetUnique() K {
	return i.Id
}
func (i *Item[K]) OnInit() {
	i.Decompose = i.DecomChest == "true"
}

// IsCanStack 是否可以堆积
func (i *Item[K]) IsCanStack() bool {
	return i.StackNum > 1
}

// IsAutoBinding 是否自动绑定
func (i *Item[K]) IsAutoBinding() bool {
	return i.AutoBind == 1
}

// parsePower
/*
 * @description 生成power
 * @param typ
 * @param val
 * @return *pbGame.PowerData
 */
func (i *Item[K]) parsePower(typ, val int) *pbGame.PowerData {
	return &pbGame.PowerData{
		Type:  MyDefine.POWER(typ),
		Value: int32(val),
	}
}

func (i *Item[K]) ParsePower1() *pbGame.PowerData {
	if i.cachePower1 == nil {
		i.cachePower1 = i.parsePower(i.Power1, i.PowerValue1)
	}
	return i.cachePower1
}
func (i *Item[K]) ParsePower2() *pbGame.PowerData {
	if i.cachePower2 == nil {
		i.cachePower2 = i.parsePower(i.Power2, i.PowerValue2)
	}
	return i.cachePower2
}
func (i *Item[K]) ParsePower3() *pbGame.PowerData {
	if i.cachePower3 == nil {
		i.cachePower3 = i.parsePower(i.Power3, i.PowerValue3)
	}
	return i.cachePower3
}
func (i *Item[K]) ParseBindPower1() *pbGame.PowerData {
	if i.cacheBindPower1 == nil {
		i.cacheBindPower1 = i.parsePower(i.BindPower1, i.BindPowerValue1)
	}
	return i.cacheBindPower1
}
func (i *Item[K]) ParseBindPower2() *pbGame.PowerData {
	if i.cacheBindPower2 == nil {
		i.cacheBindPower2 = i.parsePower(i.BindPower2, i.BindPowerValue2)
	}
	return i.cacheBindPower2
}

// IsOneHandWeaponType 是不是单手武器类型
//
// Returns:
//   - bool
func (i *Item[K]) IsOneHandWeaponType() bool {
	if i.Type == ITEM_TYPE.WEAPON_ONEHAND_SWORD {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_ONEHAND_BLADE {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_ONEHAND_CROSSBOW {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_ONEHAND_GUN {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_ONEHAND_HAMMER {
		return true
	}
	return false
}

// IsTwoHandWeaponType 是不是双手武器类型
//
// Returns:
//   - bool
func (i *Item[K]) IsTwoHandWeaponType() bool {
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_SWORD {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_BLADE {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_HEAVY {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_STAFF {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_LANCE {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_CROSSBOW {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_BALL {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_BOW {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_GUN {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_HAMMER {
		return true
	}
	if i.Type == ITEM_TYPE.WEAPON_TWOHAND_FAN {
		return true
	}
	return false
}
