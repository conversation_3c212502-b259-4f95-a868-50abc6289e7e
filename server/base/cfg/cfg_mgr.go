package cfg

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"
	ut "world/utils"

	"github.com/huyangv/vmqant/log"
)

var configs = make(map[string]any, 0)

// ConfigLoad load配置文件
func ConfigLoad() {
	sTime := time.Now()
	log.Info("开始初始化配置文件!")
	LoadConfig()

	for _, config := range configs {
		valueOf := reflect.ValueOf(config)
		initFunc := valueOf.MethodByName("OnInit")
		initFunc.Call(nil)
	}
	log.Info("配置文件初始化完成， during:%fs", time.Since(sTime).Seconds())
	afterInit()
}

// 请不要手动调用该方法
func localNewContainer[K Unique, V Config[K]](name string) *ConfigContainer[K, V] {
	temp := &ConfigContainer[K, V]{
		name: name,
		data: make([]V, 0),
	}
	temp.initConfigData()
	configs[temp.name] = temp
	return temp
}

type ConfigContainer[K Unique, V Config[K]] struct {
	name         string  // 配置名称
	data         []V     // 大部分配置表都是数组配置
	obj          V       // 有些配置表是对象配置
	init         bool    // 是否已经初始化
	mapData      map[K]V // 包装数据 用于快速使用unique获取
	isTypeArray  bool
	isTypeObject bool
}

// GetContainerName 配置容器名称
func (c *ConfigContainer[K, V]) GetContainerName() string {
	return c.name
}

func (c *ConfigContainer[K, V]) initConfigData() {
	if c.init {
		return
	}
	bytes, err := ut.LoadConfig(c.GetContainerName())
	if err != nil {
		panic(fmt.Sprintf("加载配置文件[ %s ]出错:%s", c.GetContainerName(), err.Error()))
	}
	if len(bytes) > 0 {
		if bytes[0] == 91 {
			err = json.Unmarshal(bytes, &c.data)
			c.isTypeArray = true
			if err != nil {
				panic(fmt.Sprintf("配置文件[ %s ]初始化出错:%s", c.GetContainerName(), err.Error()))
			}
			c.mapData = make(map[K]V)
			for _, v := range c.data {
				c.mapData[v.GetUnique()] = v
			}
		}
		if bytes[0] == 123 {
			err = json.Unmarshal(bytes, &c.obj)
			c.isTypeObject = true
			if err != nil {
				panic(fmt.Sprintf("配置文件[ %s ]初始化出错:%s", c.GetContainerName(), err.Error()))
			}
		}
		c.init = true
	}
}

// OnInit
/*
 * @description 初始化
 */
func (c *ConfigContainer[K, V]) OnInit() {
	if c.init {
		if c.isTypeArray {
			for _, v := range c.mapData {
				v.OnInit()
			}
		}
		if c.isTypeObject {
			c.obj.OnInit()
		}
	}
}

// GetObj
/*
 * @description 获取对象数据
 * @return obj
 */
func (c *ConfigContainer[K, V]) GetObj() (obj V) {
	if !c.isTypeObject {
		panic("错误的调用方式，配置container不是一个对象")
	}
	obj = c.obj
	return
}

// GetData
/*
 * @description 获取元数组数据 千万要注意，不要修改配置数据
 * @return *ut.Array[*V]
 */
func (c *ConfigContainer[K, V]) GetData() []V {
	if !c.isTypeArray {
		panic("错误的调用方式，配置container不是一个数组")
	}
	return c.data
}

// GetBeanByUnique
/*
 * @description 根据唯一key获取对象
 * @param unique
 * @return obj
 * @return exists 是否获取成功
 */
func (c *ConfigContainer[K, V]) GetBeanByUnique(unique K) (obj V, exists bool) {
	if c.isTypeArray {
		obj, exists = c.mapData[unique]
	}
	return
}
