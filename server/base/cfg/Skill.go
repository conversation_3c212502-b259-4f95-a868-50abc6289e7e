package cfg

import (
	"fmt"
	"world/common/pbBase/Job"
	"world/common/pbGame/MyDefine"
	"world/common/pbGame/SKILL_TYPE"
)

const ConfigNameSkill = "Skill"

type Skill[K string] struct {
	Id             int                   `json:"id"`
	Level          int                   `json:"level"`
	Type           SKILL_TYPE.SKILL_TYPE `json:"type"`
	ReqJob         Job.Type              `json:"reqJob"`
	ReqLevel       int                   `json:"reqLevel"`
	Anime1         int                   `json:"anime1"`
	Anime2         int                   `json:"anime2"`
	Anime3         int                   `json:"anime3"`
	SkillWeapon    int                   `json:"skillWeapon"`
	SkillAtkType   int                   `json:"skillAtkType"`
	AtkTime        int                   `json:"atk_time"`
	Area           MyDefine.SKILL_AREA   `json:"area"`
	UseMP          int                   `json:"useMp"`
	UseHP          int                   `json:"useHp"`
	Round          int                   `json:"round"`
	Power1         MyDefine.POWER        `json:"power1"`
	Power2         MyDefine.POWER        `json:"power2"`
	Power3         MyDefine.POWER        `json:"power3"`
	PowerValue1    int                   `json:"powerValue1"`
	PowerValue2    int                   `json:"powerValue2"`
	PowerValue3    int                   `json:"powerValue3"`
	StatusBit1     int                   `json:"statusBit1"`
	StatusBit2     int                   `json:"statusBit2"`
	StatusBit3     int                   `json:"statusBit3"`
	FormationType  int                   `json:"formationType"`
	TeamCount      int                   `json:"teamCount"`
	Effect         []*SkillEffect        `json:"-"`
	ConfigMaxLevel int                   `json:"-"`
}

func (s *Skill[K]) GetName() string {
	return ConfigNameSkill
}
func (s *Skill[K]) GetUnique() K {
	u := fmt.Sprintf("%d-%d", s.Id, s.Level)
	return K(u)
}
func (s *Skill[K]) OnInit() {
	s.Effect = make([]*SkillEffect, 0)
	if s.PowerValue1 >= 0 {
		s.Effect = append(s.Effect, &SkillEffect{MyDefine.POWER(s.Power1), s.PowerValue1, s.StatusBit1})
	}
	if s.PowerValue2 >= 0 {
		s.Effect = append(s.Effect, &SkillEffect{MyDefine.POWER(s.Power2), s.PowerValue2, s.StatusBit2})
	}
	if s.PowerValue3 >= 0 {
		s.Effect = append(s.Effect, &SkillEffect{MyDefine.POWER(s.Power3), s.PowerValue3, s.StatusBit3})
	}
}

type SkillEffect struct {
	PowerType  MyDefine.POWER
	PowerValue int
	StatusBit  int
}
