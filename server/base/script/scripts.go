package script

import (
	"encoding/json"
	"flag"
	"fmt"
	"math"
	"os"
	"runtime"
	"strings"
	"time"
	"world/base/enum/ServerType"
	"world/base/env"
	comm "world/common"
	ut "world/utils"

	"github.com/fatih/color"

	"github.com/huyangv/vmqant/conf"
	basegate "github.com/huyangv/vmqant/gate/base"
	"github.com/huyangv/vmqant/log"
	logs "github.com/huyangv/vmqant/log/beego"
	"github.com/huyangv/vmqant/module"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

var ServerTypeStr string // 当前进程服务器类型
var Sid int              // 如果是game的话，sid代表区服
var Mark int             // 服务器标记

// CallBeforeApplicationLaunchedSync 程序启动
func CallBeforeApplicationLaunchedSync() {
	loadSeverConfig()
	SetLogger()
	// 解析启动参数
	flag.Parse()
	// 启动死锁监听
	comm.InitDeadlockListener()
	// 判断启动参数
	if ut.IsEmpty(ServerTypeStr) {
		flag.PrintDefaults()
		os.Exit(-1)
	}

	switch ServerTypeStr {
	case ServerType.Login:
		Mark = ServerType.MarkLogin
	case ServerType.Game:
		Mark = ServerType.MarkGame
	case ServerType.Gate:
		Mark = ServerType.MarkGate
	case ServerType.Mid:
		Mark = ServerType.MarkMid
	case ServerType.Dev:
		Mark |= ServerType.MarkLogin | ServerType.MarkGame | ServerType.MarkGate | ServerType.MarkMid
	}
	log.Info("ServerType:%s", ServerTypeStr)
	if ServerTypeStr == ServerType.Game || ServerTypeStr == ServerType.Login || ServerTypeStr == ServerType.Dev {
		if Sid == -1 {
			fmt.Printf("请传入参数:区服序号,例: -sid 1, 区服必须是数据库中存在一条区服数据的. \n")
			os.Exit(-1)
		}
		log.Info("Sid:%d", Sid)
	}

}

func SetLogger() {
	// 设置日志级别
	level := ut.If(env.IsDebug(), logs.LevelDebug, logs.LevelInformational)
	log.LogBeego().SetLevel(level)
	// 正式环境开启异步日志
	if !env.IsDebug() {
		log.LogBeego().Async()
	}
	// 不再格式化时间，由下面的SetFormatFunc去格式化消息
	logs.FormatTime = ""
	// 写入log file时附带颜色
	logs.WriteLogFileColor = true
	// 是否打印堆栈信息
	withStack := env.IsDebug()

	// 定义颜色输出函数
	alertStyle := color.New(color.BgGreen, color.FgHiWhite).SprintFunc()
	errorStyle := color.New(color.BgRed, color.FgHiWhite).SprintFunc()
	debugStyle := color.New(color.BgMagenta, color.FgHiWhite).SprintFunc()
	warningStyle := color.New(color.BgYellow, color.FgHiWhite).SprintFunc()

	log.LogBeego().SetFormatFunc(func(when time.Time, span *logs.BeegoTraceSpan, logLevel int, msg string, v ...interface{}) (string, error) {
		timestr := when.Format("2006/01/02 - 15:04:05")
		msg = fmt.Sprintf(msg, v...)

		var colorFunc func(...interface{}) string
		switch logLevel {
		case logs.LevelAlert:
			colorFunc = alertStyle
		case logs.LevelError:
			colorFunc = errorStyle
		case logs.LevelDebug:
			colorFunc = debugStyle
		case logs.LevelWarning:
			colorFunc = warningStyle
		default:
			colorFunc = fmt.Sprint
		}

		if withStack {
			_, file, line, _ := runtime.Caller(4)
			i := math.Max(cast.ToFloat64(strings.LastIndex(file, "/"))+1, 0)
			fileInfo := fmt.Sprintf("%s:%d", file[cast.ToInt(i):], line)
			serverInfo := fmt.Sprintf("%s&%d", ServerTypeStr, Sid)
			return colorFunc(fmt.Sprintf("%-22s [%s] %s: %s", fileInfo, serverInfo, timestr, msg)), nil
		}

		serverInfo := fmt.Sprintf("%s&%d", ServerTypeStr, Sid)
		return colorFunc(fmt.Sprintf("[%s] %s: %s", serverInfo, timestr, msg)), nil
	})
}

// AddRPCSerializeOrNot 因为只有gate模块才会注入Session反序列化逻辑，所以这里需要手动为其他模块注入
func AddRPCSerializeOrNot(app module.App) {
	serialize := app.GetRPCSerialize()
	if serialize["gate"] != nil {
		return
	}
	gate := new(basegate.Gate)
	// 这句话是必须的
	gate.App = app
	log.Info("Adding session structures to serialize interfaces.")
	if err := app.AddRPCSerialize("gate", gate); err != nil {
		log.Warning("Adding session structures failed to serialize interfaces %s", err.Error())
	}
}

// LaunchAllOrNotAtDev  1 dev环境需要全部模块启动 2 game和login环境下需要修改setting.ID
func LaunchAllOrNotAtDev(app module.App) {
	if ServerTypeStr == ServerType.Dev {
		mods := app.GetSettings().Module
		setProcessID(mods[ServerType.Gate])
		setProcessID(mods[ServerType.Login])
		setProcessID(mods[ServerType.Game])
		setProcessID(mods[ServerType.Mid])
	}
	// 游戏服节点需要绑定区服id
	setID(ServerType.Game, app)
	//setID(enum.ServerTypeLogin, app)
}

func setID(t string, app module.App) {
	if ServerTypeStr != ServerType.Dev && ServerTypeStr != t {
		return
	}
	mod := app.GetSettings().Module[t]
	if mod == nil {
		log.Error("no %s node found at app.GetSettings.", t)
		os.Exit(-1)
	}
	for _, settings := range mod {
		settings.ID = fmt.Sprintf("%s&%d", settings.ID, Sid)
	}
}

// setProcessID
/*
 * @description 设置进程ID 方便dev环境下统一启动模块
 * @param settings
 */
func setProcessID(settings []*conf.ModuleSettings) {
	if settings == nil {
		return
	}
	mut := len(settings) <= 1
	for i, setting := range settings {
		setting.ProcessID = lo.If(mut, "dev").Else(fmt.Sprintf("dev&%d", i))
	}
}

func loadSeverConfig() {
	bytes, err := ut.LoadJsonByPath("server", "/bin/conf/")
	if err != nil {
		panic(fmt.Sprintf("加载server.json出错:%s", err.Error()))
	}
	err = json.Unmarshal(bytes, &env.ServerConfig)
	if err != nil {
		panic(fmt.Sprintf("解析server.json出错:%s", err.Error()))
	}
}
