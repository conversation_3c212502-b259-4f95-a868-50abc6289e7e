package env

import (
	"github.com/huyangv/vmqant/conf"
	"github.com/spf13/cast"
)

var ServerConfig *conf.Config //server.json

func IsDebug() bool {
	return cast.ToBool(GetSetting()["Debug"])
}

// GetConsulUrl 获取服务器发现ip
func GetConsulUrl() string {
	return GetSetting()["ConsulURL"].(string)
}

func GetNatsUrl() string {
	return GetSetting()["NatsURL"].(string)
}

func GetSeverArea() string {
	return cast.ToString(GetSetting()["SeverArea"])
}

// IsInland 是否国内区域
func IsInland() bool {
	return GetSeverArea() == "inland"
}

func GetSetting() map[string]interface{} {
	return ServerConfig.Settings
}
