package db

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/huyangv/vmqant/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	client   *mongo.Client
	database string
	ok       bool //是否初始化完成
)

// GetCollection 获取db连接对象 千万要记得必须实现MongoTable接口  不然会引起panic
func GetCollection(def *TableDef) *mongo.Collection {
	collection := client.Database(database).Collection(def.Name())
	return collection
}

func CreateIndex(def *TableDef) {
	collection := GetCollection(def)

	cursor, err := collection.Indexes().List(context.Background())
	if err != nil {
		panic(fmt.Sprintf("获取索引列表失败：%s %s", def.name, err.Error()))
	}
	var existingIndexes []bson.M
	if err = cursor.All(context.Background(), &existingIndexes); err != nil {
		panic(fmt.Sprintf("读取索引失败：%s %s", def.name, err.Error()))
	}

	indexModels := make([]mongo.IndexModel, 0)
	// 遍历配置表
	for key, unique := range def.GetIndex() {
		if key == "" {
			continue
		}
		if indexExists(existingIndexes, key) {
			continue
		}
		indexModels = append(indexModels, mongo.IndexModel{
			Keys:    bson.M{key: 1},
			Options: options.Index().SetUnique(unique),
		})
	}
	// 处理新的复合索引
	for _, idx := range def.complexIndex {
		keys := bson.D{}
		for _, f := range idx.Fields {
			keys = append(keys, bson.E{Key: f.Field, Value: f.Sort})
		}
		if complexIndexExists(existingIndexes, keys) {
			continue
		}
		indexModels = append(indexModels, mongo.IndexModel{
			Keys:    keys,
			Options: options.Index().SetUnique(idx.Unique),
		})
	}

	// 创建索引
	if len(indexModels) > 0 {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()
		indexView := collection.Indexes()
		if _, err := indexView.CreateMany(ctx, indexModels); err != nil {
			log.Error("创建索引失败：%s %s", def.name, err.Error())
		}
	}
}

// InitMongoDB 初始化连接
func InitMongoDB(url, dbname string) {
	log.Info("Try to connect mongo db, wait for response.")
	database = dbname
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	opt := options.Client().ApplyURI(url)
	opt.SetLocalThreshold(3 * time.Second)  //只使用与mongo操作耗时小于3秒的
	opt.SetMaxConnIdleTime(5 * time.Second) //指定连接可以保持空闲的最大毫秒数
	opt.SetMaxPoolSize(1024)                //使用最大的连接数
	var err error
	if client, err = mongo.Connect(ctx, opt); err == nil {
		err = client.Ping(context.Background(), nil)
		if err != nil {
			log.Error("Connect mongo db err :%s , System exit.", err.Error())
			os.Exit(-1)
		}
		delayInit()
		for _, table := range tables {
			CreateIndex(table)
		}
		log.Info("mongodb init success! " + url + "[" + database + "]")
	} else if err == mongo.ErrNoDocuments {
		log.Error("mongodb init error! ErrNoDocuments")
	} else {
		log.Error(err.Error())
	}

}

type IndexField struct {
	Field string
	Sort  int // 1 升序, -1 降序
}

type IndexDef struct {
	Fields []IndexField
	Unique bool
}

// 检查单字段索引是否存在
func indexExists(indexes []bson.M, key string) bool {
	for _, index := range indexes {
		if keys, ok := index["key"].(bson.M); ok {
			if len(keys) == 1 {
				for indexKey := range keys {
					if indexKey == key {
						return true
					}
				}
			}
		}
	}
	return false
}

// 检查复合索引是否存在
func complexIndexExists(indexes []bson.M, keys bson.D) bool {
	targetKeys := make(map[string]int)
	for _, k := range keys {
		targetKeys[k.Key] = k.Value.(int)
	}

	for _, index := range indexes {
		if existingKeys, ok := index["key"].(bson.M); ok {
			if len(existingKeys) == len(targetKeys) {
				match := true
				for k, v := range existingKeys {
					if targetKeys[k] != int(v.(int32)) {
						match = false
						break
					}
				}
				if match {
					return true
				}
			}
		}
	}
	return false
}
