package db

import (
	"fmt"
	"world/base/script"
)

// 用户数据表-不分区
var USER = Def("user").Schema(map[string]bool{
	"username": true,
	"password": false,
})

// 区服数据表
var AREA = Def("area").Schema(map[string]bool{
	"id": true,
})

// 角色数据表-分区
var PLAYER *TableDef

// 公共数据-不分区
var COMMON = Def("common").Schema(map[string]bool{
	"key": true,
})

func delayInit() {
	PLAYER = Def(fmt.Sprintf("player_%d", script.Sid)).
		Schema(map[string]bool{
			"id":     false,
			"gameId": true,
			"name":   true,
		})
}
