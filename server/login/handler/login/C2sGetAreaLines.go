package login

import (
	"world/base/mgr"
	"world/base/structs"
	"world/common/pbBase/Response"
	"world/common/pbLogin"
	"world/login/base"
	ut "world/utils"

	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/module"
	"github.com/samber/lo"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// C2sGetAreaLinesMessageHandler 获取区服信息
func C2sGetAreaLinesMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbLogin.C2S_GetAreaLinesMessage) protoreflect.ProtoMessage {
		id := session.GetUserID()
		if ut.IsEmpty(id) {
			return &pbLogin.S2C_GetAreaLinesMessage{
				Code: Response.ErrPleaseLoginFirst,
			}
		}
		user := base.GetUserFromDbById(id)
		list := mgr.Area().GetAreaList()
		if list == nil {
			return &pbLogin.S2C_GetAreaLinesMessage{
				Code: Response.ErrNoAreaLine,
			}
		}
		return &pbLogin.S2C_GetAreaLinesMessage{
			LastId: int32(user.LastSelectServerId),
			List: lo.Map(list, func(t *structs.Area, i int) *pbLogin.AreaLine {
				pb := t.ToPb()
				if user.ActorCnt != nil {
					// 填充角色数量
					cnt, ok := user.ActorCnt[t.Id]
					if ok {
						pb.ActorCount = int32(cnt)
					}
				}
				return pb
			}),
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
