package login

import (
	"github.com/huyangv/vmqant/gate"
	"github.com/huyangv/vmqant/module"
	"google.golang.org/protobuf/reflect/protoreflect"
	"world/common/net_helper"
	"world/common/pbCross"
	"world/common/pbLogin"
	"world/common/router"
	"world/login/loginMgr"
	ut "world/utils"
)

// C2sLoginMessageHandler 登录
func C2sLoginMessageHandler(this module.RPCModule) any {
	/* action-code-start */
	return func(session gate.Session, msg *pbLogin.C2S_LoginMessage) protoreflect.ProtoMessage {
		code, user, token := loginMgr.User().HandleUserLogin(session, msg.Method, msg.Args)
		result := &pbLogin.LoginResult{}
		if user != nil && !ut.IsEmpty(token) {
			result.Id = user.Id
			result.Token = token
			// 账号单点登录 uid相关联的会话全部踢下线
			// 游戏服
			net_helper.CallGameSync(this, router.S2RKickPlayerForceByUidMessage, session, &pbCross.S2R_KickPlayerForceByUidMessage{Uid: user.Id})
			// 登录服
			net_helper.CallLoginSync(this, router.S2RKickSessionByUidMessage, session, &pbCross.S2R_KickSessionByUidMessage{Uid: user.Id})
			// 用户临时存入本节点
			loginMgr.User().AddTmpUser(user)
		}
		return &pbLogin.S2C_LoginMessage{
			Code:   code,
			Result: result,
		}
	}
	/* action-code-end */
}

/* logic-code-start */
/* logic-code-end */
