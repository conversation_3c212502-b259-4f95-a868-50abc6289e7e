package main

import (
	"fmt"
	"io"
	"os"
	"sort"
	"strings"
	ut "world/utils"
)

// 需要拷贝的文件列表 文件在磁盘中必须是.json结尾
var configs = map[string]string{
	"Misc_C":    "string",
	"Ai":        "int",
	"Group":     "int",
	"Item":      "int",
	"LevelExp":  "int",
	"Map":       "int",
	"Mission":   "int",
	"Monster":   "int",
	"Npc":       "int",
	"Identity":  "int",
	"Skill":     "string",
	"SkillShop": "string",
	"Suit":      "int",
	"Pet":       "int",
}

func main() {
	workDir := ut.WorkDir()
	fmt.Println(workDir)
	jsonFilePath := fmt.Sprintf("%s/bin/conf/json", workDir)
	cfgBasePath := fmt.Sprintf("%s/base/cfg/", workDir)
	for name, typ := range configs {
		fileName := fmt.Sprintf("%s.json", name)
		// 拷贝文件
		copyFile(from, jsonFilePath, fileName)
		// 生成配置结构体代码
		genStructCode(name, typ, cfgBasePath)
	}
	genRegister(cfgBasePath)
}

func genRegister(cfgBasePath string) {
	out := "package cfg\n\n"
	out = AppendToString(out, `import (
	"github.com/huyangv/vmqant/log"
	"time"
)`)
	out = AppendToString(out, "\n")
	out = AppendToString(out, "\n")
	_var := ""
	_func := ""
	sortName := make([]string, 0)
	for name, _ := range configs {
		sortName = append(sortName, name)
	}
	sort.Strings(sortName)
	for _, name := range sortName {
		typ := configs[name]
		_var = AppendToString(_var, fmt.Sprintf("var Container%s *ConfigContainer[%s, *%s[%s]]", name, typ, name, typ))
		_var = AppendToString(_var, "\n")
		_func = AppendToString(_func, fmt.Sprintf("\tContainer%s = localNewContainer[%s, *%s[%s]](ConfigName%s)", name, typ, name, typ, name))
		_func = AppendToString(_func, "\n")
	}
	out = AppendToString(out, _var)
	out = AppendToString(out, "\n")
	out = AppendToString(out, fmt.Sprintf("func LoadConfig() {\n"))
	out = AppendToString(out, fmt.Sprintf("\tsTime := time.Now()\n"))
	out = AppendToString(out, fmt.Sprintf("\tlog.Info(\"开始初始化配置文件!\")\n"))
	out = AppendToString(out, _func)
	out = AppendToString(out, "\tlog.Info(\"配置文件初始化完成， during:%fs\", time.Since(sTime).Seconds())\n")
	out = AppendToString(out, "}\n")
	outGo := fmt.Sprintf("%scfg_register.go", cfgBasePath)
	wio, _ := os.OpenFile(outGo, os.O_RDWR|os.O_CREATE, 0755)
	wio.WriteString(out)
	wio.Close()
}

func genStructCode(name, typ, cfgBasePath string) {
	outGo := fmt.Sprintf("%s%s.go", cfgBasePath, name)
	// 文件不存在才生成结构体代码
	_, err := os.Stat(outGo)
	if err != nil && os.IsNotExist(err) {
		out := "package cfg\n\n"
		out = AppendToString(out, fmt.Sprintf("const ConfigName%s = \"%s\"\n\n", name, name))
		out = AppendToString(out, fmt.Sprintf("type %s[K %s] struct {\n", name, typ))
		out = AppendToString(out, "}\n\n")
		nf := strings.ToLower(name[:1])
		// GetName方法
		out = AppendToString(out, fmt.Sprintf("func (%s *%s[K]) GetName() string {\n", nf, name))
		out = AppendToString(out, fmt.Sprintf("\treturn ConfigName%s", name))
		out = AppendToString(out, "\n}\n")
		// GetUnique方法
		out = AppendToString(out, fmt.Sprintf("func (%s *%s[K]) GetUnique() K {\n", nf, name))
		out = AppendToString(out, `	panic("implement me")`)
		out = AppendToString(out, "\n}\n")
		// OnInit方法
		out = AppendToString(out, fmt.Sprintf("func (%s *%s[K]) OnInit() {\n", nf, name))
		out = AppendToString(out, "}\n")

		wio, _ := os.OpenFile(outGo, os.O_RDWR|os.O_CREATE, 0755)
		wio.WriteString(out)
		wio.Close()
	}
}

func copyFile(source, target, fileName string) {
	// 源文件
	_dist, err := os.OpenFile(fmt.Sprintf("%s/%s", source, fileName), os.O_RDONLY, 0755)
	if err != nil {
		panic(err)
	}
	// 目标文件
	_target, err := os.OpenFile(fmt.Sprintf("%s/%s", target, fileName), os.O_TRUNC|os.O_CREATE|os.O_RDWR, 0755)
	if err != nil {
		panic(err)
	}
	_, err = io.Copy(_target, _dist)
	if err != nil {
		panic(err)
	}
}

func AppendToString(self, target string) string {
	return fmt.Sprintf("%s%s", self, target)
}
